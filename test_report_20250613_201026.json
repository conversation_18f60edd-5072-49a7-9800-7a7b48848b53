{"timestamp": "2025-06-13T20:10:26.958117", "summary": {"passed": 15, "failed": 3, "warnings": 0, "errors": 0, "total_execution_time": 20.272301197052002}, "results": [{"test_name": "page_structure", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.03694295883178711, "recommendations": [], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.019917011260986328, "recommendations": [], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "PASSED", "severity": "LOW", "details": "No forms found", "execution_time": 0.0014138221740722656, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.040667057037353516, "recommendations": ["Consider adding breadcrumb navigation for deep site structures", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 1.6008319854736328, "recommendations": [], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.010149240493774414, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.1729879379272461, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.016173124313354492, "recommendations": ["Consider adding structured data for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.0031909942626953125, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.13472390174865723, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 5.672944068908691, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "PASSED", "severity": "LOW", "details": "Tested 0 malicious inputs | Issues: 0", "execution_time": 0.0206301212310791, "recommendations": [], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 3.4613218307495117, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 2.726660966873169, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 2.671286106109619, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.458743095397949, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "PASSED", "severity": "LOW", "details": "Tested 3 error handling cases | Issues: 0", "execution_time": 0.04755091667175293, "recommendations": [], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 4 | Vulnerabilities: 3 | Recommendations: 10 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Inline scripts detected - potential XSS risk", "execution_time": 0.*****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test signup with duplicate email", "Test email validation", "Test password strength requirements", "Test input sanitization", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250613_201026.png"}]}