{"timestamp": "2025-06-13T00:35:55.516664", "summary": {"passed": 9, "failed": 9, "warnings": 0, "errors": 0, "total_execution_time": 17.29141139984131}, "results": [{"test_name": "page_structure", "status": "FAILED", "severity": "MEDIUM", "details": "Missing <header> element; Missing <main> element; Page title missing or too short; Missing meta description; No H1 heading found", "execution_time": 0.04187583923339844, "recommendations": ["Add semantic <header> element", "Add semantic <main> element for primary content", "Add descriptive page title (50-60 characters)", "Add meta description for SEO", "Add exactly one H1 heading per page"], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Insufficient ARIA landmarks", "execution_time": 0.021212100982666016, "recommendations": ["Add ARIA landmarks for better screen reader navigation"], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "PASSED", "severity": "LOW", "details": "No forms found", "execution_time": 0.0019121170043945312, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Limited navigation options", "execution_time": 0.027065277099609375, "recommendations": ["Provide adequate navigation options", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5614831447601318, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.008014202117919922, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.034973859786987305, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "FAILED", "severity": "LOW", "details": "Missing page title; Limited internal linking", "execution_time": 0.004703998565673828, "recommendations": ["Add descriptive page title", "Consider adding structured data for better SEO", "Add more internal links for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.0030221939086914062, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.18871617317199707, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 4.082623243331909, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "PASSED", "severity": "LOW", "details": "Tested 0 malicious inputs | Issues: 0", "execution_time": 0.015276193618774414, "recommendations": [], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 3.000056028366089, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 2.093579053878784, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 2.616708755493164, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.442456007003784, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "FAILED", "severity": "MEDIUM", "details": "Tested 3 error handling cases | Issues: 1", "execution_time": 0.04846811294555664, "recommendations": ["Implement proper 404 error pages", "Add path traversal protection", "Implement error boundaries", "Add proper error logging"], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 2 | Vulnerabilities: 1 | Recommendations: 6 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability", "execution_time": 0.*****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250613_003555.png"}]}