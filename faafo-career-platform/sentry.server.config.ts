// This file configures the initialization of Sentry on the server side.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

// Only initialize Sentry if DSN is configured
if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    environment: process.env.NODE_ENV,

    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: process.env.NODE_ENV === 'development',

    // Enhanced error filtering
    beforeSend(event: any, hint: any) {
      // Filter out development-only errors in production
      if (process.env.NODE_ENV === 'production') {
        // Skip certain error types that are not actionable
        if (event.exception?.values?.[0]?.type === 'ChunkLoadError') {
          return null;
        }

        // Remove sensitive data
        if (event.request?.headers) {
          delete event.request.headers.authorization;
          delete event.request.headers.cookie;
        }
      }

      return event;
    },

    // Enhanced performance monitoring
    integrations: [
      Sentry.httpIntegration(),
      Sentry.prismaIntegration(),
    ],

    // Add release tracking
    release: process.env.VERCEL_GIT_COMMIT_SHA || 'development',
  });
}
