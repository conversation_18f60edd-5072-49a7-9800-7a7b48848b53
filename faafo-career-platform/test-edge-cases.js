#!/usr/bin/env node

/**
 * Edge case and performance testing for Resume Builder
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 EDGE CASE & PERFORMANCE TESTING\n');
console.log('='.repeat(50));

let totalTests = 0;
let passedTests = 0;
let issues = [];

function runTest(testName, testFunction) {
  totalTests++;
  console.log(`\n🔍 ${testName}`);
  
  try {
    const result = testFunction();
    if (result.success) {
      passedTests++;
      console.log(`✅ ${result.message}`);
    } else {
      console.log(`❌ ${result.message}`);
      issues.push(`${testName}: ${result.message}`);
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
    issues.push(`${testName}: ${error.message}`);
  }
}

// Test 1: SQL Injection Prevention
runTest('SQL Injection Prevention', () => {
  const apiPath = path.join(__dirname, 'src/app/api/resume-builder/route.ts');
  const content = fs.readFileSync(apiPath, 'utf8');
  
  // Check for Prisma usage (prevents SQL injection)
  if (!content.includes('prisma.')) {
    return { success: false, message: 'No Prisma usage found - potential SQL injection risk' };
  }
  
  // Check for raw SQL queries (dangerous)
  if (content.includes('$queryRaw') || content.includes('$executeRaw')) {
    return { success: false, message: 'Raw SQL queries found - review for injection risks' };
  }
  
  // Check for proper parameterization
  if (content.includes('prisma.resume.findMany') && content.includes('where:')) {
    return { success: true, message: 'Proper Prisma usage with parameterized queries' };
  }
  
  return { success: false, message: 'Could not verify SQL injection prevention' };
});

// Test 2: XSS Prevention
runTest('XSS Prevention', () => {
  const frontendFiles = [
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let xssIssues = [];
  
  frontendFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for dangerous patterns
      if (content.includes('dangerouslySetInnerHTML')) {
        xssIssues.push(`${file}: Uses dangerouslySetInnerHTML`);
      }
      
      // Check for proper escaping in React (React auto-escapes by default)
      if (content.includes('{') && content.includes('}') && !content.includes('dangerouslySetInnerHTML')) {
        // Good - React JSX auto-escapes
      }
    }
  });
  
  if (xssIssues.length > 0) {
    return { success: false, message: `XSS risks: ${xssIssues.join(', ')}` };
  }
  
  return { success: true, message: 'No XSS vulnerabilities found - React auto-escaping in use' };
});

// Test 3: Input Validation Edge Cases
runTest('Input Validation Edge Cases', () => {
  const apiPath = path.join(__dirname, 'src/app/api/resume-builder/route.ts');
  const content = fs.readFileSync(apiPath, 'utf8');
  
  const validationChecks = [
    { pattern: '\\.trim\\(\\)', name: 'String trimming' },
    { pattern: '\\.min\\(', name: 'Minimum length validation' },
    { pattern: '\\.max\\(', name: 'Maximum length validation' },
    { pattern: '\\.email\\(', name: 'Email validation' },
    { pattern: '\\.url\\(', name: 'URL validation' },
    { pattern: 'safeParse', name: 'Safe parsing' }
  ];
  
  const missingValidations = validationChecks.filter(check => 
    !new RegExp(check.pattern, 'i').test(content)
  );
  
  if (missingValidations.length > 2) {
    return { 
      success: false, 
      message: `Missing validations: ${missingValidations.map(v => v.name).join(', ')}` 
    };
  }
  
  return { success: true, message: `Comprehensive validation (${validationChecks.length - missingValidations.length}/${validationChecks.length} checks)` };
});

// Test 4: Memory Leak Prevention
runTest('Memory Leak Prevention', () => {
  const frontendFiles = [
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let memoryIssues = [];
  
  frontendFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for useEffect cleanup
      const useEffectCount = (content.match(/useEffect/g) || []).length;
      const cleanupCount = (content.match(/return.*\(\)/g) || []).length;
      
      // Check for event listener cleanup
      if (content.includes('addEventListener') && !content.includes('removeEventListener')) {
        memoryIssues.push(`${file}: Event listeners without cleanup`);
      }
      
      // Check for timer cleanup
      if ((content.includes('setTimeout') || content.includes('setInterval')) && 
          !content.includes('clear')) {
        memoryIssues.push(`${file}: Timers without cleanup`);
      }
    }
  });
  
  if (memoryIssues.length > 0) {
    return { success: false, message: `Memory leak risks: ${memoryIssues.join(', ')}` };
  }
  
  return { success: true, message: 'No obvious memory leak patterns found' };
});

// Test 5: Performance Optimization
runTest('Performance Optimization', () => {
  const frontendFiles = [
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let performanceScore = 0;
  let totalChecks = 0;
  
  frontendFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      const optimizations = [
        { pattern: 'useMemo', name: 'Memoization' },
        { pattern: 'useCallback', name: 'Callback memoization' },
        { pattern: 'React\\.memo', name: 'Component memoization' },
        { pattern: 'lazy\\(', name: 'Lazy loading' },
        { pattern: 'Suspense', name: 'Suspense boundaries' }
      ];
      
      optimizations.forEach(opt => {
        totalChecks++;
        if (new RegExp(opt.pattern, 'i').test(content)) {
          performanceScore++;
        }
      });
    }
  });
  
  // Check API pagination
  const apiPath = path.join(__dirname, 'src/app/api/resume-builder/route.ts');
  if (fs.existsSync(apiPath)) {
    const apiContent = fs.readFileSync(apiPath, 'utf8');
    totalChecks++;
    if (apiContent.includes('pagination') || apiContent.includes('limit')) {
      performanceScore++;
    }
  }
  
  const perfPercentage = (performanceScore / totalChecks) * 100;
  
  if (perfPercentage < 30) {
    return { 
      success: false, 
      message: `Low performance optimization: ${perfPercentage.toFixed(1)}%` 
    };
  }
  
  return { 
    success: true, 
    message: `Good performance patterns: ${perfPercentage.toFixed(1)}% (${performanceScore}/${totalChecks})` 
  };
});

// Test 6: Error Boundary Implementation
runTest('Error Boundary Coverage', () => {
  const frontendFiles = [
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let errorBoundaryScore = 0;
  let totalErrorChecks = 0;
  
  frontendFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      const errorHandling = [
        { pattern: 'try.*catch', name: 'Try-catch blocks' },
        { pattern: 'error.*state', name: 'Error state management' },
        { pattern: 'toast\\.error', name: 'User error feedback' },
        { pattern: 'console\\.error', name: 'Error logging' },
        { pattern: 'if.*error', name: 'Error condition handling' }
      ];
      
      errorHandling.forEach(eh => {
        totalErrorChecks++;
        if (new RegExp(eh.pattern, 'i').test(content)) {
          errorBoundaryScore++;
        }
      });
    }
  });
  
  const errorCoverage = (errorBoundaryScore / totalErrorChecks) * 100;
  
  if (errorCoverage < 60) {
    return { 
      success: false, 
      message: `Insufficient error handling: ${errorCoverage.toFixed(1)}%` 
    };
  }
  
  return { 
    success: true, 
    message: `Good error handling: ${errorCoverage.toFixed(1)}% (${errorBoundaryScore}/${totalErrorChecks})` 
  };
});

// Test 7: Accessibility Implementation
runTest('Accessibility Features', () => {
  const frontendFiles = [
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let a11yScore = 0;
  let totalA11yChecks = 0;
  
  frontendFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      const a11yFeatures = [
        { pattern: 'aria-', name: 'ARIA attributes' },
        { pattern: 'htmlFor=', name: 'Label associations' },
        { pattern: 'title=', name: 'Tooltips' },
        { pattern: 'alt=', name: 'Alt text' },
        { pattern: 'role=', name: 'ARIA roles' },
        { pattern: 'tabIndex', name: 'Tab navigation' }
      ];
      
      a11yFeatures.forEach(a11y => {
        totalA11yChecks++;
        if (new RegExp(a11y.pattern, 'i').test(content)) {
          a11yScore++;
        }
      });
    }
  });
  
  const a11yPercentage = (a11yScore / totalA11yChecks) * 100;
  
  if (a11yPercentage < 40) {
    return { 
      success: false, 
      message: `Low accessibility: ${a11yPercentage.toFixed(1)}%` 
    };
  }
  
  return { 
    success: true, 
    message: `Good accessibility: ${a11yPercentage.toFixed(1)}% (${a11yScore}/${totalA11yChecks})` 
  };
});

// Test 8: Code Quality Metrics
runTest('Code Quality Assessment', () => {
  const allFiles = [
    'src/app/api/resume-builder/route.ts',
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let qualityIssues = [];
  
  allFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      // Check file length
      if (lines.length > 500) {
        qualityIssues.push(`${file}: File too long (${lines.length} lines)`);
      }
      
      // Check for very long functions
      const functionMatches = content.match(/function.*\{[\s\S]*?\n\}/g) || [];
      functionMatches.forEach((func, index) => {
        const funcLines = func.split('\n').length;
        if (funcLines > 50) {
          qualityIssues.push(`${file}: Function ${index + 1} too long (${funcLines} lines)`);
        }
      });
      
      // Check for TODO/FIXME comments
      if (content.includes('TODO') || content.includes('FIXME')) {
        qualityIssues.push(`${file}: Contains TODO/FIXME comments`);
      }
    }
  });
  
  if (qualityIssues.length > 3) {
    return { 
      success: false, 
      message: `Code quality issues: ${qualityIssues.slice(0, 3).join(', ')}...` 
    };
  }
  
  return { 
    success: true, 
    message: `Good code quality (${qualityIssues.length} minor issues)` 
  };
});

// Summary
console.log('\n' + '='.repeat(50));
console.log('🏁 EDGE CASE TEST SUMMARY');
console.log('='.repeat(50));
console.log(`✅ Passed: ${passedTests}/${totalTests}`);
console.log(`📊 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (issues.length > 0) {
  console.log('\n⚠️  ISSUES FOUND:');
  issues.forEach(issue => console.log(`   ❌ ${issue}`));
}

// Final Assessment
const successRate = (passedTests / totalTests) * 100;

console.log('\n📋 SECURITY & PERFORMANCE ASSESSMENT:');
if (successRate >= 90) {
  console.log('🎉 EXCELLENT: Production-ready with enterprise-grade security');
} else if (successRate >= 75) {
  console.log('✅ GOOD: Ready for production with minor security improvements');
} else if (successRate >= 60) {
  console.log('⚠️  FAIR: Needs security and performance improvements');
} else {
  console.log('❌ POOR: Significant security and performance issues');
}

console.log('\n' + '='.repeat(50));
console.log('Edge case testing completed at:', new Date().toISOString());
