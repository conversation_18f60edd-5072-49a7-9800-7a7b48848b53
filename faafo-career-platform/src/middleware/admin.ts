import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions, isUserAdmin, requireAdmin } from '@/lib/auth';
import { Session } from 'next-auth';

/**
 * Admin middleware for protecting admin-only routes
 */
export async function withAdminAuth(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has admin privileges
    const isAdmin = await isUserAdmin(session.user.id);
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // User is authenticated and has admin privileges, proceed with handler
    return handler(request);
  } catch (error) {
    console.error('Admin middleware error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Check admin status for a session
 */
export async function checkAdminStatus(session: Session | null): Promise<{
  isAdmin: boolean;
  role: string | null;
  error?: string;
}> {
  if (!session?.user?.id) {
    return {
      isAdmin: false,
      role: null,
      error: 'Not authenticated'
    };
  }

  try {
    const isAdmin = await isUserAdmin(session.user.id);
    const role = isAdmin ? 'ADMIN' : 'USER';
    
    return {
      isAdmin,
      role,
    };
  } catch (error) {
    console.error('Error checking admin status:', error);
    return {
      isAdmin: false,
      role: null,
      error: 'Failed to check admin status'
    };
  }
}

/**
 * Require admin access - throws error if not admin
 */
export async function requireAdminAccess(session: Session | null): Promise<void> {
  await requireAdmin(session);
}

/**
 * Admin route wrapper for API routes
 */
export function adminRoute(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    return withAdminAuth(request, handler);
  };
}

/**
 * Check if current user can perform admin actions
 */
export async function canPerformAdminAction(
  session: Session | null,
  action: string
): Promise<boolean> {
  if (!session?.user?.id) {
    return false;
  }

  try {
    const isAdmin = await isUserAdmin(session.user.id);
    
    // For now, all admin actions require admin role
    // In the future, this could be expanded for granular permissions
    return isAdmin;
  } catch (error) {
    console.error(`Error checking admin action permission for ${action}:`, error);
    return false;
  }
}

export default {
  withAdminAuth,
  checkAdminStatus,
  requireAdminAccess,
  adminRoute,
  canPerformAdminAction,
};
