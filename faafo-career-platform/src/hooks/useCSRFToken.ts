'use client';

import { useState, useEffect } from 'react';

interface CSRFTokenResponse {
  csrfToken: string;
  timestamp: number;
}

export function useCSRFToken() {
  const [csrfToken, setCSRFToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCSRFToken = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/csrf-token', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch CSRF token: ${response.status}`);
      }

      const data: CSRFTokenResponse = await response.json();
      setCSRFToken(data.csrfToken);
    } catch (err) {
      console.error('Error fetching CSRF token:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch CSRF token');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCSRFToken();
  }, []);

  // Refresh token function for manual refresh
  const refreshToken = () => {
    fetchCSRFToken();
  };

  // Get headers with CSRF token for API requests
  const getCSRFHeaders = (): Record<string, string> => {
    if (!csrfToken) {
      return {
        'Content-Type': 'application/json',
      };
    }

    return {
      'X-CSRF-Token': csrfToken,
      'Content-Type': 'application/json',
    };
  };

  // Enhanced fetch function with CSRF protection
  const csrfFetch = async (url: string, options: RequestInit = {}) => {
    if (!csrfToken) {
      throw new Error('CSRF token not available');
    }

    const headers: Record<string, string> = {
      ...getCSRFHeaders(),
      ...(options.headers as Record<string, string> || {}),
    };

    return fetch(url, {
      ...options,
      headers,
      credentials: 'same-origin',
    });
  };

  return {
    csrfToken,
    isLoading,
    error,
    refreshToken,
    getCSRFHeaders,
    csrfFetch,
  };
}
