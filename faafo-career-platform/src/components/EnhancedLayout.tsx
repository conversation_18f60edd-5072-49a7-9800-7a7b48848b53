'use client';

import React from 'react';
import { EnhancedErrorBoundary } from '@/components/EnhancedErrorBoundary';
import { PerformanceMonitor } from '@/components/PerformanceMonitor';

interface EnhancedLayoutProps {
  children: React.ReactNode;
}

export const EnhancedLayout: React.FC<EnhancedLayoutProps> = ({ children }) => {
  const handlePerformanceMetrics = (metrics: any) => {
    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚀 Performance Metrics Collected');
      console.table(metrics);
      console.groupEnd();
    }
  };

  const handleError = (error: Error, errorInfo: any) => {
    // Log error details
    console.error('Enhanced Error Boundary caught error:', error);
    console.error('Error Info:', errorInfo);

    // Report to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // This would typically send to your error monitoring service
      fetch('/api/error-reporting', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          error: {
            message: error.message,
            stack: error.stack,
            name: error.name
          },
          errorInfo,
          timestamp: Date.now(),
          userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Unknown',
          url: typeof window !== 'undefined' ? window.location.href : 'Unknown'
        })
      }).catch(reportError => {
        console.error('Failed to report error:', reportError);
      });
    }
  };

  return (
    <>
      {/* Performance monitoring */}
      <PerformanceMonitor
        onMetricsCollected={handlePerformanceMetrics}
        enableConsoleLogging={process.env.NODE_ENV === 'development'}
        enableReporting={process.env.NODE_ENV === 'production'}
      />
      
      {/* Enhanced error boundary */}
      <EnhancedErrorBoundary
        onError={handleError}
        showDetails={process.env.NODE_ENV === 'development'}
      >
        {children}
      </EnhancedErrorBoundary>
    </>
  );
};

export default EnhancedLayout;
