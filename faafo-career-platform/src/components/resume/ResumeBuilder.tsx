'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Save, 
  Eye, 
  Sparkles,
  Plus,
  Trash2,
  User,
  Briefcase,
  GraduationCap,
  Code,
  FolderOpen,
  Download,
  Share2
} from 'lucide-react';

interface ResumeBuilderProps {
  resumeId?: string;
  initialData?: any;
  onSave?: (data: any) => void;
  onPreview?: () => void;
  onOptimize?: () => void;
}

interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  location: string;
  website: string;
  linkedin: string;
  summary: string;
}

interface Experience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate: string;
  current: boolean;
  description: string;
  achievements: string[];
}

interface Education {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate: string;
  gpa: string;
  honors: string[];
}

interface SkillCategory {
  category: string;
  items: string[];
}

interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  url: string;
  repository: string;
}

export default function ResumeBuilder({ 
  resumeId, 
  initialData, 
  onSave, 
  onPreview, 
  onOptimize 
}: ResumeBuilderProps) {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('personal');
  const [hasChanges, setHasChanges] = useState(false);

  // Form state
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    location: '',
    website: '',
    linkedin: '',
    summary: '',
  });
  const [experience, setExperience] = useState<Experience[]>([]);
  const [education, setEducation] = useState<Education[]>([]);
  const [skills, setSkills] = useState<SkillCategory[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);

  useEffect(() => {
    if (initialData) {
      setPersonalInfo(initialData.personalInfo || personalInfo);
      setExperience(initialData.experience || []);
      setEducation(initialData.education || []);
      setSkills(initialData.skills || []);
      setProjects(initialData.projects || []);
    }
  }, [initialData]);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const markAsChanged = () => {
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      
      const resumeData = {
        personalInfo,
        experience,
        education,
        skills,
        projects,
      };

      if (onSave) {
        await onSave(resumeData);
        setHasChanges(false);
        alert('Resume saved successfully!');
      }
    } catch (error) {
      console.error('Error saving resume:', error);
      alert('Failed to save resume');
    } finally {
      setLoading(false);
    }
  };

  const handleOptimize = async () => {
    if (onOptimize) {
      await onOptimize();
    }
  };

  const addExperience = () => {
    setExperience([...experience, {
      id: generateId(),
      company: '',
      position: '',
      startDate: '',
      endDate: '',
      current: false,
      description: '',
      achievements: [],
    }]);
    markAsChanged();
  };

  const removeExperience = (id: string) => {
    setExperience(experience.filter(exp => exp.id !== id));
    markAsChanged();
  };

  const updateExperience = (id: string, field: keyof Experience, value: any) => {
    setExperience(experience.map(exp => 
      exp.id === id ? { ...exp, [field]: value } : exp
    ));
    markAsChanged();
  };

  const addEducation = () => {
    setEducation([...education, {
      id: generateId(),
      institution: '',
      degree: '',
      field: '',
      startDate: '',
      endDate: '',
      gpa: '',
      honors: [],
    }]);
    markAsChanged();
  };

  const removeEducation = (id: string) => {
    setEducation(education.filter(edu => edu.id !== id));
    markAsChanged();
  };

  const updateEducation = (id: string, field: keyof Education, value: any) => {
    setEducation(education.map(edu => 
      edu.id === id ? { ...edu, [field]: value } : edu
    ));
    markAsChanged();
  };

  const addSkillCategory = () => {
    setSkills([...skills, {
      category: '',
      items: [],
    }]);
    markAsChanged();
  };

  const removeSkillCategory = (index: number) => {
    setSkills(skills.filter((_, i) => i !== index));
    markAsChanged();
  };

  const updateSkillCategory = (index: number, field: keyof SkillCategory, value: any) => {
    setSkills(skills.map((skill, i) => 
      i === index ? { ...skill, [field]: value } : skill
    ));
    markAsChanged();
  };

  const addProject = () => {
    setProjects([...projects, {
      id: generateId(),
      name: '',
      description: '',
      technologies: [],
      url: '',
      repository: '',
    }]);
    markAsChanged();
  };

  const removeProject = (id: string) => {
    setProjects(projects.filter(proj => proj.id !== id));
    markAsChanged();
  };

  const updateProject = (id: string, field: keyof Project, value: any) => {
    setProjects(projects.map(proj => 
      proj.id === id ? { ...proj, [field]: value } : proj
    ));
    markAsChanged();
  };

  return (
    <div className="w-full">
      {/* Action Bar */}
      <div className="flex justify-between items-center mb-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center gap-2">
          {hasChanges && (
            <Badge variant="secondary" className="text-orange-600">
              Unsaved Changes
            </Badge>
          )}
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleSave}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {loading ? 'Saving...' : 'Save'}
          </Button>
          <Button
            variant="outline"
            onClick={onPreview}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            Preview
          </Button>
          <Button
            variant="outline"
            onClick={handleOptimize}
            className="flex items-center gap-2"
          >
            <Sparkles className="h-4 w-4" />
            AI Optimize
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
          >
            <Share2 className="h-4 w-4" />
            Share
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Personal
          </TabsTrigger>
          <TabsTrigger value="experience" className="flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Experience
          </TabsTrigger>
          <TabsTrigger value="education" className="flex items-center gap-2">
            <GraduationCap className="h-4 w-4" />
            Education
          </TabsTrigger>
          <TabsTrigger value="skills" className="flex items-center gap-2">
            <Code className="h-4 w-4" />
            Skills
          </TabsTrigger>
          <TabsTrigger value="projects" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Projects
          </TabsTrigger>
        </TabsList>

        <TabsContent value="personal" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Basic information that will appear at the top of your resume
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={personalInfo.firstName}
                    onChange={(e) => {
                      setPersonalInfo({...personalInfo, firstName: e.target.value});
                      markAsChanged();
                    }}
                    placeholder="John"
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={personalInfo.lastName}
                    onChange={(e) => {
                      setPersonalInfo({...personalInfo, lastName: e.target.value});
                      markAsChanged();
                    }}
                    placeholder="Doe"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
