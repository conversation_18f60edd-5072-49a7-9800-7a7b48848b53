'use client';

import { useState } from 'react';
import { Loader2, Save, Trash2, Plus } from 'lucide-react';

interface Skill {
  id: string;
  skillName: string;
  category: string;
  proficiency?: number;
  sortOrder: number;
}

interface SkillFormProps {
  resumeId: string;
  skill?: Skill;
  onSuccess: (skill: any) => void;
  onCancel: () => void;
}

export default function SkillForm({ resumeId, skill, onSuccess, onCancel }: SkillFormProps) {
  const [formData, setFormData] = useState({
    category: skill?.category || '',
    skillName: skill?.skillName || '',
    proficiency: skill?.proficiency || 3
  });
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const skillCategories = [
    'Programming Languages',
    'Frameworks & Libraries',
    'Databases',
    'Tools & Technologies',
    'Cloud Platforms',
    'Operating Systems',
    'Soft Skills',
    'Languages',
    'Other'
  ];

  const proficiencyLevels = [
    { value: 1, label: 'Beginner', color: 'bg-red-500' },
    { value: 2, label: 'Intermediate', color: 'bg-yellow-500' },
    { value: 3, label: 'Advanced', color: 'bg-blue-500' },
    { value: 4, label: 'Expert', color: 'bg-green-500' },
    { value: 5, label: 'Master', color: 'bg-purple-500' }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      setErrors({});

      const response = await fetch(`/api/resume-builder/${resumeId}/skills`, {
        method: skill ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(skill ? {...formData, id: skill.id} : formData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        onSuccess(data.data);
      } else {
        if (data.details && Array.isArray(data.details)) {
          const serverErrors: Record<string, string> = {};
          data.details.forEach((error: any) => {
            if (error.path && error.path.length > 0) {
              serverErrors[error.path[0]] = error.message;
            }
          });
          setErrors(serverErrors);
        } else {
          setErrors({ general: data.error || 'Failed to create skill' });
        }
      }
    } catch (error) {
      console.error('Error creating skill:', error);
      setErrors({ general: 'Network error. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">skill ? 'Edit Skill' : 'Add Skill'</h3>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {errors.general && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-700 text-sm">{errors.general}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category *
            </label>
            <select
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="">Select a category</option>
              {skillCategories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-600">{errors.category}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Skill Name *
            </label>
            <input
              type="text"
              value={formData.skillName}
              onChange={(e) => handleInputChange('skillName', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="JavaScript, React, Python, etc."
            />
            {errors.skillName && (
              <p className="mt-1 text-sm text-red-600">{errors.skillName}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Proficiency Level
          </label>
          <div className="grid grid-cols-5 gap-2">
            {proficiencyLevels.map((level) => (
              <button
                key={level.value}
                type="button"
                onClick={() => handleInputChange('proficiency', level.value)}
                className={`p-3 rounded-lg border-2 transition-all text-center ${
                  formData.proficiency === level.value
                    ? 'border-purple-500 bg-purple-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className={`w-4 h-4 rounded-full ${level.color} mx-auto mb-1`}></div>
                <div className="text-xs font-medium text-gray-900">{level.label}</div>
              </button>
            ))}
          </div>
        </div>

        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            disabled={saving}
            className="bg-purple-600 text-white px-6 py-2 rounded-xl hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center gap-2"
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                skill ? 'Update Skill' : 'Save Skill'
              </>
            )}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="bg-gray-100 text-gray-700 px-6 py-2 rounded-xl hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}
