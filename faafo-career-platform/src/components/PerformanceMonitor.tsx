'use client';

import React, { useEffect, useRef, useState } from 'react';

interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  timeToInteractive: number;
  memoryUsage?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

interface PerformanceMonitorProps {
  onMetricsCollected?: (metrics: PerformanceMetrics) => void;
  enableConsoleLogging?: boolean;
  enableReporting?: boolean;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  onMetricsCollected,
  enableConsoleLogging = false,
  enableReporting = true
}) => {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({});
  const metricsCollected = useRef(false);

  useEffect(() => {
    if (typeof window === 'undefined' || metricsCollected.current) return;

    const collectMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      const newMetrics: Partial<PerformanceMetrics> = {};

      // Basic timing metrics
      if (navigation) {
        newMetrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
        newMetrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
      }

      // Paint metrics
      const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
      if (fcp) {
        newMetrics.firstContentfulPaint = fcp.startTime;
      }

      // Web Vitals
      collectWebVitals(newMetrics);

      // Memory usage (if available)
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        newMetrics.memoryUsage = {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit
        };
      }

      setMetrics(newMetrics);
      
      if (enableConsoleLogging) {
        console.group('🚀 Performance Metrics');
        console.table(newMetrics);
        console.groupEnd();
      }

      if (onMetricsCollected && Object.keys(newMetrics).length > 0) {
        onMetricsCollected(newMetrics as PerformanceMetrics);
      }

      if (enableReporting) {
        reportMetrics(newMetrics);
      }

      metricsCollected.current = true;
    };

    const collectWebVitals = (metrics: Partial<PerformanceMetrics>) => {
      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            metrics.largestContentfulPaint = lastEntry.startTime;
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              metrics.firstInputDelay = entry.processingStart - entry.startTime;
            });
          });
          fidObserver.observe({ entryTypes: ['first-input'] });

          // Cumulative Layout Shift
          let clsValue = 0;
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: any) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            });
            metrics.cumulativeLayoutShift = clsValue;
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // Clean up observers after 10 seconds
          setTimeout(() => {
            lcpObserver.disconnect();
            fidObserver.disconnect();
            clsObserver.disconnect();
          }, 10000);
        } catch (error) {
          console.warn('Performance Observer not fully supported:', error);
        }
      }
    };

    const reportMetrics = (metrics: Partial<PerformanceMetrics>) => {
      // Report to analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('event', 'page_performance', {
          page_load_time: metrics.pageLoadTime,
          first_contentful_paint: metrics.firstContentfulPaint,
          largest_contentful_paint: metrics.largestContentfulPaint,
          cumulative_layout_shift: metrics.cumulativeLayoutShift,
          first_input_delay: metrics.firstInputDelay
        });
      }

      // Report to custom endpoint
      if (enableReporting) {
        fetch('/api/performance-metrics', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            metrics,
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            url: window.location.href
          })
        }).catch(error => {
          console.warn('Failed to report performance metrics:', error);
        });
      }
    };

    // Collect metrics when page is fully loaded
    if (document.readyState === 'complete') {
      setTimeout(collectMetrics, 100);
    } else {
      window.addEventListener('load', () => {
        setTimeout(collectMetrics, 100);
      });
    }

    // Collect additional metrics on page visibility change
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && !metricsCollected.current) {
        collectMetrics();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [onMetricsCollected, enableConsoleLogging, enableReporting]);

  // Performance budget warnings
  useEffect(() => {
    if (!metrics.pageLoadTime) return;

    const warnings = [];

    if (metrics.pageLoadTime && metrics.pageLoadTime > 3000) {
      warnings.push('Page load time exceeds 3 seconds');
    }

    if (metrics.firstContentfulPaint && metrics.firstContentfulPaint > 1800) {
      warnings.push('First Contentful Paint exceeds 1.8 seconds');
    }

    if (metrics.largestContentfulPaint && metrics.largestContentfulPaint > 2500) {
      warnings.push('Largest Contentful Paint exceeds 2.5 seconds');
    }

    if (metrics.cumulativeLayoutShift && metrics.cumulativeLayoutShift > 0.1) {
      warnings.push('Cumulative Layout Shift exceeds 0.1');
    }

    if (metrics.firstInputDelay && metrics.firstInputDelay > 100) {
      warnings.push('First Input Delay exceeds 100ms');
    }

    if (warnings.length > 0 && enableConsoleLogging) {
      console.group('⚠️ Performance Budget Warnings');
      warnings.forEach(warning => console.warn(warning));
      console.groupEnd();
    }
  }, [metrics, enableConsoleLogging]);

  return null; // This component doesn't render anything
};

// Hook for accessing performance metrics
export const usePerformanceMetrics = () => {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({});

  const collectCurrentMetrics = () => {
    if (typeof window === 'undefined') return;

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    const currentMetrics: Partial<PerformanceMetrics> = {};

    if (navigation) {
      currentMetrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
      currentMetrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
    }

    const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
    if (fcp) {
      currentMetrics.firstContentfulPaint = fcp.startTime;
    }

    if ('memory' in performance) {
      const memory = (performance as any).memory;
      currentMetrics.memoryUsage = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }

    setMetrics(currentMetrics);
    return currentMetrics;
  };

  return {
    metrics,
    collectCurrentMetrics
  };
};

// Performance grade calculator
export const calculatePerformanceGrade = (metrics: Partial<PerformanceMetrics>): {
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  score: number;
  recommendations: string[];
} => {
  let score = 100;
  const recommendations: string[] = [];

  // Page Load Time (30% weight)
  if (metrics.pageLoadTime) {
    if (metrics.pageLoadTime > 5000) {
      score -= 30;
      recommendations.push('Optimize page load time (currently > 5s)');
    } else if (metrics.pageLoadTime > 3000) {
      score -= 15;
      recommendations.push('Improve page load time (currently > 3s)');
    }
  }

  // First Contentful Paint (25% weight)
  if (metrics.firstContentfulPaint) {
    if (metrics.firstContentfulPaint > 3000) {
      score -= 25;
      recommendations.push('Optimize First Contentful Paint (currently > 3s)');
    } else if (metrics.firstContentfulPaint > 1800) {
      score -= 12;
      recommendations.push('Improve First Contentful Paint (currently > 1.8s)');
    }
  }

  // Largest Contentful Paint (25% weight)
  if (metrics.largestContentfulPaint) {
    if (metrics.largestContentfulPaint > 4000) {
      score -= 25;
      recommendations.push('Optimize Largest Contentful Paint (currently > 4s)');
    } else if (metrics.largestContentfulPaint > 2500) {
      score -= 12;
      recommendations.push('Improve Largest Contentful Paint (currently > 2.5s)');
    }
  }

  // Cumulative Layout Shift (10% weight)
  if (metrics.cumulativeLayoutShift) {
    if (metrics.cumulativeLayoutShift > 0.25) {
      score -= 10;
      recommendations.push('Reduce Cumulative Layout Shift (currently > 0.25)');
    } else if (metrics.cumulativeLayoutShift > 0.1) {
      score -= 5;
      recommendations.push('Improve Cumulative Layout Shift (currently > 0.1)');
    }
  }

  // First Input Delay (10% weight)
  if (metrics.firstInputDelay) {
    if (metrics.firstInputDelay > 300) {
      score -= 10;
      recommendations.push('Optimize First Input Delay (currently > 300ms)');
    } else if (metrics.firstInputDelay > 100) {
      score -= 5;
      recommendations.push('Improve First Input Delay (currently > 100ms)');
    }
  }

  let grade: 'A' | 'B' | 'C' | 'D' | 'F';
  if (score >= 90) grade = 'A';
  else if (score >= 80) grade = 'B';
  else if (score >= 70) grade = 'C';
  else if (score >= 60) grade = 'D';
  else grade = 'F';

  return { grade, score: Math.max(0, score), recommendations };
};

export default PerformanceMonitor;
