'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import {
  Activity,
  Database,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Server,
  RefreshCw,
} from 'lucide-react';

interface PerformanceMetrics {
  queries: {
    total: number;
    recent: Array<{
      query: string;
      executionTime: number;
      timestamp: Date;
    }>;
    averageTime: number;
  };
  connections: {
    active: number;
    total: number;
    health: string;
  };
  cache: {
    hitRate: number;
    size: number;
    memory: string;
  };
}

interface DatabaseStats {
  totalUsers: number;
  totalLearningResources: number;
  totalLearningPaths: number;
  avgQueryTime: number;
  slowQueries: Array<{
    query: string;
    executionTime: number;
    timestamp: Date;
  }>;
  connectionPool: {
    totalConnections: number;
    activeConnections: number;
    failedConnections: number;
  };
  cache: {
    hitRate: number;
    size: number;
  };
}

interface QueryPattern {
  pattern: string;
  frequency: number;
  averageExecutionTime: number;
  lastSeen: Date;
}

export default function DatabasePerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [queryPatterns, setQueryPatterns] = useState<QueryPattern[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const fetchData = async () => {
    try {
      const [metricsRes, statsRes, patternsRes] = await Promise.all([
        fetch('/api/admin/database?action=metrics'),
        fetch('/api/admin/database'),
        fetch('/api/admin/database?action=query_patterns'),
      ]);

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json();
        setMetrics(metricsData.data);
      }

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData.data.database);
      }

      if (patternsRes.ok) {
        const patternsData = await patternsRes.json();
        setQueryPatterns(patternsData.data.mostFrequentPatterns || []);
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getHealthStatus = () => {
    if (!stats) return { status: 'unknown', color: 'gray' };

    const avgTime = stats.avgQueryTime;
    const failureRate = stats.connectionPool.failedConnections / stats.connectionPool.totalConnections;

    if (avgTime < 500 && failureRate < 0.05) {
      return { status: 'excellent', color: 'green' };
    } else if (avgTime < 1000 && failureRate < 0.1) {
      return { status: 'good', color: 'blue' };
    } else if (avgTime < 2000 && failureRate < 0.2) {
      return { status: 'fair', color: 'yellow' };
    } else {
      return { status: 'poor', color: 'red' };
    }
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading performance data...</span>
      </div>
    );
  }

  const healthStatus = getHealthStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Database Performance Monitor</h2>
          <p className="text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className="h-4 w-4 mr-2" />
            Auto Refresh: {autoRefresh ? 'On' : 'Off'}
          </Button>
          <Button variant="outline" size="sm" onClick={fetchData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Health Status Alert */}
      <Alert className={`border-${healthStatus.color}-200 bg-${healthStatus.color}-50`}>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          Database health status: <strong className="capitalize">{healthStatus.status}</strong>
          {stats && (
            <span className="ml-2">
              (Avg query time: {formatTime(stats.avgQueryTime)}, 
              Active connections: {stats.connectionPool.activeConnections})
            </span>
          )}
        </AlertDescription>
      </Alert>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Queries</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics ? formatNumber(metrics.queries.total) : '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              Avg: {metrics ? formatTime(metrics.queries.averageTime) : '0ms'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics ? `${metrics.cache.hitRate.toFixed(1)}%` : '0%'}
            </div>
            <Progress 
              value={metrics?.cache.hitRate || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics ? metrics.connections.active : '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              Total: {metrics ? metrics.connections.total : '0'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Slow Queries</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats ? stats.slowQueries.length : '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              Last hour
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="queries">Query Analysis</TabsTrigger>
          <TabsTrigger value="patterns">Query Patterns</TabsTrigger>
          <TabsTrigger value="cache">Cache Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Database Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Database Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                {stats && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Users:</span>
                      <span className="font-medium">{formatNumber(stats.totalUsers)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Learning Resources:</span>
                      <span className="font-medium">{formatNumber(stats.totalLearningResources)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Learning Paths:</span>
                      <span className="font-medium">{formatNumber(stats.totalLearningPaths)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Query Time:</span>
                      <span className="font-medium">{formatTime(stats.avgQueryTime)}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Connection Pool Status */}
            <Card>
              <CardHeader>
                <CardTitle>Connection Pool</CardTitle>
              </CardHeader>
              <CardContent>
                {stats && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Active Connections:</span>
                      <span className="font-medium">{stats.connectionPool.activeConnections}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Connections:</span>
                      <span className="font-medium">{stats.connectionPool.totalConnections}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Failed Connections:</span>
                      <span className="font-medium text-red-600">{stats.connectionPool.failedConnections}</span>
                    </div>
                    <div className="mt-4">
                      <div className="text-sm text-muted-foreground mb-1">Connection Usage</div>
                      <Progress 
                        value={(stats.connectionPool.activeConnections / stats.connectionPool.totalConnections) * 100} 
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="queries" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Slow Queries</CardTitle>
            </CardHeader>
            <CardContent>
              {stats?.slowQueries.length ? (
                <div className="space-y-2">
                  {stats.slowQueries.slice(0, 10).map((query, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex-1">
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {query.query.length > 60 ? `${query.query.substring(0, 60)}...` : query.query}
                        </code>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="destructive">
                          {formatTime(query.executionTime)}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {new Date(query.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No slow queries detected</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Most Frequent Query Patterns</CardTitle>
            </CardHeader>
            <CardContent>
              {queryPatterns.length ? (
                <div className="space-y-2">
                  {queryPatterns.slice(0, 10).map((pattern, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex-1">
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {pattern.pattern.length > 50 ? `${pattern.pattern.substring(0, 50)}...` : pattern.pattern}
                        </code>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">
                          {pattern.frequency}x
                        </Badge>
                        <Badge variant="outline">
                          {formatTime(pattern.averageExecutionTime)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No query patterns available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Cache Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                {metrics && (
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span>Hit Rate</span>
                        <span className="font-medium">{metrics.cache.hitRate.toFixed(1)}%</span>
                      </div>
                      <Progress value={metrics.cache.hitRate} />
                    </div>
                    <div className="flex justify-between">
                      <span>Cache Size:</span>
                      <span className="font-medium">{metrics.cache.size} entries</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Memory Usage:</span>
                      <span className="font-medium">{metrics.cache.memory}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
