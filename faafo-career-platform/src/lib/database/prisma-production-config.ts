/**
 * Production-Optimized Prisma Configuration
 * Enhanced Prisma setup with production connection pooling and monitoring
 */

import { PrismaClient } from '@prisma/client';
import { productionConnectionPool } from './production-connection-pool';

export interface PrismaProductionConfig {
  connectionLimit: number;
  connectionTimeout: number;
  queryTimeout: number;
  transactionTimeout: number;
  enableLogging: boolean;
  logLevel: 'info' | 'query' | 'warn' | 'error';
  enableMetrics: boolean;
  enableTracing: boolean;
  datasourceUrl: string;
  environment: 'development' | 'staging' | 'production';
  databaseTier: 'hobby' | 'basic' | 'standard' | 'premium' | 'enterprise';
}

export class PrismaProductionManager {
  private static instance: PrismaProductionManager;
  private prismaClient: PrismaClient | null = null;
  private config: PrismaProductionConfig;
  private isInitialized = false;

  private constructor() {
    this.config = this.loadConfiguration();
  }

  public static getInstance(): PrismaProductionManager {
    if (!PrismaProductionManager.instance) {
      PrismaProductionManager.instance = new PrismaProductionManager();
    }
    return PrismaProductionManager.instance;
  }

  /**
   * Load production configuration
   */
  private loadConfiguration(): PrismaProductionConfig {
    const environment = (process.env.NODE_ENV || 'development') as 'development' | 'staging' | 'production';
    const databaseTier = (process.env.DATABASE_TIER || 'basic') as 'hobby' | 'basic' | 'standard' | 'premium' | 'enterprise';
    
    // Get tier-specific limits
    const tierLimits = this.getTierLimits(databaseTier);
    
    return {
      connectionLimit: parseInt(process.env.DATABASE_CONNECTION_LIMIT || tierLimits.connectionLimit.toString()),
      connectionTimeout: parseInt(process.env.DATABASE_CONNECTION_TIMEOUT || tierLimits.connectionTimeout.toString()),
      queryTimeout: parseInt(process.env.DATABASE_QUERY_TIMEOUT || tierLimits.queryTimeout.toString()),
      transactionTimeout: parseInt(process.env.DATABASE_TRANSACTION_TIMEOUT || tierLimits.transactionTimeout.toString()),
      enableLogging: process.env.DATABASE_ENABLE_LOGGING !== 'false',
      logLevel: (process.env.DATABASE_LOG_LEVEL || tierLimits.logLevel) as 'info' | 'query' | 'warn' | 'error',
      enableMetrics: process.env.DATABASE_ENABLE_METRICS !== 'false',
      enableTracing: process.env.DATABASE_ENABLE_TRACING === 'true',
      datasourceUrl: process.env.DATABASE_URL || '',
      environment,
      databaseTier,
    };
  }

  /**
   * Get tier-specific configuration limits
   */
  private getTierLimits(tier: string) {
    const tierConfigs = {
      hobby: {
        connectionLimit: 5,
        connectionTimeout: 5000,
        queryTimeout: 10000,
        transactionTimeout: 15000,
        logLevel: 'warn',
      },
      basic: {
        connectionLimit: 20,
        connectionTimeout: 10000,
        queryTimeout: 15000,
        transactionTimeout: 30000,
        logLevel: 'warn',
      },
      standard: {
        connectionLimit: 50,
        connectionTimeout: 15000,
        queryTimeout: 20000,
        transactionTimeout: 45000,
        logLevel: 'info',
      },
      premium: {
        connectionLimit: 100,
        connectionTimeout: 20000,
        queryTimeout: 30000,
        transactionTimeout: 60000,
        logLevel: 'info',
      },
      enterprise: {
        connectionLimit: 200,
        connectionTimeout: 30000,
        queryTimeout: 45000,
        transactionTimeout: 120000,
        logLevel: 'query',
      },
    };

    return tierConfigs[tier as keyof typeof tierConfigs] || tierConfigs.basic;
  }

  /**
   * Initialize Prisma with production configuration
   */
  async initialize(): Promise<PrismaClient> {
    if (this.isInitialized && this.prismaClient) {
      return this.prismaClient;
    }

    console.log(`Initializing Prisma for ${this.config.environment} environment with ${this.config.databaseTier} tier`);

    // Build connection string with pooling parameters
    const connectionString = this.buildConnectionString();

    // Create Prisma client with production configuration
    this.prismaClient = new PrismaClient({
      datasources: {
        db: {
          url: connectionString,
        },
      },
      log: this.getLogConfiguration(),
      errorFormat: this.config.environment === 'production' ? 'minimal' : 'pretty',
    });

    // Setup middleware
    await this.setupMiddleware();

    // Setup event listeners
    this.setupEventListeners();

    // Test connection
    await this.testConnection();

    this.isInitialized = true;
    console.log('Prisma production configuration initialized successfully');

    return this.prismaClient;
  }

  /**
   * Build optimized connection string
   */
  private buildConnectionString(): string {
    const baseUrl = this.config.datasourceUrl;
    
    if (!baseUrl) {
      throw new Error('DATABASE_URL is required');
    }

    // Parse existing URL
    const url = new URL(baseUrl);
    
    // Add connection pooling parameters
    const params = new URLSearchParams(url.search);
    
    // Connection pool settings
    params.set('connection_limit', this.config.connectionLimit.toString());
    params.set('connect_timeout', Math.floor(this.config.connectionTimeout / 1000).toString());
    params.set('pool_timeout', Math.floor(this.config.queryTimeout / 1000).toString());
    
    // Performance optimizations
    params.set('pgbouncer', 'true');
    params.set('prepared_statements', 'false'); // Required for PgBouncer
    
    // SSL configuration for production
    if (this.config.environment === 'production') {
      params.set('sslmode', 'require');
    }

    // Schema and search path
    if (!params.has('schema')) {
      params.set('schema', 'public');
    }

    url.search = params.toString();
    return url.toString();
  }

  /**
   * Get log configuration based on environment and tier
   */
  private getLogConfiguration(): ('query' | 'info' | 'warn' | 'error')[] {
    if (!this.config.enableLogging) {
      return [];
    }

    const logConfig: ('query' | 'info' | 'warn' | 'error')[] = [];

    switch (this.config.logLevel) {
      case 'query':
        logConfig.push('query', 'info', 'warn', 'error');
        break;
      case 'info':
        logConfig.push('info', 'warn', 'error');
        break;
      case 'warn':
        logConfig.push('warn', 'error');
        break;
      case 'error':
        logConfig.push('error');
        break;
    }

    return logConfig;
  }

  /**
   * Setup production middleware
   */
  private async setupMiddleware(): Promise<void> {
    if (!this.prismaClient) return;

    // Connection pool monitoring middleware
    this.prismaClient.$use(async (params, next) => {
      const startTime = Date.now();
      
      try {
        const result = await next(params);
        
        // Record successful operation
        productionConnectionPool.recordMetrics({
          connectionTime: Date.now() - startTime,
          queryTime: Date.now() - startTime,
          success: true,
          query: `${params.model}.${params.action}`,
        });
        
        return result;
      } catch (error) {
        // Record failed operation
        productionConnectionPool.recordMetrics({
          connectionTime: Date.now() - startTime,
          queryTime: Date.now() - startTime,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          query: `${params.model}.${params.action}`,
        });
        
        throw error;
      }
    });

    // Query timeout middleware
    this.prismaClient.$use(async (params, next) => {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Query timeout after ${this.config.queryTimeout}ms`));
        }, this.config.queryTimeout);
      });

      return Promise.race([next(params), timeoutPromise]);
    });

    // Load additional middleware if available
    try {
      if (this.config.enableMetrics) {
        const { queryAnalysisMiddleware } = await import('./query-analyzer');
        this.prismaClient.$use(queryAnalysisMiddleware);
        console.log('Query analysis middleware enabled');
      }

      const { queryOptimizationMiddleware } = await import('./query-optimizer');
      this.prismaClient.$use(queryOptimizationMiddleware);
      console.log('Query optimization middleware enabled');

      if (process.env.REDIS_CACHE_ENABLED === 'true') {
        const { redisCacheMiddleware } = await import('./redis-cache');
        this.prismaClient.$use(redisCacheMiddleware);
        console.log('Redis cache middleware enabled');
      }
    } catch (error) {
      console.warn('Some middleware could not be loaded:', error);
    }
  }

  /**
   * Setup event listeners for monitoring
   */
  private setupEventListeners(): void {
    if (!this.prismaClient) return;

    // Listen to connection pool events
    productionConnectionPool.on('healthCheck', (data) => {
      if (!data.success) {
        console.error('Database health check failed:', data.error);
      }
    });

    productionConnectionPool.on('scaleUp', (data) => {
      console.log(`Connection pool scaled up: ${data.oldMax} -> ${data.newMax}`);
    });

    productionConnectionPool.on('scaleDown', (data) => {
      console.log(`Connection pool scaled down: ${data.oldMax} -> ${data.newMax}`);
    });

    productionConnectionPool.on('connectionLeak', (data) => {
      console.warn(`Connection leaks detected: ${data.count} (total: ${data.totalDetected})`);
    });

    productionConnectionPool.on('slowQuery', (data) => {
      console.warn(`Slow query detected: ${data.queryTime}ms (threshold: ${data.threshold}ms)`);
    });
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<void> {
    if (!this.prismaClient) {
      throw new Error('Prisma client not initialized');
    }

    try {
      await this.prismaClient.$queryRaw`SELECT 1 as connection_test`;
      console.log('Database connection test successful');
    } catch (error) {
      console.error('Database connection test failed:', error);
      throw error;
    }
  }

  /**
   * Get current configuration
   */
  getConfiguration(): PrismaProductionConfig {
    return { ...this.config };
  }

  /**
   * Get Prisma client instance
   */
  getClient(): PrismaClient {
    if (!this.prismaClient) {
      throw new Error('Prisma client not initialized. Call initialize() first.');
    }
    return this.prismaClient;
  }

  /**
   * Get connection pool statistics
   */
  getConnectionPoolStats() {
    return productionConnectionPool.getStats();
  }

  /**
   * Get production recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const poolStats = productionConnectionPool.getStats();
    const poolRecommendations = productionConnectionPool.getProductionRecommendations();
    
    recommendations.push(...poolRecommendations);

    // Configuration-specific recommendations
    if (this.config.connectionLimit < 10 && this.config.environment === 'production') {
      recommendations.push('Consider increasing connection limit for production workload.');
    }

    if (this.config.queryTimeout < 15000 && this.config.databaseTier !== 'hobby') {
      recommendations.push('Consider increasing query timeout for complex operations.');
    }

    if (!this.config.enableMetrics && this.config.environment === 'production') {
      recommendations.push('Enable metrics collection for better monitoring in production.');
    }

    return recommendations;
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    console.log('Shutting down Prisma production manager...');
    
    if (this.prismaClient) {
      await this.prismaClient.$disconnect();
      this.prismaClient = null;
    }
    
    await productionConnectionPool.shutdown();
    this.isInitialized = false;
    
    console.log('Prisma production manager shutdown completed');
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: any;
  }> {
    try {
      if (!this.prismaClient) {
        return {
          status: 'unhealthy',
          details: { error: 'Prisma client not initialized' },
        };
      }

      const startTime = Date.now();
      await this.prismaClient.$queryRaw`SELECT 1 as health`;
      const responseTime = Date.now() - startTime;

      const poolStats = productionConnectionPool.getStats();
      const utilizationRate = poolStats.activeConnections / this.config.connectionLimit;

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      
      if (responseTime > 1000 || utilizationRate > 0.9) {
        status = 'degraded';
      }
      
      if (responseTime > 5000 || utilizationRate > 0.95 || poolStats.failedConnections > poolStats.successfulConnections * 0.1) {
        status = 'unhealthy';
      }

      return {
        status,
        details: {
          responseTime,
          utilizationRate,
          poolStats,
          config: this.config,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }
}

// Export singleton instance
export const prismaProductionManager = PrismaProductionManager.getInstance();

// Export utility functions
export const initializePrismaProduction = async (): Promise<PrismaClient> => {
  return prismaProductionManager.initialize();
};

export const getPrismaProductionClient = (): PrismaClient => {
  return prismaProductionManager.getClient();
};

export const getPrismaProductionConfig = (): PrismaProductionConfig => {
  return prismaProductionManager.getConfiguration();
};

export default prismaProductionManager;
