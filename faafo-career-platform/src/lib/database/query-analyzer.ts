/**
 * Advanced Query Analysis and Optimization
 * Analyzes query patterns, detects N+1 queries, and provides optimization suggestions
 */

import { Prisma } from '@prisma/client';
import { queryOptimizer } from './query-optimizer';

export interface QueryPattern {
  pattern: string;
  frequency: number;
  averageExecutionTime: number;
  lastSeen: Date;
  examples: string[];
}

export interface N1QueryDetection {
  suspectedN1Queries: {
    pattern: string;
    frequency: number;
    timeWindow: string;
    suggestion: string;
  }[];
  recommendations: string[];
}

export interface QueryOptimizationSuggestion {
  query: string;
  currentExecutionTime: number;
  suggestedOptimization: string;
  expectedImprovement: string;
  priority: 'high' | 'medium' | 'low';
}

export interface ExecutionPlanAnalysis {
  query: string;
  executionPlan: any;
  bottlenecks: string[];
  suggestions: string[];
  indexRecommendations: string[];
}

class QueryAnalyzer {
  private queryPatterns = new Map<string, QueryPattern>();
  private recentQueries: Array<{
    query: string;
    timestamp: Date;
    executionTime: number;
  }> = [];
  private maxRecentQueries = 1000;

  /**
   * Analyze query patterns and detect potential issues
   */
  analyzeQueryPattern(params: Prisma.MiddlewareParams, executionTime: number): void {
    const querySignature = this.generateQuerySignature(params);
    const timestamp = new Date();

    // Store recent query for N+1 detection
    this.recentQueries.push({
      query: querySignature,
      timestamp,
      executionTime,
    });

    // Keep only recent queries
    if (this.recentQueries.length > this.maxRecentQueries) {
      this.recentQueries = this.recentQueries.slice(-this.maxRecentQueries);
    }

    // Update query patterns
    const existing = this.queryPatterns.get(querySignature);
    if (existing) {
      existing.frequency++;
      existing.averageExecutionTime = 
        (existing.averageExecutionTime * (existing.frequency - 1) + executionTime) / existing.frequency;
      existing.lastSeen = timestamp;
      
      // Keep only recent examples
      existing.examples.push(JSON.stringify(params.args));
      if (existing.examples.length > 5) {
        existing.examples = existing.examples.slice(-5);
      }
    } else {
      this.queryPatterns.set(querySignature, {
        pattern: querySignature,
        frequency: 1,
        averageExecutionTime: executionTime,
        lastSeen: timestamp,
        examples: [JSON.stringify(params.args)],
      });
    }
  }

  /**
   * Generate a normalized query signature for pattern analysis
   */
  private generateQuerySignature(params: Prisma.MiddlewareParams): string {
    const model = params.model || 'unknown';
    const action = params.action;
    
    // Normalize arguments to create a pattern
    let argsPattern = '';
    if (params.args) {
      argsPattern = this.normalizeArgs(params.args);
    }

    return `${model}.${action}${argsPattern}`;
  }

  /**
   * Normalize query arguments to identify patterns
   */
  private normalizeArgs(args: any): string {
    if (!args) return '';

    const normalized: any = {};

    // Normalize common patterns
    if (args.where) {
      normalized.where = this.normalizeWhereClause(args.where);
    }
    if (args.include) {
      normalized.include = this.normalizeInclude(args.include);
    }
    if (args.select) {
      normalized.select = Object.keys(args.select).sort();
    }
    if (args.orderBy) {
      normalized.orderBy = this.normalizeOrderBy(args.orderBy);
    }
    if (args.take) {
      normalized.take = 'LIMIT';
    }
    if (args.skip) {
      normalized.skip = 'OFFSET';
    }

    return JSON.stringify(normalized);
  }

  /**
   * Normalize WHERE clause for pattern matching
   */
  private normalizeWhereClause(where: any): any {
    if (!where || typeof where !== 'object') return where;

    const normalized: any = {};
    
    for (const [key, value] of Object.entries(where)) {
      if (typeof value === 'object' && value !== null) {
        // Handle nested conditions
        if (Array.isArray(value)) {
          normalized[key] = '[ARRAY]';
        } else if ('in' in value) {
          normalized[key] = { in: '[ARRAY]' };
        } else if ('contains' in value || 'startsWith' in value || 'endsWith' in value) {
          normalized[key] = { [Object.keys(value)[0]]: '[STRING]' };
        } else {
          normalized[key] = this.normalizeWhereClause(value);
        }
      } else {
        normalized[key] = '[VALUE]';
      }
    }

    return normalized;
  }

  /**
   * Normalize include clause
   */
  private normalizeInclude(include: any): any {
    if (typeof include === 'boolean') return include;
    if (!include || typeof include !== 'object') return include;

    const normalized: any = {};
    for (const key of Object.keys(include).sort()) {
      normalized[key] = true;
    }
    return normalized;
  }

  /**
   * Normalize orderBy clause
   */
  private normalizeOrderBy(orderBy: any): any {
    if (Array.isArray(orderBy)) {
      return orderBy.map(item => this.normalizeOrderBy(item));
    }
    if (typeof orderBy === 'object') {
      const normalized: any = {};
      for (const [key, value] of Object.entries(orderBy)) {
        normalized[key] = value;
      }
      return normalized;
    }
    return orderBy;
  }

  /**
   * Detect potential N+1 query patterns
   */
  detectN1Queries(timeWindowMinutes: number = 5): N1QueryDetection {
    const cutoffTime = new Date(Date.now() - timeWindowMinutes * 60 * 1000);
    const recentQueries = this.recentQueries.filter(q => q.timestamp > cutoffTime);

    const suspectedN1Queries: N1QueryDetection['suspectedN1Queries'] = [];
    const recommendations: string[] = [];

    // Group queries by pattern
    const queryGroups = new Map<string, typeof recentQueries>();
    
    recentQueries.forEach(query => {
      const pattern = query.query.split('[VALUE]')[0]; // Remove specific values
      if (!queryGroups.has(pattern)) {
        queryGroups.set(pattern, []);
      }
      queryGroups.get(pattern)!.push(query);
    });

    // Detect patterns that might indicate N+1 queries
    queryGroups.forEach((queries, pattern) => {
      if (queries.length > 10) { // More than 10 similar queries in time window
        const avgTime = queries.reduce((sum, q) => sum + q.executionTime, 0) / queries.length;
        
        if (pattern.includes('findUnique') || pattern.includes('findFirst')) {
          suspectedN1Queries.push({
            pattern,
            frequency: queries.length,
            timeWindow: `${timeWindowMinutes} minutes`,
            suggestion: 'Consider using findMany with include or batch loading',
          });
        }
      }
    });

    // Generate recommendations
    if (suspectedN1Queries.length > 0) {
      recommendations.push('N+1 query patterns detected. Consider using include/select to fetch related data in single queries.');
      recommendations.push('Use batch loading or dataloader pattern for frequently accessed related data.');
      recommendations.push('Review query patterns and consider denormalization for read-heavy operations.');
    }

    return {
      suspectedN1Queries,
      recommendations,
    };
  }

  /**
   * Generate optimization suggestions based on query patterns
   */
  generateOptimizationSuggestions(): QueryOptimizationSuggestion[] {
    const suggestions: QueryOptimizationSuggestion[] = [];

    this.queryPatterns.forEach((pattern) => {
      if (pattern.averageExecutionTime > 1000) { // Slow queries
        let suggestion = '';
        let expectedImprovement = '';
        let priority: 'high' | 'medium' | 'low' = 'medium';

        if (pattern.pattern.includes('findMany') && !pattern.pattern.includes('take')) {
          suggestion = 'Add pagination (take/skip) to limit result set size';
          expectedImprovement = '50-80% reduction in query time';
          priority = 'high';
        } else if (pattern.pattern.includes('include') && pattern.averageExecutionTime > 2000) {
          suggestion = 'Consider using select instead of include, or split into separate queries';
          expectedImprovement = '30-60% reduction in query time';
          priority = 'high';
        } else if (pattern.frequency > 100 && pattern.averageExecutionTime > 500) {
          suggestion = 'High-frequency slow query - consider adding specific index or caching';
          expectedImprovement = '40-70% reduction in query time';
          priority = 'high';
        } else if (pattern.pattern.includes('count') && pattern.averageExecutionTime > 500) {
          suggestion = 'Consider caching count results or using approximate counts';
          expectedImprovement = '60-90% reduction in query time';
          priority = 'medium';
        } else {
          suggestion = 'Review query structure and consider adding indexes';
          expectedImprovement = '20-40% reduction in query time';
          priority = 'low';
        }

        suggestions.push({
          query: pattern.pattern,
          currentExecutionTime: pattern.averageExecutionTime,
          suggestedOptimization: suggestion,
          expectedImprovement,
          priority,
        });
      }
    });

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Get query pattern statistics
   */
  getQueryPatternStats(): {
    totalPatterns: number;
    mostFrequentPatterns: QueryPattern[];
    slowestPatterns: QueryPattern[];
    recentActivity: number;
  } {
    const patterns = Array.from(this.queryPatterns.values());
    
    const mostFrequentPatterns = patterns
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 10);

    const slowestPatterns = patterns
      .sort((a, b) => b.averageExecutionTime - a.averageExecutionTime)
      .slice(0, 10);

    const recentActivity = this.recentQueries.filter(
      q => q.timestamp > new Date(Date.now() - 5 * 60 * 1000)
    ).length;

    return {
      totalPatterns: patterns.length,
      mostFrequentPatterns,
      slowestPatterns,
      recentActivity,
    };
  }

  /**
   * Clear old patterns and queries
   */
  cleanup(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours

    // Remove old patterns
    this.queryPatterns.forEach((pattern, key) => {
      if (pattern.lastSeen < cutoffTime) {
        this.queryPatterns.delete(key);
      }
    });

    // Keep only recent queries
    this.recentQueries = this.recentQueries.filter(q => q.timestamp > cutoffTime);
  }

  /**
   * Get middleware for query analysis
   */
  middleware(): Prisma.Middleware {
    return async (params: Prisma.MiddlewareParams, next) => {
      const startTime = Date.now();
      const result = await next(params);
      const executionTime = Date.now() - startTime;

      // Skip analysis for health check queries to prevent feedback loops
      const isHealthCheck = params.action === 'queryRaw' &&
        params.args?.query?.includes?.('health_check');

      if (!isHealthCheck) {
        // Analyze the query pattern
        this.analyzeQueryPattern(params, executionTime);
      }

      return result;
    };
  }
}

// Singleton instance
export const queryAnalyzer = new QueryAnalyzer();

// Export middleware
export const queryAnalysisMiddleware = queryAnalyzer.middleware();

export default queryAnalyzer;
