/**
 * Advanced Query Optimization Middleware
 * Implements query caching, performance monitoring, and optimization strategies
 */

import { Prisma } from '@prisma/client';
import { connectionPool } from './connection-pool';

export interface QueryCacheConfig {
  enabled: boolean;
  defaultTTL: number;
  maxCacheSize: number;
  cacheKeyPrefix: string;
}

export interface QueryMetrics {
  query: string;
  model: string;
  action: string;
  executionTime: number;
  cacheHit: boolean;
  rowsAffected: number;
  timestamp: Date;
  args?: any;
}

export interface SlowQueryAlert {
  query: string;
  executionTime: number;
  threshold: number;
  timestamp: Date;
  frequency: number;
}

class QueryOptimizer {
  private queryCache = new Map<string, { data: any; expiry: number }>();
  private queryMetrics: QueryMetrics[] = [];
  private slowQueries = new Map<string, SlowQueryAlert>();
  private config: QueryCacheConfig;

  constructor() {
    this.config = {
      enabled: process.env.QUERY_CACHE_ENABLED !== 'false',
      defaultTTL: parseInt(process.env.QUERY_CACHE_TTL || '300000'), // 5 minutes
      maxCacheSize: parseInt(process.env.QUERY_CACHE_SIZE || '1000'),
      cacheKeyPrefix: 'query_cache:',
    };
  }

  /**
   * Generate cache key for query
   */
  private generateCacheKey(params: Prisma.MiddlewareParams): string {
    const key = `${this.config.cacheKeyPrefix}${params.model}.${params.action}:${JSON.stringify(params.args)}`;
    return Buffer.from(key).toString('base64').substring(0, 100);
  }

  /**
   * Check if query should be cached
   */
  private shouldCache(params: Prisma.MiddlewareParams): boolean {
    if (!this.config.enabled) return false;
    
    // Only cache read operations
    const readOperations = ['findFirst', 'findMany', 'findUnique', 'count', 'aggregate'];
    return readOperations.includes(params.action);
  }

  /**
   * Get cached query result
   */
  private getCachedResult(cacheKey: string): any | null {
    const cached = this.queryCache.get(cacheKey);
    
    if (!cached) return null;
    
    if (Date.now() > cached.expiry) {
      this.queryCache.delete(cacheKey);
      return null;
    }
    
    return cached.data;
  }

  /**
   * Cache query result
   */
  private setCachedResult(cacheKey: string, data: any, ttl?: number): void {
    // Implement LRU eviction if cache is full
    if (this.queryCache.size >= this.config.maxCacheSize) {
      const firstKey = this.queryCache.keys().next().value;
      if (firstKey) {
        this.queryCache.delete(firstKey);
      }
    }

    const expiry = Date.now() + (ttl || this.config.defaultTTL);
    this.queryCache.set(cacheKey, { data, expiry });
  }

  /**
   * Record query metrics
   */
  private recordMetrics(
    params: Prisma.MiddlewareParams,
    executionTime: number,
    cacheHit: boolean,
    rowsAffected: number = 0
  ): void {
    const metric: QueryMetrics = {
      query: `${params.model}.${params.action}`,
      model: params.model || 'unknown',
      action: params.action,
      executionTime,
      cacheHit,
      rowsAffected,
      timestamp: new Date(),
      args: this.sanitizeArgs(params.args),
    };

    this.queryMetrics.push(metric);
    
    // Keep only recent metrics
    if (this.queryMetrics.length > 10000) {
      this.queryMetrics = this.queryMetrics.slice(-5000);
    }

    // Record in connection pool
    connectionPool.recordMetrics({
      connectionTime: executionTime,
      queryTime: executionTime,
      success: true,
    });

    // Check for slow queries
    this.checkSlowQuery(metric);
  }

  /**
   * Sanitize query arguments for logging
   */
  private sanitizeArgs(args: any): any {
    if (!args) return args;
    
    // Remove sensitive data
    const sanitized = JSON.parse(JSON.stringify(args));
    
    // Remove password fields
    if (sanitized.data?.password) {
      sanitized.data.password = '[REDACTED]';
    }
    
    // Limit large arrays/objects
    if (Array.isArray(sanitized.data)) {
      sanitized.data = sanitized.data.slice(0, 5);
    }
    
    return sanitized;
  }

  /**
   * Check for slow queries and alert
   */
  private checkSlowQuery(metric: QueryMetrics): void {
    const threshold = parseInt(process.env.SLOW_QUERY_THRESHOLD || '1000'); // 1 second
    
    if (metric.executionTime > threshold) {
      const queryKey = metric.query;
      const existing = this.slowQueries.get(queryKey);
      
      if (existing) {
        existing.frequency++;
        existing.executionTime = Math.max(existing.executionTime, metric.executionTime);
      } else {
        this.slowQueries.set(queryKey, {
          query: queryKey,
          executionTime: metric.executionTime,
          threshold,
          timestamp: metric.timestamp,
          frequency: 1,
        });
      }

      // Log slow query
      console.warn(`Slow query detected: ${queryKey} (${metric.executionTime}ms)`);
    }
  }

  /**
   * Prisma middleware for query optimization
   */
  middleware(): Prisma.Middleware {
    return async (params: Prisma.MiddlewareParams, next) => {
      const startTime = Date.now();
      const shouldCache = this.shouldCache(params);
      let cacheHit = false;
      let result;

      // Try to get from cache
      if (shouldCache) {
        const cacheKey = this.generateCacheKey(params);
        const cachedResult = this.getCachedResult(cacheKey);
        
        if (cachedResult !== null) {
          cacheHit = true;
          result = cachedResult;
        }
      }

      // Execute query if not cached
      if (!cacheHit) {
        try {
          result = await next(params);
          
          // Cache the result
          if (shouldCache && result) {
            const cacheKey = this.generateCacheKey(params);
            this.setCachedResult(cacheKey, result);
          }
        } catch (error) {
          // Record failed query
          connectionPool.recordMetrics({
            connectionTime: Date.now() - startTime,
            queryTime: Date.now() - startTime,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
          throw error;
        }
      }

      const executionTime = Date.now() - startTime;
      
      // Record metrics
      const rowsAffected = Array.isArray(result) ? result.length : 
                          result && typeof result === 'object' ? 1 : 0;
      
      this.recordMetrics(params, executionTime, cacheHit, rowsAffected);

      return result;
    };
  }

  /**
   * Get query performance statistics
   */
  getStats(): {
    totalQueries: number;
    cacheHitRate: number;
    averageExecutionTime: number;
    slowQueries: SlowQueryAlert[];
    topQueries: { query: string; count: number; avgTime: number }[];
  } {
    const totalQueries = this.queryMetrics.length;
    const cacheHits = this.queryMetrics.filter(m => m.cacheHit).length;
    const cacheHitRate = totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0;
    
    const totalTime = this.queryMetrics.reduce((sum, m) => sum + m.executionTime, 0);
    const averageExecutionTime = totalQueries > 0 ? totalTime / totalQueries : 0;

    // Get top queries by frequency
    const queryFrequency = new Map<string, { count: number; totalTime: number }>();
    
    this.queryMetrics.forEach(metric => {
      const existing = queryFrequency.get(metric.query);
      if (existing) {
        existing.count++;
        existing.totalTime += metric.executionTime;
      } else {
        queryFrequency.set(metric.query, { count: 1, totalTime: metric.executionTime });
      }
    });

    const topQueries = Array.from(queryFrequency.entries())
      .map(([query, stats]) => ({
        query,
        count: stats.count,
        avgTime: stats.totalTime / stats.count,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalQueries,
      cacheHitRate,
      averageExecutionTime,
      slowQueries: Array.from(this.slowQueries.values()),
      topQueries,
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.queryCache.clear();
  }

  /**
   * Clear metrics
   */
  clearMetrics(): void {
    this.queryMetrics = [];
    this.slowQueries.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: string;
  } {
    const recentQueries = this.queryMetrics.slice(-1000);
    const cacheableQueries = recentQueries.filter(m => 
      ['findFirst', 'findMany', 'findUnique', 'count', 'aggregate'].includes(m.action)
    );
    const cacheHits = cacheableQueries.filter(m => m.cacheHit);
    
    const hitRate = cacheableQueries.length > 0 ? 
      (cacheHits.length / cacheableQueries.length) * 100 : 0;

    // Estimate memory usage (rough calculation)
    const estimatedMemory = this.queryCache.size * 1024; // Assume 1KB per entry
    const memoryUsage = estimatedMemory > 1024 * 1024 ? 
      `${(estimatedMemory / (1024 * 1024)).toFixed(2)} MB` :
      `${(estimatedMemory / 1024).toFixed(2)} KB`;

    return {
      size: this.queryCache.size,
      maxSize: this.config.maxCacheSize,
      hitRate,
      memoryUsage,
    };
  }
}

// Singleton instance
export const queryOptimizer = new QueryOptimizer();

// Export middleware function
export const queryOptimizationMiddleware = queryOptimizer.middleware();

export default queryOptimizer;
