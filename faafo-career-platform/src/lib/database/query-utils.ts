/**
 * Query Optimization Utilities
 * Provides optimized query builders and utilities for common patterns
 */

import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export interface PaginationOptions {
  page?: number;
  limit?: number;
  cursor?: string;
  orderBy?: any;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextCursor?: string;
    prevCursor?: string;
  };
}

export interface BatchOperationResult {
  success: boolean;
  processed: number;
  errors: string[];
  results?: any[];
}

export interface QueryOptimizationOptions {
  useSelect?: boolean;
  batchSize?: number;
  enableCaching?: boolean;
  cacheTTL?: number;
}

/**
 * Optimized pagination utility with cursor-based and offset-based options
 */
export class PaginationHelper {
  /**
   * Cursor-based pagination (recommended for large datasets)
   */
  static async cursorPaginate<T>(
    model: any,
    options: PaginationOptions & {
      where?: any;
      select?: any;
      include?: any;
      cursorField?: string;
    }
  ): Promise<PaginatedResult<T>> {
    const {
      limit = 20,
      cursor,
      where = {},
      select,
      include,
      orderBy = { createdAt: 'desc' },
      cursorField = 'id',
    } = options;

    // Build cursor condition
    const cursorCondition = cursor ? { [cursorField]: { gt: cursor } } : {};
    const finalWhere = { ...where, ...cursorCondition };

    // Fetch one extra item to determine if there's a next page
    const items = await model.findMany({
      where: finalWhere,
      ...(select && { select }),
      ...(include && { include }),
      orderBy,
      take: limit + 1,
    });

    const hasNext = items.length > limit;
    const data = hasNext ? items.slice(0, -1) : items;
    
    const nextCursor = hasNext && data.length > 0 
      ? data[data.length - 1][cursorField] 
      : undefined;

    // Get total count (expensive operation, consider caching)
    const total = await model.count({ where });

    return {
      data,
      pagination: {
        page: 1, // Cursor pagination doesn't use traditional pages
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext,
        hasPrev: !!cursor,
        nextCursor,
      },
    };
  }

  /**
   * Offset-based pagination (for smaller datasets or when total count is needed)
   */
  static async offsetPaginate<T>(
    model: any,
    options: PaginationOptions & {
      where?: any;
      select?: any;
      include?: any;
    }
  ): Promise<PaginatedResult<T>> {
    const {
      page = 1,
      limit = 20,
      where = {},
      select,
      include,
      orderBy = { createdAt: 'desc' },
    } = options;

    const skip = (page - 1) * limit;

    // Execute count and data queries in parallel
    const [data, total] = await Promise.all([
      model.findMany({
        where,
        ...(select && { select }),
        ...(include && { include }),
        orderBy,
        take: limit,
        skip,
      }),
      model.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }
}

/**
 * Batch operation utilities for efficient bulk operations
 */
export class BatchOperations {
  /**
   * Batch create with error handling
   */
  static async batchCreate<T>(
    model: any,
    data: any[],
    options: { batchSize?: number; skipDuplicates?: boolean } = {}
  ): Promise<BatchOperationResult> {
    const { batchSize = 100, skipDuplicates = false } = options;
    const results: any[] = [];
    const errors: string[] = [];
    let processed = 0;

    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      try {
        const result = await model.createMany({
          data: batch,
          skipDuplicates,
        });
        
        results.push(result);
        processed += batch.length;
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${errorMsg}`);
      }
    }

    return {
      success: errors.length === 0,
      processed,
      errors,
      results,
    };
  }

  /**
   * Batch update with error handling
   */
  static async batchUpdate<T>(
    model: any,
    updates: Array<{ where: any; data: any }>,
    options: { batchSize?: number } = {}
  ): Promise<BatchOperationResult> {
    const { batchSize = 50 } = options;
    const results: any[] = [];
    const errors: string[] = [];
    let processed = 0;

    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      try {
        const batchResults = await Promise.allSettled(
          batch.map(update => model.update(update))
        );

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
            processed++;
          } else {
            errors.push(`Update ${i + index + 1}: ${result.reason}`);
          }
        });
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${errorMsg}`);
      }
    }

    return {
      success: errors.length === 0,
      processed,
      errors,
      results,
    };
  }

  /**
   * Batch delete with error handling
   */
  static async batchDelete(
    model: any,
    conditions: any[],
    options: { batchSize?: number } = {}
  ): Promise<BatchOperationResult> {
    const { batchSize = 100 } = options;
    const results: any[] = [];
    const errors: string[] = [];
    let processed = 0;

    for (let i = 0; i < conditions.length; i += batchSize) {
      const batch = conditions.slice(i, i + batchSize);
      
      try {
        const batchResults = await Promise.allSettled(
          batch.map(where => model.deleteMany({ where }))
        );

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
            processed += result.value.count || 0;
          } else {
            errors.push(`Delete ${i + index + 1}: ${result.reason}`);
          }
        });
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${errorMsg}`);
      }
    }

    return {
      success: errors.length === 0,
      processed,
      errors,
      results,
    };
  }
}

/**
 * Optimized query builders for common patterns
 */
export class OptimizedQueries {
  /**
   * Get user with optimized includes
   */
  static async getUserWithProgress(userId: string, options: QueryOptimizationOptions = {}) {
    const { useSelect = true } = options;

    if (useSelect) {
      // Use select for better performance
      return prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          profile: {
            select: {
              firstName: true,
              lastName: true,
              experienceLevel: true,
              currentIndustry: true,
            },
          },
          learningPaths: {
            select: {
              id: true,
              status: true,
              progressPercent: true,
              learningPath: {
                select: {
                  id: true,
                  title: true,
                  difficulty: true,
                },
              },
            },
            where: { status: { in: ['IN_PROGRESS', 'NOT_STARTED'] } },
            take: 10,
          },
        },
      });
    } else {
      // Use include (less optimal but more flexible)
      return prisma.user.findUnique({
        where: { id: userId },
        include: {
          profile: true,
          learningPaths: {
            include: {
              learningPath: true,
            },
            where: { status: { in: ['IN_PROGRESS', 'NOT_STARTED'] } },
            take: 10,
          },
        },
      });
    }
  }

  /**
   * Get learning resources with optimized filtering
   */
  static async getLearningResources(
    filters: {
      category?: string;
      skillLevel?: string;
      type?: string;
      isActive?: boolean;
    } = {},
    pagination: PaginationOptions = {}
  ) {
    const where: any = {
      isActive: filters.isActive ?? true,
    };

    if (filters.category) where.category = filters.category;
    if (filters.skillLevel) where.skillLevel = filters.skillLevel;
    if (filters.type) where.type = filters.type;

    return PaginationHelper.cursorPaginate(prisma.learningResource, {
      where,
      select: {
        id: true,
        title: true,
        description: true,
        type: true,
        category: true,
        skillLevel: true,
        estimatedHours: true,
        cost: true,
        url: true,
        createdAt: true,
        _count: {
          select: {
            ratings: true,
            userProgress: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      ...pagination,
    });
  }

  /**
   * Get forum posts with optimized includes
   */
  static async getForumPosts(
    categoryId?: string,
    pagination: PaginationOptions = {}
  ) {
    const where: any = {
      isHidden: false,
    };

    if (categoryId) where.categoryId = categoryId;

    return PaginationHelper.offsetPaginate(prisma.forumPost, {
      where,
      select: {
        id: true,
        title: true,
        content: true,
        viewCount: true,
        likeCount: true,
        isPinned: true,
        createdAt: true,
        author: {
          select: {
            id: true,
            name: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            replies: true,
            reactions: true,
          },
        },
      },
      orderBy: [
        { isPinned: 'desc' },
        { createdAt: 'desc' },
      ],
      ...pagination,
    });
  }

  /**
   * Get user analytics with aggregations
   */
  static async getUserAnalytics(userId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const [
      learningProgress,
      completedResources,
      forumActivity,
      skillProgress,
    ] = await Promise.all([
      prisma.userLearningProgress.groupBy({
        by: ['status'],
        where: {
          userId,
          updatedAt: { gte: startDate },
        },
        _count: true,
      }),
      prisma.userLearningProgress.count({
        where: {
          userId,
          status: 'COMPLETED',
          completedAt: { gte: startDate },
        },
      }),
      prisma.forumPost.count({
        where: {
          authorId: userId,
          createdAt: { gte: startDate },
        },
      }),
      prisma.userSkillProgress.findMany({
        where: { userId },
        select: {
          currentLevel: true,
          skill: {
            select: {
              name: true,
              category: true,
            },
          },
        },
        orderBy: { currentLevel: 'desc' },
        take: 10,
      }),
    ]);

    return {
      learningProgress,
      completedResources,
      forumActivity,
      skillProgress,
      period: `${days} days`,
    };
  }
}

/**
 * Query performance monitoring utilities
 */
export class QueryPerformanceMonitor {
  private static slowQueryThreshold = 1000; // 1 second

  /**
   * Monitor query execution time
   */
  static async executeWithMonitoring<T>(
    queryName: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await queryFn();
      const executionTime = Date.now() - startTime;
      
      if (executionTime > this.slowQueryThreshold) {
        console.warn(`Slow query detected: ${queryName} took ${executionTime}ms`);
      }
      
      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`Query failed: ${queryName} after ${executionTime}ms`, error);
      throw error;
    }
  }

  /**
   * Batch execute with monitoring
   */
  static async batchExecuteWithMonitoring<T>(
    operations: Array<{ name: string; fn: () => Promise<T> }>
  ): Promise<T[]> {
    const startTime = Date.now();
    
    try {
      const results = await Promise.all(
        operations.map(async (op) => {
          return this.executeWithMonitoring(op.name, op.fn);
        })
      );
      
      const totalTime = Date.now() - startTime;
      console.log(`Batch operation completed in ${totalTime}ms`);
      
      return results;
    } catch (error) {
      const totalTime = Date.now() - startTime;
      console.error(`Batch operation failed after ${totalTime}ms`, error);
      throw error;
    }
  }
}

// Export utilities
export const queryUtils = {
  PaginationHelper,
  BatchOperations,
  OptimizedQueries,
  QueryPerformanceMonitor,
};

export default queryUtils;
