/**
 * Database Initialization and Setup
 * Comprehensive database initialization with optimization and monitoring setup
 */

import { prisma, checkDatabaseHealth } from '../prisma';
import { connectionPool } from './connection-pool';
import { queryOptimizer } from './query-optimizer';
import { dbConfigManager } from './config-manager';
import { dbOptimization } from '../services/databaseOptimization';

export interface InitializationResult {
  success: boolean;
  message: string;
  details: {
    connection: boolean;
    optimization: boolean;
    monitoring: boolean;
    indexes: boolean;
  };
  errors: string[];
  warnings: string[];
}

/**
 * Initialize database with full optimization setup
 */
export async function initializeDatabase(): Promise<InitializationResult> {
  const result: InitializationResult = {
    success: false,
    message: '',
    details: {
      connection: false,
      optimization: false,
      monitoring: false,
      indexes: false,
    },
    errors: [],
    warnings: [],
  };

  console.log('🚀 Starting database initialization...');

  try {
    // Step 1: Test database connection
    console.log('📡 Testing database connection...');
    const healthCheck = await checkDatabaseHealth();
    
    if (!healthCheck.healthy) {
      result.errors.push(`Database connection failed: ${healthCheck.error}`);
      return result;
    }
    
    result.details.connection = true;
    console.log(`✅ Database connection healthy (${healthCheck.latency}ms)`);

    // Step 2: Validate configuration
    console.log('⚙️ Validating database configuration...');
    const config = dbConfigManager.getConfigSummary();
    console.log(`📊 Environment: ${config.environment}`);
    console.log(`🔗 Max connections: ${config.database.maxConnections}`);
    console.log(`💾 Cache enabled: ${config.cache.enabled}`);
    console.log(`📈 Metrics enabled: ${config.performance.metricsEnabled}`);

    // Step 3: Setup query optimization
    console.log('🔧 Setting up query optimization...');
    try {
      // Query optimizer is already initialized via middleware
      const queryStats = queryOptimizer.getStats();
      result.details.optimization = true;
      console.log('✅ Query optimization middleware active');
    } catch (error) {
      result.warnings.push('Query optimization setup had issues');
      console.warn('⚠️ Query optimization setup warning:', error);
    }

    // Step 4: Setup connection pool monitoring
    console.log('📊 Setting up connection pool monitoring...');
    try {
      const poolStats = connectionPool.getStats();
      result.details.monitoring = true;
      console.log('✅ Connection pool monitoring active');
    } catch (error) {
      result.warnings.push('Connection pool monitoring setup had issues');
      console.warn('⚠️ Connection pool monitoring warning:', error);
    }

    // Step 5: Apply performance indexes (optional, can be skipped in development)
    if (process.env.NODE_ENV === 'production' || process.env.APPLY_INDEXES === 'true') {
      console.log('🗂️ Applying performance indexes...');
      try {
        const indexResult = await dbOptimization.applyPerformanceIndexes();
        if (indexResult.success) {
          result.details.indexes = true;
          console.log('✅ Performance indexes applied successfully');
        } else {
          result.warnings.push(`Index application issues: ${indexResult.message}`);
          console.warn('⚠️ Index application warning:', indexResult.message);
        }
      } catch (error) {
        result.warnings.push('Performance index application failed');
        console.warn('⚠️ Performance index warning:', error);
      }
    } else {
      console.log('⏭️ Skipping performance indexes (development mode)');
    }

    // Step 6: Run initial optimization
    console.log('🔄 Running initial database optimization...');
    try {
      const optimizeResult = await dbOptimization.optimizeDatabase();
      if (optimizeResult.success) {
        console.log('✅ Database optimization completed');
      } else {
        result.warnings.push(`Database optimization issues: ${optimizeResult.message}`);
        console.warn('⚠️ Database optimization warning:', optimizeResult.message);
      }
    } catch (error) {
      result.warnings.push('Database optimization failed');
      console.warn('⚠️ Database optimization warning:', error);
    }

    // Step 7: Generate initial performance report
    console.log('📋 Generating initial performance report...');
    try {
      const performanceReport = await dbOptimization.getPerformanceReport();
      console.log(`📊 Performance score: ${performanceReport.summary.score}/100 (${performanceReport.summary.status})`);
      
      if (performanceReport.summary.issues.length > 0) {
        console.log('⚠️ Performance issues detected:');
        performanceReport.summary.issues.forEach(issue => {
          console.log(`   - ${issue}`);
          result.warnings.push(issue);
        });
      }
    } catch (error) {
      result.warnings.push('Performance report generation failed');
      console.warn('⚠️ Performance report warning:', error);
    }

    // Determine overall success
    const criticalComponents = [result.details.connection];
    const allCriticalSuccessful = criticalComponents.every(Boolean);
    
    result.success = allCriticalSuccessful;
    
    if (result.success) {
      result.message = 'Database initialization completed successfully';
      console.log('🎉 Database initialization completed successfully!');
      
      if (result.warnings.length > 0) {
        console.log(`⚠️ ${result.warnings.length} warnings encountered`);
      }
    } else {
      result.message = 'Database initialization failed';
      console.error('❌ Database initialization failed');
    }

    return result;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    result.errors.push(errorMessage);
    result.message = `Database initialization failed: ${errorMessage}`;
    console.error('❌ Database initialization error:', error);
    return result;
  }
}

/**
 * Quick health check for monitoring
 */
export async function quickHealthCheck(): Promise<{
  healthy: boolean;
  latency: number;
  details: {
    database: boolean;
    cache: boolean;
    connections: boolean;
  };
}> {
  try {
    const startTime = Date.now();
    
    // Test database connection
    const dbHealth = await checkDatabaseHealth();
    
    // Check cache status
    const cacheStats = queryOptimizer.getCacheStats();
    const cacheHealthy = cacheStats.size >= 0; // Basic check
    
    // Check connection pool
    const poolStats = connectionPool.getStats();
    const connectionsHealthy = poolStats.failedConnections === 0 || 
                              poolStats.successfulConnections > poolStats.failedConnections * 10;
    
    const latency = Date.now() - startTime;
    const overall = dbHealth.healthy && cacheHealthy && connectionsHealthy;
    
    return {
      healthy: overall,
      latency,
      details: {
        database: dbHealth.healthy,
        cache: cacheHealthy,
        connections: connectionsHealthy,
      },
    };
  } catch (error) {
    return {
      healthy: false,
      latency: -1,
      details: {
        database: false,
        cache: false,
        connections: false,
      },
    };
  }
}

/**
 * Graceful shutdown of database connections
 */
export async function shutdownDatabase(): Promise<void> {
  console.log('🔄 Shutting down database connections...');
  
  try {
    // Shutdown connection pool monitoring
    await connectionPool.shutdown();
    
    // Clear caches
    queryOptimizer.clearCache();
    queryOptimizer.clearMetrics();
    
    // Disconnect Prisma
    await prisma.$disconnect();
    
    console.log('✅ Database shutdown completed');
  } catch (error) {
    console.error('❌ Database shutdown error:', error);
  }
}

/**
 * Auto-initialize database on module load (if enabled)
 */
if (process.env.AUTO_INIT_DATABASE === 'true') {
  initializeDatabase().catch(error => {
    console.error('Auto-initialization failed:', error);
  });
}

// Setup graceful shutdown handlers
if (typeof process !== 'undefined') {
  process.on('SIGINT', shutdownDatabase);
  process.on('SIGTERM', shutdownDatabase);
  process.on('beforeExit', shutdownDatabase);
}

export default {
  initializeDatabase,
  quickHealthCheck,
  shutdownDatabase,
};
