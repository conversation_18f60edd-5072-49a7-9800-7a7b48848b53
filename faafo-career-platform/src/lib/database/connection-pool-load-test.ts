/**
 * Connection Pool Load Testing Utility
 * Tests connection pool performance under various load conditions
 */

import { PrismaClient } from '@prisma/client';
import { productionConnectionPool } from './production-connection-pool';

export interface LoadTestConfig {
  concurrentConnections: number;
  testDurationMs: number;
  queryTypes: ('read' | 'write' | 'complex')[];
  rampUpTimeMs: number;
  rampDownTimeMs: number;
  targetQPS: number; // Queries per second
  enableMetrics: boolean;
}

export interface LoadTestResult {
  testConfig: LoadTestConfig;
  startTime: Date;
  endTime: Date;
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  queriesPerSecond: number;
  connectionPoolStats: any;
  errors: string[];
  recommendations: string[];
}

export interface QueryMetric {
  queryType: string;
  startTime: number;
  endTime: number;
  success: boolean;
  error?: string;
  connectionTime: number;
}

export class ConnectionPoolLoadTester {
  private prisma: PrismaClient;
  private metrics: QueryMetric[] = [];
  private isRunning = false;
  private startTime?: Date;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Run comprehensive load test
   */
  async runLoadTest(config: LoadTestConfig): Promise<LoadTestResult> {
    console.log('Starting connection pool load test...');
    console.log('Config:', config);

    this.metrics = [];
    this.isRunning = true;
    this.startTime = new Date();

    const result: Partial<LoadTestResult> = {
      testConfig: config,
      startTime: this.startTime,
      totalQueries: 0,
      successfulQueries: 0,
      failedQueries: 0,
      errors: [],
    };

    try {
      // Ramp up phase
      if (config.rampUpTimeMs > 0) {
        console.log(`Ramp up phase: ${config.rampUpTimeMs}ms`);
        await this.rampUpPhase(config);
      }

      // Main test phase
      console.log(`Main test phase: ${config.testDurationMs}ms`);
      await this.mainTestPhase(config);

      // Ramp down phase
      if (config.rampDownTimeMs > 0) {
        console.log(`Ramp down phase: ${config.rampDownTimeMs}ms`);
        await this.rampDownPhase(config);
      }

      // Calculate results
      const endTime = new Date();
      const testResults = this.calculateResults(config, this.startTime, endTime);
      
      console.log('Load test completed successfully');
      return testResults;

    } catch (error) {
      console.error('Load test failed:', error);
      throw error;
    } finally {
      this.isRunning = false;
      await this.cleanup();
    }
  }

  /**
   * Ramp up phase - gradually increase load
   */
  private async rampUpPhase(config: LoadTestConfig): Promise<void> {
    const steps = 10;
    const stepDuration = config.rampUpTimeMs / steps;
    const maxConcurrency = config.concurrentConnections;

    for (let step = 1; step <= steps; step++) {
      const currentConcurrency = Math.floor((step / steps) * maxConcurrency);
      await this.executeQueriesWithConcurrency(currentConcurrency, stepDuration, config);
    }
  }

  /**
   * Main test phase - full load
   */
  private async mainTestPhase(config: LoadTestConfig): Promise<void> {
    await this.executeQueriesWithConcurrency(
      config.concurrentConnections,
      config.testDurationMs,
      config
    );
  }

  /**
   * Ramp down phase - gradually decrease load
   */
  private async rampDownPhase(config: LoadTestConfig): Promise<void> {
    const steps = 10;
    const stepDuration = config.rampDownTimeMs / steps;
    const maxConcurrency = config.concurrentConnections;

    for (let step = steps; step >= 1; step--) {
      const currentConcurrency = Math.floor((step / steps) * maxConcurrency);
      await this.executeQueriesWithConcurrency(currentConcurrency, stepDuration, config);
    }
  }

  /**
   * Execute queries with specified concurrency
   */
  private async executeQueriesWithConcurrency(
    concurrency: number,
    durationMs: number,
    config: LoadTestConfig
  ): Promise<void> {
    const startTime = Date.now();
    const endTime = startTime + durationMs;
    const queryInterval = 1000 / config.targetQPS; // ms between queries

    const workers: Promise<void>[] = [];

    for (let i = 0; i < concurrency; i++) {
      workers.push(this.queryWorker(endTime, queryInterval, config));
    }

    await Promise.all(workers);
  }

  /**
   * Individual query worker
   */
  private async queryWorker(
    endTime: number,
    queryInterval: number,
    config: LoadTestConfig
  ): Promise<void> {
    while (Date.now() < endTime && this.isRunning) {
      const queryType = config.queryTypes[Math.floor(Math.random() * config.queryTypes.length)];
      
      try {
        await this.executeQuery(queryType);
        
        // Wait for next query based on target QPS
        if (queryInterval > 0) {
          await new Promise(resolve => setTimeout(resolve, queryInterval));
        }
      } catch (error) {
        // Error already recorded in executeQuery
      }
    }
  }

  /**
   * Execute a single query and record metrics
   */
  private async executeQuery(queryType: 'read' | 'write' | 'complex'): Promise<void> {
    const startTime = Date.now();
    const metric: Partial<QueryMetric> = {
      queryType,
      startTime,
    };

    try {
      const connectionStartTime = Date.now();
      
      switch (queryType) {
        case 'read':
          await this.executeReadQuery();
          break;
        case 'write':
          await this.executeWriteQuery();
          break;
        case 'complex':
          await this.executeComplexQuery();
          break;
      }

      metric.connectionTime = Date.now() - connectionStartTime;
      metric.success = true;

    } catch (error) {
      metric.success = false;
      metric.error = error instanceof Error ? error.message : 'Unknown error';
    } finally {
      metric.endTime = Date.now();
      this.metrics.push(metric as QueryMetric);
    }
  }

  /**
   * Execute read query
   */
  private async executeReadQuery(): Promise<void> {
    await this.prisma.user.findMany({
      take: 10,
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
      },
    });
  }

  /**
   * Execute write query
   */
  private async executeWriteQuery(): Promise<void> {
    const testUser = await this.prisma.user.create({
      data: {
        name: `Load Test User ${Date.now()}`,
        email: `loadtest${Date.now()}@example.com`,
        password: 'test-password',
      },
    });

    // Clean up test data
    await this.prisma.user.delete({
      where: { id: testUser.id },
    });
  }

  /**
   * Execute complex query
   */
  private async executeComplexQuery(): Promise<void> {
    await this.prisma.user.findMany({
      include: {
        profile: true,
        learningPaths: {
          include: {
            learningPath: {
              include: {
                steps: {
                  take: 5,
                  include: {
                    resource: true,
                  },
                },
              },
            },
          },
          take: 3,
        },
      },
      take: 5,
    });
  }

  /**
   * Calculate test results
   */
  private calculateResults(
    config: LoadTestConfig,
    startTime: Date,
    endTime: Date
  ): LoadTestResult {
    const successfulMetrics = this.metrics.filter(m => m.success);
    const failedMetrics = this.metrics.filter(m => !m.success);
    
    const responseTimes = successfulMetrics.map(m => m.endTime - m.startTime);
    responseTimes.sort((a, b) => a - b);

    const totalDurationMs = endTime.getTime() - startTime.getTime();
    const queriesPerSecond = (this.metrics.length / totalDurationMs) * 1000;

    const p95Index = Math.floor(responseTimes.length * 0.95);
    const p99Index = Math.floor(responseTimes.length * 0.99);

    const errors = failedMetrics.map(m => m.error || 'Unknown error');
    const uniqueErrors = Array.from(new Set(errors));

    const recommendations = this.generateRecommendations(config, {
      totalQueries: this.metrics.length,
      successfulQueries: successfulMetrics.length,
      failedQueries: failedMetrics.length,
      averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
      queriesPerSecond,
    });

    return {
      testConfig: config,
      startTime,
      endTime,
      totalQueries: this.metrics.length,
      successfulQueries: successfulMetrics.length,
      failedQueries: failedMetrics.length,
      averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length || 0,
      minResponseTime: responseTimes[0] || 0,
      maxResponseTime: responseTimes[responseTimes.length - 1] || 0,
      p95ResponseTime: responseTimes[p95Index] || 0,
      p99ResponseTime: responseTimes[p99Index] || 0,
      queriesPerSecond,
      connectionPoolStats: productionConnectionPool.getStats(),
      errors: uniqueErrors,
      recommendations,
    };
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(config: LoadTestConfig, results: any): string[] {
    const recommendations: string[] = [];

    if (results.failedQueries > results.totalQueries * 0.01) {
      recommendations.push('High failure rate detected. Consider increasing connection pool size or optimizing queries.');
    }

    if (results.averageResponseTime > 1000) {
      recommendations.push('High average response time. Consider query optimization or database performance tuning.');
    }

    if (results.queriesPerSecond < config.targetQPS * 0.8) {
      recommendations.push('Target QPS not achieved. Consider scaling database resources or optimizing connection pool.');
    }

    const poolStats = productionConnectionPool.getStats();
    const utilizationRate = poolStats.activeConnections / poolStats.totalConnections;

    if (utilizationRate > 0.9) {
      recommendations.push('Connection pool utilization is very high. Consider increasing pool size.');
    }

    if (poolStats.averageConnectionTime > 500) {
      recommendations.push('High connection establishment time. Check network latency and database performance.');
    }

    return recommendations;
  }

  /**
   * Run predefined test scenarios
   */
  async runStandardTests(): Promise<LoadTestResult[]> {
    const scenarios: LoadTestConfig[] = [
      {
        concurrentConnections: 5,
        testDurationMs: 30000,
        queryTypes: ['read'],
        rampUpTimeMs: 5000,
        rampDownTimeMs: 5000,
        targetQPS: 10,
        enableMetrics: true,
      },
      {
        concurrentConnections: 10,
        testDurationMs: 60000,
        queryTypes: ['read', 'write'],
        rampUpTimeMs: 10000,
        rampDownTimeMs: 10000,
        targetQPS: 20,
        enableMetrics: true,
      },
      {
        concurrentConnections: 20,
        testDurationMs: 120000,
        queryTypes: ['read', 'write', 'complex'],
        rampUpTimeMs: 15000,
        rampDownTimeMs: 15000,
        targetQPS: 50,
        enableMetrics: true,
      },
    ];

    const results: LoadTestResult[] = [];

    for (const scenario of scenarios) {
      console.log(`\nRunning scenario: ${scenario.concurrentConnections} concurrent connections`);
      const result = await this.runLoadTest(scenario);
      results.push(result);
      
      // Wait between tests
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    return results;
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    try {
      await this.prisma.$disconnect();
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  /**
   * Generate load test report
   */
  generateReport(results: LoadTestResult[]): string {
    let report = '# Connection Pool Load Test Report\n\n';
    
    results.forEach((result, index) => {
      report += `## Test ${index + 1}: ${result.testConfig.concurrentConnections} Concurrent Connections\n\n`;
      report += `- **Duration**: ${result.endTime.getTime() - result.startTime.getTime()}ms\n`;
      report += `- **Total Queries**: ${result.totalQueries}\n`;
      report += `- **Success Rate**: ${((result.successfulQueries / result.totalQueries) * 100).toFixed(2)}%\n`;
      report += `- **Average Response Time**: ${result.averageResponseTime.toFixed(2)}ms\n`;
      report += `- **P95 Response Time**: ${result.p95ResponseTime.toFixed(2)}ms\n`;
      report += `- **P99 Response Time**: ${result.p99ResponseTime.toFixed(2)}ms\n`;
      report += `- **Queries Per Second**: ${result.queriesPerSecond.toFixed(2)}\n\n`;
      
      if (result.recommendations.length > 0) {
        report += '### Recommendations:\n';
        result.recommendations.forEach(rec => {
          report += `- ${rec}\n`;
        });
        report += '\n';
      }
      
      if (result.errors.length > 0) {
        report += '### Errors:\n';
        result.errors.forEach(error => {
          report += `- ${error}\n`;
        });
        report += '\n';
      }
    });

    return report;
  }
}

// Export utility functions
export const runConnectionPoolLoadTest = async (config: LoadTestConfig): Promise<LoadTestResult> => {
  const tester = new ConnectionPoolLoadTester();
  return tester.runLoadTest(config);
};

export const runStandardConnectionPoolTests = async (): Promise<LoadTestResult[]> => {
  const tester = new ConnectionPoolLoadTester();
  return tester.runStandardTests();
};

export default ConnectionPoolLoadTester;
