/**
 * Enterprise-Level Backup and Disaster Recovery System
 * Comprehensive backup strategies with automated recovery procedures
 */

import { PrismaClient } from '@prisma/client';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';

const execAsync = promisify(exec);

// Backup configuration
interface BackupConfig {
  database: {
    enabled: boolean;
    schedule: string; // cron format
    retention: {
      daily: number;
      weekly: number;
      monthly: number;
      yearly: number;
    };
    compression: boolean;
    encryption: boolean;
  };
  files: {
    enabled: boolean;
    directories: string[];
    exclusions: string[];
    compression: boolean;
  };
  storage: {
    local: {
      enabled: boolean;
      path: string;
    };
    cloud: {
      enabled: boolean;
      provider: 'aws' | 'gcp' | 'azure';
      bucket: string;
      region: string;
    };
  };
  monitoring: {
    enabled: boolean;
    alertOnFailure: boolean;
    healthCheckInterval: number;
  };
}

// Backup metadata
interface BackupMetadata {
  id: string;
  type: 'database' | 'files' | 'full';
  timestamp: number;
  size: number;
  checksum: string;
  encrypted: boolean;
  compressed: boolean;
  location: string;
  status: 'in_progress' | 'completed' | 'failed';
  duration?: number;
  error?: string;
}

// Recovery options
interface RecoveryOptions {
  backupId: string;
  targetTimestamp?: number;
  dryRun: boolean;
  skipValidation: boolean;
  restoreLocation?: string;
}

class EnterpriseBackup {
  private config: BackupConfig;
  private prisma: PrismaClient;
  private backupHistory: BackupMetadata[] = [];
  private isBackupInProgress = false;

  constructor() {
    this.config = this.getBackupConfig();
    this.prisma = new PrismaClient();
    this.setupBackupScheduling();
  }

  /**
   * Create comprehensive database backup
   */
  async createDatabaseBackup(): Promise<BackupMetadata> {
    if (this.isBackupInProgress) {
      throw new Error('Backup already in progress');
    }

    this.isBackupInProgress = true;
    const startTime = Date.now();
    const backupId = this.generateBackupId();
    
    const metadata: BackupMetadata = {
      id: backupId,
      type: 'database',
      timestamp: startTime,
      size: 0,
      checksum: '',
      encrypted: this.config.database.encryption,
      compressed: this.config.database.compression,
      location: '',
      status: 'in_progress'
    };

    try {
      // Create backup directory
      const backupDir = path.join(this.config.storage.local.path, 'database');
      await fs.mkdir(backupDir, { recursive: true });

      // Generate backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `faafo_db_backup_${timestamp}.sql`;
      const backupPath = path.join(backupDir, filename);

      // Create database dump
      await this.createDatabaseDump(backupPath);

      // Compress if enabled
      let finalPath = backupPath;
      if (this.config.database.compression) {
        finalPath = await this.compressFile(backupPath);
        await fs.unlink(backupPath); // Remove uncompressed file
      }

      // Encrypt if enabled
      if (this.config.database.encryption) {
        finalPath = await this.encryptFile(finalPath);
        if (this.config.database.compression) {
          await fs.unlink(backupPath + '.gz'); // Remove unencrypted file
        } else {
          await fs.unlink(backupPath); // Remove unencrypted file
        }
      }

      // Get file stats
      const stats = await fs.stat(finalPath);
      metadata.size = stats.size;
      metadata.checksum = await this.calculateChecksum(finalPath);
      metadata.location = finalPath;
      metadata.status = 'completed';
      metadata.duration = Date.now() - startTime;

      // Upload to cloud storage if enabled
      if (this.config.storage.cloud.enabled) {
        await this.uploadToCloud(finalPath, backupId);
      }

      // Add to backup history
      this.backupHistory.push(metadata);
      
      // Clean up old backups
      await this.cleanupOldBackups();

      console.log(`✅ Database backup completed: ${backupId}`);
      return metadata;

    } catch (error) {
      metadata.status = 'failed';
      metadata.error = error instanceof Error ? error.message : 'Unknown error';
      metadata.duration = Date.now() - startTime;
      
      this.backupHistory.push(metadata);
      console.error(`❌ Database backup failed: ${error}`);
      throw error;
    } finally {
      this.isBackupInProgress = false;
    }
  }

  /**
   * Create file system backup
   */
  async createFileBackup(): Promise<BackupMetadata> {
    const startTime = Date.now();
    const backupId = this.generateBackupId();
    
    const metadata: BackupMetadata = {
      id: backupId,
      type: 'files',
      timestamp: startTime,
      size: 0,
      checksum: '',
      encrypted: false,
      compressed: this.config.files.compression,
      location: '',
      status: 'in_progress'
    };

    try {
      // Create backup directory
      const backupDir = path.join(this.config.storage.local.path, 'files');
      await fs.mkdir(backupDir, { recursive: true });

      // Generate backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `faafo_files_backup_${timestamp}.tar.gz`;
      const backupPath = path.join(backupDir, filename);

      // Create tar archive
      await this.createFileArchive(backupPath);

      // Get file stats
      const stats = await fs.stat(backupPath);
      metadata.size = stats.size;
      metadata.checksum = await this.calculateChecksum(backupPath);
      metadata.location = backupPath;
      metadata.status = 'completed';
      metadata.duration = Date.now() - startTime;

      // Upload to cloud storage if enabled
      if (this.config.storage.cloud.enabled) {
        await this.uploadToCloud(backupPath, backupId);
      }

      this.backupHistory.push(metadata);
      console.log(`✅ File backup completed: ${backupId}`);
      return metadata;

    } catch (error) {
      metadata.status = 'failed';
      metadata.error = error instanceof Error ? error.message : 'Unknown error';
      metadata.duration = Date.now() - startTime;
      
      this.backupHistory.push(metadata);
      console.error(`❌ File backup failed: ${error}`);
      throw error;
    }
  }

  /**
   * Restore database from backup
   */
  async restoreDatabase(options: RecoveryOptions): Promise<void> {
    const backup = this.backupHistory.find(b => b.id === options.backupId);
    if (!backup || backup.type !== 'database') {
      throw new Error(`Database backup not found: ${options.backupId}`);
    }

    if (backup.status !== 'completed') {
      throw new Error(`Backup is not in completed state: ${backup.status}`);
    }

    console.log(`🔄 Starting database restore from backup: ${options.backupId}`);

    try {
      // Validate backup integrity
      if (!options.skipValidation) {
        await this.validateBackupIntegrity(backup);
      }

      // Create restore point before restoration
      if (!options.dryRun) {
        const restorePointBackup = await this.createDatabaseBackup();
        console.log(`📍 Created restore point: ${restorePointBackup.id}`);
      }

      // Prepare backup file for restoration
      let restoreFile = backup.location;
      
      // Decrypt if needed
      if (backup.encrypted) {
        restoreFile = await this.decryptFile(restoreFile);
      }

      // Decompress if needed
      if (backup.compressed) {
        restoreFile = await this.decompressFile(restoreFile);
      }

      if (options.dryRun) {
        console.log(`🧪 Dry run: Would restore database from ${restoreFile}`);
        return;
      }

      // Perform database restoration
      await this.performDatabaseRestore(restoreFile);

      console.log(`✅ Database restore completed successfully`);

    } catch (error) {
      console.error(`❌ Database restore failed: ${error}`);
      throw error;
    }
  }

  /**
   * Get backup history and statistics
   */
  getBackupHistory(): {
    backups: BackupMetadata[];
    statistics: {
      totalBackups: number;
      successfulBackups: number;
      failedBackups: number;
      totalSize: number;
      averageDuration: number;
      lastBackup?: BackupMetadata;
    };
  } {
    const successfulBackups = this.backupHistory.filter(b => b.status === 'completed');
    const failedBackups = this.backupHistory.filter(b => b.status === 'failed');
    const totalSize = successfulBackups.reduce((sum, b) => sum + b.size, 0);
    const averageDuration = successfulBackups.length > 0
      ? successfulBackups.reduce((sum, b) => sum + (b.duration || 0), 0) / successfulBackups.length
      : 0;

    return {
      backups: this.backupHistory.sort((a, b) => b.timestamp - a.timestamp),
      statistics: {
        totalBackups: this.backupHistory.length,
        successfulBackups: successfulBackups.length,
        failedBackups: failedBackups.length,
        totalSize,
        averageDuration,
        lastBackup: this.backupHistory[this.backupHistory.length - 1]
      }
    };
  }

  /**
   * Validate backup integrity
   */
  async validateBackupIntegrity(backup: BackupMetadata): Promise<boolean> {
    try {
      // Check if file exists
      await fs.access(backup.location);

      // Verify checksum
      const currentChecksum = await this.calculateChecksum(backup.location);
      if (currentChecksum !== backup.checksum) {
        throw new Error(`Checksum mismatch for backup ${backup.id}`);
      }

      // Additional validation for database backups
      if (backup.type === 'database') {
        await this.validateDatabaseBackup(backup.location);
      }

      return true;
    } catch (error) {
      console.error(`Backup validation failed for ${backup.id}:`, error);
      return false;
    }
  }

  /**
   * Private helper methods
   */
  private getBackupConfig(): BackupConfig {
    return {
      database: {
        enabled: process.env.BACKUP_DATABASE_ENABLED !== 'false',
        schedule: process.env.BACKUP_SCHEDULE || '0 2 * * *', // Daily at 2 AM
        retention: {
          daily: parseInt(process.env.BACKUP_RETENTION_DAILY || '30'),
          weekly: parseInt(process.env.BACKUP_RETENTION_WEEKLY || '12'),
          monthly: parseInt(process.env.BACKUP_RETENTION_MONTHLY || '12'),
          yearly: parseInt(process.env.BACKUP_RETENTION_YEARLY || '7')
        },
        compression: process.env.BACKUP_COMPRESSION !== 'false',
        encryption: process.env.BACKUP_ENCRYPTION === 'true'
      },
      files: {
        enabled: process.env.BACKUP_FILES_ENABLED === 'true',
        directories: process.env.BACKUP_DIRECTORIES?.split(',') || ['./uploads', './public'],
        exclusions: process.env.BACKUP_EXCLUSIONS?.split(',') || ['node_modules', '.git', '.next'],
        compression: true
      },
      storage: {
        local: {
          enabled: true,
          path: process.env.BACKUP_LOCAL_PATH || './backups'
        },
        cloud: {
          enabled: process.env.BACKUP_CLOUD_ENABLED === 'true',
          provider: (process.env.BACKUP_CLOUD_PROVIDER as any) || 'aws',
          bucket: process.env.BACKUP_CLOUD_BUCKET || '',
          region: process.env.BACKUP_CLOUD_REGION || 'us-east-1'
        }
      },
      monitoring: {
        enabled: process.env.BACKUP_MONITORING_ENABLED !== 'false',
        alertOnFailure: process.env.BACKUP_ALERT_ON_FAILURE !== 'false',
        healthCheckInterval: parseInt(process.env.BACKUP_HEALTH_CHECK_INTERVAL || '3600000') // 1 hour
      }
    };
  }

  private generateBackupId(): string {
    return `backup_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  private async createDatabaseDump(outputPath: string): Promise<void> {
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl) {
      throw new Error('DATABASE_URL not configured');
    }

    // Parse database URL
    const url = new URL(dbUrl);
    const host = url.hostname;
    const port = url.port || '5432';
    const database = url.pathname.slice(1);
    const username = url.username;
    const password = url.password;

    // Create pg_dump command
    const command = `PGPASSWORD="${password}" pg_dump -h ${host} -p ${port} -U ${username} -d ${database} -f ${outputPath}`;
    
    await execAsync(command);
  }

  private async createFileArchive(outputPath: string): Promise<void> {
    const directories = this.config.files.directories.join(' ');
    const exclusions = this.config.files.exclusions.map(e => `--exclude=${e}`).join(' ');
    
    const command = `tar -czf ${outputPath} ${exclusions} ${directories}`;
    await execAsync(command);
  }

  private async compressFile(filePath: string): Promise<string> {
    const compressedPath = `${filePath}.gz`;
    const command = `gzip -c ${filePath} > ${compressedPath}`;
    await execAsync(command);
    return compressedPath;
  }

  private async decompressFile(filePath: string): Promise<string> {
    const decompressedPath = filePath.replace(/\.gz$/, '');
    const command = `gunzip -c ${filePath} > ${decompressedPath}`;
    await execAsync(command);
    return decompressedPath;
  }

  private async encryptFile(filePath: string): Promise<string> {
    const encryptedPath = `${filePath}.enc`;
    const key = process.env.BACKUP_ENCRYPTION_KEY || 'default-key';
    const command = `openssl enc -aes-256-cbc -salt -in ${filePath} -out ${encryptedPath} -k ${key}`;
    await execAsync(command);
    return encryptedPath;
  }

  private async decryptFile(filePath: string): Promise<string> {
    const decryptedPath = filePath.replace(/\.enc$/, '');
    const key = process.env.BACKUP_ENCRYPTION_KEY || 'default-key';
    const command = `openssl enc -aes-256-cbc -d -in ${filePath} -out ${decryptedPath} -k ${key}`;
    await execAsync(command);
    return decryptedPath;
  }

  private async calculateChecksum(filePath: string): Promise<string> {
    const fileBuffer = await fs.readFile(filePath);
    return crypto.createHash('sha256').update(fileBuffer).digest('hex');
  }

  private async uploadToCloud(filePath: string, backupId: string): Promise<void> {
    // Implement cloud upload based on provider
    console.log(`Uploading backup ${backupId} to cloud storage...`);
    // This would integrate with AWS S3, Google Cloud Storage, or Azure Blob Storage
  }

  private async performDatabaseRestore(backupFile: string): Promise<void> {
    const dbUrl = process.env.DATABASE_URL;
    if (!dbUrl) {
      throw new Error('DATABASE_URL not configured');
    }

    // Parse database URL
    const url = new URL(dbUrl);
    const host = url.hostname;
    const port = url.port || '5432';
    const database = url.pathname.slice(1);
    const username = url.username;
    const password = url.password;

    // Create psql restore command
    const command = `PGPASSWORD="${password}" psql -h ${host} -p ${port} -U ${username} -d ${database} -f ${backupFile}`;
    
    await execAsync(command);
  }

  private async validateDatabaseBackup(backupFile: string): Promise<void> {
    // Basic validation - check if file contains SQL commands
    const content = await fs.readFile(backupFile, 'utf8');
    if (!content.includes('CREATE TABLE') && !content.includes('INSERT INTO')) {
      throw new Error('Invalid database backup file');
    }
  }

  private async cleanupOldBackups(): Promise<void> {
    // Implement backup retention policy
    const now = Date.now();
    const retentionPeriods = {
      daily: this.config.database.retention.daily * 24 * 60 * 60 * 1000,
      weekly: this.config.database.retention.weekly * 7 * 24 * 60 * 60 * 1000,
      monthly: this.config.database.retention.monthly * 30 * 24 * 60 * 60 * 1000,
      yearly: this.config.database.retention.yearly * 365 * 24 * 60 * 60 * 1000
    };

    // Remove backups older than retention periods
    for (const backup of this.backupHistory) {
      const age = now - backup.timestamp;
      if (age > retentionPeriods.daily) {
        try {
          await fs.unlink(backup.location);
          console.log(`🗑️ Cleaned up old backup: ${backup.id}`);
        } catch (error) {
          console.error(`Failed to cleanup backup ${backup.id}:`, error);
        }
      }
    }
  }

  private setupBackupScheduling(): void {
    // Implement cron-based backup scheduling
    if (this.config.database.enabled) {
      console.log(`📅 Backup scheduling enabled: ${this.config.database.schedule}`);
      // This would integrate with a cron job scheduler
    }
  }
}

// Export singleton instance
export const enterpriseBackup = new EnterpriseBackup();

// Export types
export type { BackupConfig, BackupMetadata, RecoveryOptions };
