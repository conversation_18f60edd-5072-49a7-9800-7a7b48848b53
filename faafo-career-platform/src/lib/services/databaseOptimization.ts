import prisma from '@/lib/prisma';
import { connectionPool } from '@/lib/database/connection-pool';
import { queryOptimizer } from '@/lib/database/query-optimizer';
import { dbConfigManager } from '@/lib/database/config-manager';
import fs from 'fs';
import path from 'path';

interface QueryPerformanceMetrics {
  query: string;
  executionTime: number;
  rowsAffected: number;
  timestamp: Date;
}

interface DatabaseStats {
  totalUsers: number;
  totalLearningResources: number;
  totalLearningPaths: number;
  totalAssessments: number;
  totalForumPosts: number;
  activeEnrollments: number;
  completedLearningPaths: number;
  avgQueryTime: number;
  slowQueries: QueryPerformanceMetrics[];
  // Enhanced metrics
  connectionPool: {
    totalConnections: number;
    activeConnections: number;
    successfulConnections: number;
    failedConnections: number;
    averageConnectionTime: number;
    uptime: number;
  };
  queryOptimization: {
    totalQueries: number;
    cacheHitRate: number;
    averageExecutionTime: number;
    slowQueriesCount: number;
  };
  cache: {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: string;
  };
}

class DatabaseOptimizationService {
  private queryMetrics: QueryPerformanceMetrics[] = [];
  private maxMetricsHistory = 1000;

  // Apply performance indexes
  async applyPerformanceIndexes(): Promise<{ success: boolean; message: string; errors?: string[] }> {
    try {
      const indexesPath = path.join(process.cwd(), 'prisma', 'migrations', 'add_performance_indexes.sql');
      
      if (!fs.existsSync(indexesPath)) {
        return {
          success: false,
          message: 'Performance indexes SQL file not found'
        };
      }

      const indexesSQL = fs.readFileSync(indexesPath, 'utf-8');
      const statements = indexesSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      const errors: string[] = [];
      let successCount = 0;

      for (const statement of statements) {
        try {
          await prisma.$executeRawUnsafe(statement);
          successCount++;
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Failed to execute: ${statement.substring(0, 100)}... - ${errorMsg}`);
        }
      }

      return {
        success: errors.length === 0,
        message: `Applied ${successCount} indexes successfully${errors.length > 0 ? `, ${errors.length} failed` : ''}`,
        ...(errors.length > 0 && { errors })
      };

    } catch (error) {
      return {
        success: false,
        message: `Failed to apply indexes: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Monitor query performance
  async trackQuery(query: string, executionTimeMs: number, rowsAffected: number = 0): Promise<void> {
    const metric: QueryPerformanceMetrics = {
      query: query.substring(0, 500), // Truncate long queries
      executionTime: executionTimeMs,
      rowsAffected,
      timestamp: new Date()
    };

    this.queryMetrics.push(metric);

    // Keep only recent metrics
    if (this.queryMetrics.length > this.maxMetricsHistory) {
      this.queryMetrics = this.queryMetrics.slice(-this.maxMetricsHistory);
    }

    // Log slow queries
    if (executionTimeMs > 1000) { // Queries taking more than 1 second
      console.warn(`Slow query detected (${executionTimeMs}ms):`, query.substring(0, 200));
    }
  }

  // Get database statistics
  async getDatabaseStats(): Promise<DatabaseStats> {
    const startTime = Date.now();

    try {
      const [
        totalUsers,
        totalLearningResources,
        totalLearningPaths,
        totalAssessments,
        totalForumPosts,
        activeEnrollments,
        completedLearningPaths
      ] = await Promise.all([
        prisma.user.count(),
        prisma.learningResource.count({ where: { isActive: true } }),
        prisma.learningPath.count({ where: { isActive: true } }),
        prisma.assessment.count(),
        prisma.forumPost.count({ where: { isHidden: false } }),
        prisma.userLearningPath.count({ where: { status: { in: ['IN_PROGRESS', 'NOT_STARTED'] } } }),
        prisma.userLearningPath.count({ where: { status: 'COMPLETED' } })
      ]);

      const queryTime = Date.now() - startTime;
      await this.trackQuery('getDatabaseStats', queryTime);

      // Get enhanced metrics from query optimizer
      const queryStats = queryOptimizer.getStats();
      const connectionStats = connectionPool.getStats();
      const cacheStats = queryOptimizer.getCacheStats();

      // Calculate average query time from recent metrics
      const recentMetrics = this.queryMetrics.slice(-100);
      const avgQueryTime = recentMetrics.length > 0
        ? recentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / recentMetrics.length
        : 0;

      // Get slow queries from recent metrics
      const slowQueries = recentMetrics
        .filter(m => m.executionTime > 500)
        .sort((a, b) => b.executionTime - a.executionTime)
        .slice(0, 10);

      return {
        totalUsers,
        totalLearningResources,
        totalLearningPaths,
        totalAssessments,
        totalForumPosts,
        activeEnrollments,
        completedLearningPaths,
        avgQueryTime,
        slowQueries,
        // Enhanced metrics
        connectionPool: {
          totalConnections: connectionStats.totalConnections,
          activeConnections: connectionStats.activeConnections,
          successfulConnections: connectionStats.successfulConnections,
          failedConnections: connectionStats.failedConnections,
          averageConnectionTime: connectionStats.averageConnectionTime,
          uptime: connectionStats.uptime
        },
        queryOptimization: {
          totalQueries: queryStats.totalQueries,
          cacheHitRate: queryStats.cacheHitRate,
          averageExecutionTime: queryStats.averageExecutionTime,
          slowQueriesCount: queryStats.slowQueries.length
        },
        cache: {
          size: cacheStats.size,
          maxSize: cacheStats.maxSize,
          hitRate: cacheStats.hitRate,
          memoryUsage: cacheStats.memoryUsage
        }
      };

    } catch (error) {
      console.error('Error getting database stats:', error);
      throw error;
    }
  }

  // Optimize database by running ANALYZE and VACUUM (PostgreSQL specific)
  async optimizeDatabase(): Promise<{ success: boolean; message: string }> {
    try {
      // Note: These commands are database-specific
      // For SQLite, we can use ANALYZE and VACUUM
      // For PostgreSQL, we can use ANALYZE and VACUUM
      // For MySQL, we can use ANALYZE TABLE and OPTIMIZE TABLE

      const databaseUrl = process.env.DATABASE_URL || '';
      
      if (databaseUrl.includes('postgresql')) {
        // PostgreSQL optimization
        await prisma.$executeRaw`ANALYZE`;
        await prisma.$executeRaw`VACUUM ANALYZE`;
        return {
          success: true,
          message: 'PostgreSQL database optimized successfully'
        };
      } else if (databaseUrl.includes('sqlite')) {
        // SQLite optimization
        await prisma.$executeRaw`ANALYZE`;
        await prisma.$executeRaw`VACUUM`;
        return {
          success: true,
          message: 'SQLite database optimized successfully'
        };
      } else {
        return {
          success: false,
          message: 'Database optimization not supported for this database type'
        };
      }

    } catch (error) {
      return {
        success: false,
        message: `Database optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // Check index usage and effectiveness
  async analyzeIndexUsage(): Promise<any> {
    try {
      const databaseUrl = process.env.DATABASE_URL || '';
      
      if (databaseUrl.includes('postgresql')) {
        // PostgreSQL index usage analysis
        const indexUsage = await prisma.$queryRaw`
          SELECT 
            schemaname,
            tablename,
            indexname,
            idx_tup_read,
            idx_tup_fetch,
            idx_scan
          FROM pg_stat_user_indexes 
          ORDER BY idx_scan DESC
          LIMIT 20
        `;
        
        return {
          type: 'postgresql',
          indexUsage
        };
      } else if (databaseUrl.includes('sqlite')) {
        // SQLite doesn't have built-in index usage stats
        // We can check if indexes exist
        const indexes = await prisma.$queryRaw`
          SELECT name, sql 
          FROM sqlite_master 
          WHERE type = 'index' 
          AND name NOT LIKE 'sqlite_%'
          ORDER BY name
        `;
        
        return {
          type: 'sqlite',
          indexes
        };
      } else {
        return {
          type: 'unknown',
          message: 'Index analysis not supported for this database type'
        };
      }

    } catch (error) {
      console.error('Error analyzing index usage:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Get table sizes and row counts
  async getTableSizes(): Promise<any> {
    try {
      const tables = [
        'User',
        'Assessment',
        'AssessmentResponse',
        'LearningResource',
        'UserLearningProgress',
        'LearningPath',
        'UserLearningPath',
        'UserLearningPathProgress',
        'ForumPost',
        'ForumComment',
        'CareerPath',
        'Skill',
        'UserSkillProgress',
        'LearningAnalytics'
      ];

      const tableSizes = await Promise.all(
        tables.map(async (table) => {
          try {
            // Check if the model exists in Prisma client
            const modelName = table.charAt(0).toLowerCase() + table.slice(1);
            if (!(prisma as any)[modelName]) {
              return { table, count: 0, error: 'Model not found in Prisma client' };
            }
            const count = await (prisma as any)[modelName].count();
            return { table, count };
          } catch (error) {
            return { table, count: 0, error: 'Table not found or accessible' };
          }
        })
      );

      return tableSizes.sort((a, b) => b.count - a.count);

    } catch (error) {
      console.error('Error getting table sizes:', error);
      return [];
    }
  }

  // Connection pool monitoring
  async getConnectionPoolStats(): Promise<any> {
    try {
      const poolStats = connectionPool.getStats();
      const config = dbConfigManager.getDatabaseConfig();

      return {
        // Current statistics
        totalConnections: poolStats.totalConnections,
        activeConnections: poolStats.activeConnections,
        idleConnections: poolStats.idleConnections,
        waitingRequests: poolStats.waitingRequests,

        // Performance metrics
        successfulConnections: poolStats.successfulConnections,
        failedConnections: poolStats.failedConnections,
        averageConnectionTime: poolStats.averageConnectionTime,
        peakConnections: poolStats.peakConnections,

        // Configuration
        maxConnections: config.maxConnections,
        minConnections: config.minConnections,
        connectionTimeout: config.connectionTimeout,
        idleTimeout: config.idleTimeout,

        // Health
        lastHealthCheck: poolStats.lastHealthCheck,
        uptime: poolStats.uptime,

        // Recommendations
        recommendations: connectionPool.getRecommendations()
      };
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Performance recommendations
  async getPerformanceRecommendations(): Promise<string[]> {
    const recommendations: string[] = [];

    try {
      const stats = await this.getDatabaseStats();
      const queryStats = queryOptimizer.getStats();
      const connectionRecommendations = connectionPool.getRecommendations();

      // Add connection pool recommendations
      recommendations.push(...connectionRecommendations);

      // Check for slow queries
      if (stats.avgQueryTime > 500) {
        recommendations.push('Average query time is high. Consider optimizing slow queries or adding indexes.');
      }

      if (stats.slowQueries.length > 5) {
        recommendations.push('Multiple slow queries detected. Review query patterns and index usage.');
      }

      // Cache performance recommendations
      if (stats.cache.hitRate < 50) {
        recommendations.push('Low cache hit rate detected. Consider increasing cache TTL or optimizing cache keys.');
      }

      if (stats.cache.size > stats.cache.maxSize * 0.9) {
        recommendations.push('Cache is near capacity. Consider increasing cache size or implementing better eviction policies.');
      }

      // Connection pool recommendations
      if (stats.connectionPool.failedConnections > stats.connectionPool.successfulConnections * 0.1) {
        recommendations.push('High connection failure rate. Check database connectivity and pool configuration.');
      }

      if (stats.connectionPool.averageConnectionTime > 1000) {
        recommendations.push('High average connection time. Consider optimizing connection pool settings.');
      }

      // Query optimization recommendations
      if (queryStats.cacheHitRate < 30) {
        recommendations.push('Low query cache hit rate. Review caching strategy for read operations.');
      }

      if (queryStats.slowQueries.length > 10) {
        recommendations.push('Multiple slow queries detected. Consider adding database indexes or optimizing queries.');
      }

      // Check data volume
      if (stats.totalUsers > 10000) {
        recommendations.push('Large user base detected. Consider implementing read replicas for better performance.');
      }

      if (stats.totalLearningResources > 5000) {
        recommendations.push('Large learning resource catalog. Consider implementing search indexes and caching.');
      }

      if (stats.activeEnrollments > 1000) {
        recommendations.push('High enrollment activity. Monitor learning path performance and consider optimization.');
      }

      // Configuration recommendations
      const config = dbConfigManager.getConfigSummary();
      if (config.database.maxConnections < 20 && stats.totalUsers > 1000) {
        recommendations.push('Consider increasing max connections for better concurrent user support.');
      }

      if (!config.cache.enabled) {
        recommendations.push('Query caching is disabled. Enable caching for better read performance.');
      }

      // General recommendations
      recommendations.push('Regularly run database optimization (ANALYZE/VACUUM) for better performance.');
      recommendations.push('Monitor query performance and add indexes for frequently accessed data patterns.');
      recommendations.push('Consider implementing Redis for distributed caching in production.');

      return recommendations;

    } catch (error) {
      return ['Error generating recommendations. Check database connectivity.'];
    }
  }

  // Clear query metrics
  clearMetrics(): void {
    this.queryMetrics = [];
  }

  // Get recent query metrics
  getRecentMetrics(limit: number = 50): QueryPerformanceMetrics[] {
    return this.queryMetrics.slice(-limit);
  }

  // Get comprehensive performance report
  async getPerformanceReport(): Promise<{
    summary: {
      status: 'excellent' | 'good' | 'fair' | 'poor';
      score: number;
      issues: string[];
    };
    database: DatabaseStats;
    queryOptimization: any;
    connectionPool: any;
    recommendations: string[];
  }> {
    try {
      const stats = await this.getDatabaseStats();
      const queryStats = queryOptimizer.getStats();
      const poolStats = await this.getConnectionPoolStats();
      const recommendations = await this.getPerformanceRecommendations();

      // Calculate performance score
      let score = 100;
      const issues: string[] = [];

      // Deduct points for performance issues
      if (stats.avgQueryTime > 1000) {
        score -= 20;
        issues.push('High average query time');
      }

      if (stats.connectionPool.failedConnections > 0) {
        score -= 15;
        issues.push('Connection failures detected');
      }

      if (stats.cache.hitRate < 50) {
        score -= 10;
        issues.push('Low cache hit rate');
      }

      if (stats.slowQueries.length > 5) {
        score -= 15;
        issues.push('Multiple slow queries');
      }

      // Determine status
      let status: 'excellent' | 'good' | 'fair' | 'poor';
      if (score >= 90) status = 'excellent';
      else if (score >= 75) status = 'good';
      else if (score >= 60) status = 'fair';
      else status = 'poor';

      return {
        summary: {
          status,
          score,
          issues,
        },
        database: stats,
        queryOptimization: queryStats,
        connectionPool: poolStats,
        recommendations,
      };
    } catch (error) {
      return {
        summary: {
          status: 'poor',
          score: 0,
          issues: ['Failed to generate performance report'],
        },
        database: {} as DatabaseStats,
        queryOptimization: {},
        connectionPool: {},
        recommendations: ['Check database connectivity'],
      };
    }
  }

  // Auto-optimize database configuration
  async autoOptimize(): Promise<{
    success: boolean;
    message: string;
    optimizations: string[];
  }> {
    const optimizations: string[] = [];

    try {
      // Apply performance indexes
      const indexResult = await this.applyPerformanceIndexes();
      if (indexResult.success) {
        optimizations.push('Applied performance indexes');
      }

      // Optimize database
      const optimizeResult = await this.optimizeDatabase();
      if (optimizeResult.success) {
        optimizations.push('Ran database optimization (ANALYZE/VACUUM)');
      }

      // Clear old metrics
      this.clearMetrics();
      queryOptimizer.clearMetrics();
      optimizations.push('Cleared old performance metrics');

      // Get configuration recommendations
      const configOptimizations = connectionPool.optimizeConfiguration();
      if (Object.keys(configOptimizations).length > 0) {
        optimizations.push('Generated configuration optimization recommendations');
      }

      return {
        success: true,
        message: `Auto-optimization completed with ${optimizations.length} optimizations applied`,
        optimizations,
      };
    } catch (error) {
      return {
        success: false,
        message: `Auto-optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        optimizations,
      };
    }
  }

  // Get real-time performance metrics
  getRealTimeMetrics(): {
    queries: {
      total: number;
      recent: QueryPerformanceMetrics[];
      averageTime: number;
    };
    connections: {
      active: number;
      total: number;
      health: string;
    };
    cache: {
      hitRate: number;
      size: number;
      memory: string;
    };
  } {
    const queryStats = queryOptimizer.getStats();
    const connectionStats = connectionPool.getStats();
    const cacheStats = queryOptimizer.getCacheStats();
    const recentMetrics = this.getRecentMetrics(10);

    return {
      queries: {
        total: queryStats.totalQueries,
        recent: recentMetrics,
        averageTime: queryStats.averageExecutionTime,
      },
      connections: {
        active: connectionStats.activeConnections,
        total: connectionStats.totalConnections,
        health: connectionStats.failedConnections === 0 ? 'healthy' : 'degraded',
      },
      cache: {
        hitRate: cacheStats.hitRate,
        size: cacheStats.size,
        memory: cacheStats.memoryUsage,
      },
    };
  }
}

// Export singleton instance
export const dbOptimization = new DatabaseOptimizationService();

// Prisma middleware to track query performance
export function setupQueryTracking() {
  prisma.$use(async (params, next) => {
    const start = Date.now();
    const result = await next(params);
    const duration = Date.now() - start;
    
    // Track the query
    const queryInfo = `${params.model}.${params.action}`;
    await dbOptimization.trackQuery(queryInfo, duration);
    
    return result;
  });
}

export default dbOptimization;
