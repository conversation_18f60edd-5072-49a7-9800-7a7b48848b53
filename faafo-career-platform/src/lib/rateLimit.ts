import { NextRequest, NextResponse } from 'next/server';
import { getRedisClient, isRedisAvailable, executeRedisCommand } from './redis';

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

// In-memory store for rate limiting fallback
const store: RateLimitStore = {};

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  message?: string;
  keyGenerator?: (request: NextRequest) => string;
}

/**
 * Redis-based rate limiting function
 */
async function redisRateLimit(
  key: string,
  windowMs: number,
  maxRequests: number,
  message: string
): Promise<NextResponse | null> {
  return executeRedisCommand(async (redis) => {
    const now = Date.now();
    const redisKey = `rate_limit_v2:${key}`;

    // Get current data
    const data = await redis.hmget(redisKey, 'count', 'resetTime');
    let count = parseInt(data[0] || '0');
    let resetTime = parseInt(data[1] || '0');

    // Reset if window expired
    if (now > resetTime) {
      count = 0;
      resetTime = now + windowMs;
    }

    // Increment count
    count++;

    // Update Redis with new count and set expiration
    await redis.hmset(redisKey, 'count', count.toString(), 'resetTime', resetTime.toString());
    await redis.pexpire(redisKey, windowMs);

    // Check if limit exceeded
    if (count > maxRequests) {
      return NextResponse.json(
        { error: message },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': resetTime.toString(),
            'X-RateLimit-Backend': 'redis',
            'Retry-After': Math.ceil((resetTime - now) / 1000).toString()
          }
        }
      );
    }

    return null; // Continue to next middleware/handler
  });
}

/**
 * Memory-based rate limiting function (fallback)
 */
function memoryRateLimit(
  key: string,
  windowMs: number,
  maxRequests: number,
  message: string
): NextResponse | null {
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean up old entries
  Object.keys(store).forEach(storeKey => {
    if (store[storeKey].resetTime < windowStart) {
      delete store[storeKey];
    }
  });

  // Get or create entry for this key
  if (!store[key]) {
    store[key] = {
      count: 0,
      resetTime: now + windowMs
    };
  }

  const entry = store[key];

  // Reset if window has expired
  if (entry.resetTime < now) {
    entry.count = 0;
    entry.resetTime = now + windowMs;
  }

  // Increment count
  entry.count++;

  // Check if limit exceeded
  if (entry.count > maxRequests) {
    return NextResponse.json(
      { error: message },
      {
        status: 429,
        headers: {
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': entry.resetTime.toString(),
          'X-RateLimit-Backend': 'memory',
          'Retry-After': Math.ceil((entry.resetTime - now) / 1000).toString()
        }
      }
    );
  }

  return null; // Continue to next middleware/handler
}

export function rateLimit(config: RateLimitConfig) {
  const {
    windowMs,
    maxRequests,
    message = 'Too many requests',
    keyGenerator = (req) => getClientIP(req) || 'unknown'
  } = config;

  return async (request: NextRequest): Promise<NextResponse | null> => {
    const key = keyGenerator(request);

    // Try Redis first if available
    if (isRedisAvailable()) {
      try {
        const result = await redisRateLimit(key, windowMs, maxRequests, message);
        if (result !== null) {
          return result;
        }
      } catch (error) {
        console.warn('Redis rate limiting failed, falling back to memory:', error);
      }
    }

    // Fallback to memory-based rate limiting
    return memoryRateLimit(key, windowMs, maxRequests, message);
  };
}

function getClientIP(request: NextRequest): string | null {
  // Try various headers for client IP
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to connection remote address (may not be available in all environments)
  return (request as any).ip || null;
}

// Predefined rate limit configurations
export const rateLimitConfigs = {
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    message: 'Too many authentication attempts. Please try again later.'
  },
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    message: 'Too many API requests. Please try again later.'
  },
  contact: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many contact form submissions. Please try again later.'
  },
  signup: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many signup attempts. Please try again later.'
  }
} as const;

// Helper function to apply rate limiting to API routes
export async function withRateLimit(
  request: NextRequest,
  config: RateLimitConfig,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  const rateLimitResponse = await rateLimit(config)(request);
  
  if (rateLimitResponse) {
    return rateLimitResponse;
  }
  
  return handler();
}

// CSRF Protection
export function generateCSRFToken(): string {
  return crypto.randomUUID();
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  // In a real implementation, you'd store CSRF tokens in session/database
  // For now, we'll use a simple validation
  return token === sessionToken;
}

// Input sanitization helpers
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .trim()
    .slice(0, 1000); // Limit length
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

// SQL injection prevention (Prisma handles this, but good to have)
export function escapeSQLString(str: string): string {
  return str.replace(/'/g, "''");
}

// XSS prevention helpers
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[@$!%*?&]/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Session security helpers
export function generateSecureSessionId(): string {
  return crypto.randomUUID() + '-' + Date.now().toString(36);
}

export function isValidSessionId(sessionId: string): boolean {
  // Basic validation for session ID format
  return /^[a-f0-9-]+$/i.test(sessionId) && sessionId.length > 20;
}
