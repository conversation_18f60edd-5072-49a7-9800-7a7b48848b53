/**
 * Enterprise-Level Performance Optimization System
 * Advanced caching, optimization, and performance monitoring
 */

import { Redis } from 'ioredis';
import { performance } from 'perf_hooks';
import { LRUCache } from 'lru-cache';

// Performance configuration
interface PerformanceConfig {
  cache: {
    redis: {
      enabled: boolean;
      url?: string;
      keyPrefix: string;
      defaultTTL: number;
    };
    memory: {
      enabled: boolean;
      maxSize: number;
      maxAge: number;
    };
  };
  optimization: {
    compression: boolean;
    minification: boolean;
    imageOptimization: boolean;
    lazyLoading: boolean;
  };
  monitoring: {
    enabled: boolean;
    sampleRate: number;
    slowQueryThreshold: number;
  };
}

// Performance metrics
interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percent';
  timestamp: number;
  tags?: Record<string, string>;
}

// Cache entry interface
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

// Database query performance tracking
interface QueryPerformance {
  query: string;
  duration: number;
  timestamp: number;
  success: boolean;
  rowCount?: number;
}

class EnterprisePerformance {
  private config: PerformanceConfig;
  private redisClient?: Redis;
  private memoryCache!: LRUCache<string, CacheEntry>;
  private metrics: PerformanceMetric[] = [];
  private queryPerformance: QueryPerformance[] = [];
  private performanceObserver?: PerformanceObserver;

  constructor() {
    this.config = this.getPerformanceConfig();
    this.initializeCache();
    this.setupPerformanceMonitoring();
  }

  /**
   * Multi-tier caching system (Redis + Memory)
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();
    
    try {
      // Try memory cache first
      if (this.config.cache.memory.enabled) {
        const memoryResult = this.memoryCache.get(key);
        if (memoryResult && this.isValidCacheEntry(memoryResult)) {
          memoryResult.hits++;
          this.trackMetric('cache_hit', 1, 'count', { tier: 'memory', key });
          return memoryResult.data;
        }
      }

      // Try Redis cache
      if (this.config.cache.redis.enabled && this.redisClient) {
        const redisResult = await this.redisClient.get(this.getCacheKey(key));
        if (redisResult) {
          const parsed = JSON.parse(redisResult) as CacheEntry<T>;
          if (this.isValidCacheEntry(parsed)) {
            // Store in memory cache for faster access
            if (this.config.cache.memory.enabled) {
              this.memoryCache.set(key, parsed);
            }
            this.trackMetric('cache_hit', 1, 'count', { tier: 'redis', key });
            return parsed.data;
          }
        }
      }

      this.trackMetric('cache_miss', 1, 'count', { key });
      return null;
    } finally {
      const duration = performance.now() - startTime;
      this.trackMetric('cache_operation_duration', duration, 'ms', { operation: 'get', key });
    }
  }

  /**
   * Set cache with automatic tier management
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const startTime = performance.now();
    const cacheTTL = ttl || this.config.cache.redis.defaultTTL;
    
    const cacheEntry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: cacheTTL,
      hits: 0
    };

    try {
      // Store in memory cache
      if (this.config.cache.memory.enabled) {
        this.memoryCache.set(key, cacheEntry);
      }

      // Store in Redis cache
      if (this.config.cache.redis.enabled && this.redisClient) {
        await this.redisClient.setex(
          this.getCacheKey(key),
          Math.floor(cacheTTL / 1000),
          JSON.stringify(cacheEntry)
        );
      }

      this.trackMetric('cache_set', 1, 'count', { key });
    } finally {
      const duration = performance.now() - startTime;
      this.trackMetric('cache_operation_duration', duration, 'ms', { operation: 'set', key });
    }
  }

  /**
   * Invalidate cache entries
   */
  async invalidate(pattern: string): Promise<void> {
    const startTime = performance.now();

    try {
      // Clear from memory cache
      if (this.config.cache.memory.enabled) {
        const keysToDelete: string[] = [];
        this.memoryCache.forEach((value, key) => {
          if (key.includes(pattern)) {
            keysToDelete.push(key);
          }
        });
        keysToDelete.forEach(key => this.memoryCache.delete(key));
      }

      // Clear from Redis cache
      if (this.config.cache.redis.enabled && this.redisClient) {
        const keys = await this.redisClient.keys(this.getCacheKey(`*${pattern}*`));
        if (keys.length > 0) {
          await this.redisClient.del(...keys);
        }
      }

      this.trackMetric('cache_invalidation', 1, 'count', { pattern });
    } finally {
      const duration = performance.now() - startTime;
      this.trackMetric('cache_operation_duration', duration, 'ms', { operation: 'invalidate', pattern });
    }
  }

  /**
   * Database query performance wrapper
   */
  async trackDatabaseQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    query?: string
  ): Promise<T> {
    const startTime = performance.now();
    let success = false;
    let result: T;
    let rowCount: number | undefined;

    try {
      result = await queryFn();
      success = true;
      
      // Try to determine row count
      if (Array.isArray(result)) {
        rowCount = result.length;
      } else if (result && typeof result === 'object' && 'count' in result) {
        rowCount = (result as any).count;
      }

      return result;
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const duration = performance.now() - startTime;
      
      // Track query performance
      this.queryPerformance.push({
        query: query || queryName,
        duration,
        timestamp: Date.now(),
        success,
        rowCount
      });

      // Track metrics
      this.trackMetric('db_query_duration', duration, 'ms', { 
        query: queryName, 
        success: success.toString() 
      });

      // Alert on slow queries
      if (duration > this.config.monitoring.slowQueryThreshold) {
        this.trackMetric('slow_query', 1, 'count', { 
          query: queryName, 
          duration: duration.toString() 
        });
      }

      // Keep only last 1000 query performance records
      if (this.queryPerformance.length > 1000) {
        this.queryPerformance = this.queryPerformance.slice(-1000);
      }
    }
  }

  /**
   * API response time tracking
   */
  trackAPIResponse(endpoint: string, method: string, statusCode: number, duration: number) {
    this.trackMetric('api_response_time', duration, 'ms', {
      endpoint,
      method,
      status: statusCode.toString(),
      success: (statusCode < 400).toString()
    });

    // Track error rates
    if (statusCode >= 400) {
      this.trackMetric('api_error_rate', 1, 'count', {
        endpoint,
        method,
        status: statusCode.toString()
      });
    }
  }

  /**
   * Memory usage monitoring
   */
  trackMemoryUsage() {
    const memUsage = process.memoryUsage();
    
    this.trackMetric('memory_heap_used', memUsage.heapUsed, 'bytes');
    this.trackMetric('memory_heap_total', memUsage.heapTotal, 'bytes');
    this.trackMetric('memory_external', memUsage.external, 'bytes');
    this.trackMetric('memory_rss', memUsage.rss, 'bytes');
    
    // Calculate memory usage percentage
    const heapUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    this.trackMetric('memory_usage_percent', heapUsagePercent, 'percent');
  }

  /**
   * Cache performance analytics
   */
  getCacheAnalytics(): {
    hitRate: number;
    missRate: number;
    totalOperations: number;
    averageResponseTime: number;
    memoryUsage: number;
    redisConnected: boolean;
  } {
    const cacheHits = this.metrics.filter(m => m.name === 'cache_hit').length;
    const cacheMisses = this.metrics.filter(m => m.name === 'cache_miss').length;
    const totalOperations = cacheHits + cacheMisses;
    
    const responseTimeMetrics = this.metrics.filter(m => m.name === 'cache_operation_duration');
    const averageResponseTime = responseTimeMetrics.length > 0
      ? responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length
      : 0;

    return {
      hitRate: totalOperations > 0 ? (cacheHits / totalOperations) * 100 : 0,
      missRate: totalOperations > 0 ? (cacheMisses / totalOperations) * 100 : 0,
      totalOperations,
      averageResponseTime,
      memoryUsage: this.memoryCache.size,
      redisConnected: !!this.redisClient?.status && this.redisClient.status === 'ready'
    };
  }

  /**
   * Database performance analytics
   */
  getDatabaseAnalytics(): {
    averageQueryTime: number;
    slowQueries: number;
    totalQueries: number;
    successRate: number;
    topSlowQueries: QueryPerformance[];
  } {
    const totalQueries = this.queryPerformance.length;
    const successfulQueries = this.queryPerformance.filter(q => q.success).length;
    const slowQueries = this.queryPerformance.filter(
      q => q.duration > this.config.monitoring.slowQueryThreshold
    ).length;

    const averageQueryTime = totalQueries > 0
      ? this.queryPerformance.reduce((sum, q) => sum + q.duration, 0) / totalQueries
      : 0;

    const topSlowQueries = this.queryPerformance
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    return {
      averageQueryTime,
      slowQueries,
      totalQueries,
      successRate: totalQueries > 0 ? (successfulQueries / totalQueries) * 100 : 0,
      topSlowQueries
    };
  }

  /**
   * Performance optimization recommendations
   */
  getOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const cacheAnalytics = this.getCacheAnalytics();
    const dbAnalytics = this.getDatabaseAnalytics();

    // Cache recommendations
    if (cacheAnalytics.hitRate < 70) {
      recommendations.push('Consider increasing cache TTL or improving cache key strategies');
    }

    if (cacheAnalytics.averageResponseTime > 10) {
      recommendations.push('Cache response times are high - consider optimizing cache storage');
    }

    // Database recommendations
    if (dbAnalytics.averageQueryTime > 100) {
      recommendations.push('Database queries are slow - consider adding indexes or optimizing queries');
    }

    if (dbAnalytics.slowQueries > dbAnalytics.totalQueries * 0.1) {
      recommendations.push('High number of slow queries detected - review and optimize database schema');
    }

    // Memory recommendations
    const memoryMetrics = this.metrics.filter(m => m.name === 'memory_usage_percent');
    const latestMemoryUsage = memoryMetrics[memoryMetrics.length - 1]?.value || 0;
    
    if (latestMemoryUsage > 80) {
      recommendations.push('High memory usage detected - consider implementing memory optimization strategies');
    }

    return recommendations;
  }

  /**
   * Private helper methods
   */
  private getPerformanceConfig(): PerformanceConfig {
    return {
      cache: {
        redis: {
          enabled: !!process.env.REDIS_URL,
          url: process.env.REDIS_URL,
          keyPrefix: process.env.CACHE_KEY_PREFIX || 'faafo:',
          defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL || '3600000') // 1 hour
        },
        memory: {
          enabled: true,
          maxSize: parseInt(process.env.MEMORY_CACHE_MAX_SIZE || '1000'),
          maxAge: parseInt(process.env.MEMORY_CACHE_MAX_AGE || '300000') // 5 minutes
        }
      },
      optimization: {
        compression: process.env.ENABLE_COMPRESSION !== 'false',
        minification: process.env.NODE_ENV === 'production',
        imageOptimization: process.env.ENABLE_IMAGE_OPTIMIZATION !== 'false',
        lazyLoading: process.env.ENABLE_LAZY_LOADING !== 'false'
      },
      monitoring: {
        enabled: process.env.NODE_ENV === 'production',
        sampleRate: parseFloat(process.env.PERFORMANCE_SAMPLE_RATE || '0.1'),
        slowQueryThreshold: parseInt(process.env.SLOW_QUERY_THRESHOLD || '1000') // 1 second
      }
    };
  }

  private initializeCache(): void {
    // Initialize memory cache
    this.memoryCache = new LRUCache({
      max: this.config.cache.memory.maxSize,
      ttl: this.config.cache.memory.maxAge
    });

    // Initialize Redis cache
    if (this.config.cache.redis.enabled && this.config.cache.redis.url) {
      this.redisClient = new Redis(this.config.cache.redis.url, {
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      this.redisClient.on('error', (error) => {
        console.error('Redis connection error:', error);
        this.trackMetric('redis_error', 1, 'count', { error: error.message });
      });

      this.redisClient.on('connect', () => {
        console.log('Redis connected successfully');
        this.trackMetric('redis_connection', 1, 'count', { status: 'connected' });
      });
    }
  }

  private setupPerformanceMonitoring(): void {
    if (!this.config.monitoring.enabled) return;

    // Monitor memory usage every 30 seconds
    setInterval(() => {
      this.trackMemoryUsage();
    }, 30000);

    // Setup performance observer for Web APIs
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.trackMetric('web_performance', entry.duration, 'ms', {
            type: entry.entryType,
            name: entry.name
          });
        }
      });

      this.performanceObserver.observe({ entryTypes: ['navigation', 'resource', 'measure'] });
    }
  }

  private trackMetric(name: string, value: number, unit: PerformanceMetric['unit'], tags?: Record<string, string>): void {
    if (!this.config.monitoring.enabled) return;

    // Sample metrics based on sample rate
    if (Math.random() > this.config.monitoring.sampleRate) return;

    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      tags
    };

    this.metrics.push(metric);

    // Keep only last 5000 metrics in memory
    if (this.metrics.length > 5000) {
      this.metrics = this.metrics.slice(-5000);
    }
  }

  private getCacheKey(key: string): string {
    return `${this.config.cache.redis.keyPrefix}${key}`;
  }

  private isValidCacheEntry(entry: CacheEntry): boolean {
    const now = Date.now();
    return (now - entry.timestamp) < entry.ttl;
  }
}

// Export singleton instance
export const enterprisePerformance = new EnterprisePerformance();

// Export types
export type { PerformanceConfig, PerformanceMetric, QueryPerformance };
