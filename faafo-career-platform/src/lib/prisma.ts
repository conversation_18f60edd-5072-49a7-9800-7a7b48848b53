import { PrismaClient } from "@prisma/client";

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

/**
 * Enhanced Prisma Client Configuration with Connection Optimization
 * Implements enterprise-grade connection pooling, timeouts, and monitoring
 */
const createPrismaClient = () => {
  // Only create Prisma client on server side
  if (typeof window !== 'undefined') {
    throw new Error('Prisma client should only be used on the server side');
  }

  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Enhanced logging configuration
  const logConfig = isDevelopment
    ? ['query', 'info', 'warn', 'error'] as const
    : ['warn', 'error'] as const;

  // Connection pool configuration
  const connectionLimit = parseInt(process.env.DB_MAX_CONNECTIONS || '20');
  const connectionTimeout = parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000');
  const queryTimeout = parseInt(process.env.DB_QUERY_TIMEOUT || '5000');

  // Build optimized database URL with connection parameters
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error('DATABASE_URL environment variable is required');
  }

  // Parse and enhance connection string for optimal performance
  const url = new URL(databaseUrl);

  // Add connection pooling parameters for PostgreSQL
  if (url.protocol === 'postgres:' || url.protocol === 'postgresql:') {
    // Connection pool settings
    url.searchParams.set('connection_limit', connectionLimit.toString());
    url.searchParams.set('pool_timeout', '10');
    url.searchParams.set('connect_timeout', (connectionTimeout / 1000).toString());

    // Performance optimizations
    url.searchParams.set('statement_timeout', queryTimeout.toString());
    url.searchParams.set('idle_in_transaction_session_timeout', '30000');

    // SSL configuration for production
    if (isProduction && !url.searchParams.has('sslmode')) {
      url.searchParams.set('sslmode', 'require');
    }

    // Connection optimization
    url.searchParams.set('application_name', 'faafo-career-platform');
    // Note: Neon doesn't support default_transaction_isolation parameter
  }

  return new PrismaClient({
    log: logConfig as any,
    datasources: {
      db: {
        url: url.toString(),
      },
    },
    // Enhanced error formatting
    errorFormat: isDevelopment ? 'pretty' : 'minimal',
  });
};

const prisma = globalForPrisma.prisma ?? createPrismaClient();

// Setup comprehensive middleware stack
const setupMiddleware = async () => {
  try {
    // Query optimization middleware
    const { queryOptimizationMiddleware } = await import('./database/query-optimizer');
    prisma.$use(queryOptimizationMiddleware);
    console.log('Query optimization middleware enabled');

    // Query analysis middleware
    const { queryAnalysisMiddleware } = await import('./database/query-analyzer');
    prisma.$use(queryAnalysisMiddleware);
    console.log('Query analysis middleware enabled');

    // Redis cache middleware (if enabled)
    if (process.env.REDIS_CACHE_ENABLED === 'true') {
      const { redisCacheMiddleware } = await import('./database/redis-cache');
      prisma.$use(redisCacheMiddleware);
      console.log('Redis cache middleware enabled');
    }

    // Connection pool monitoring middleware
    const { connectionPool } = await import('./database/connection-pool');
    prisma.$use(async (params, next) => {
      const startTime = Date.now();
      try {
        const result = await next(params);
        connectionPool.recordMetrics({
          connectionTime: Date.now() - startTime,
          queryTime: Date.now() - startTime,
          success: true,
        });
        return result;
      } catch (error) {
        connectionPool.recordMetrics({
          connectionTime: Date.now() - startTime,
          queryTime: Date.now() - startTime,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        throw error;
      }
    });
    console.log('Connection pool monitoring middleware enabled');

  } catch (error) {
    console.warn('Failed to setup middleware:', error);
  }
};

// Initialize middleware
setupMiddleware();

// Connection health monitoring
let connectionHealthy = true;
let lastHealthCheck = Date.now();

/**
 * Check database connection health
 */
export async function checkDatabaseHealth(): Promise<{
  healthy: boolean;
  latency: number;
  error?: string;
}> {
  const start = Date.now();

  try {
    await prisma.$queryRaw`SELECT 1 as health_check`;
    const latency = Date.now() - start;

    connectionHealthy = true;
    lastHealthCheck = Date.now();

    return {
      healthy: true,
      latency,
    };
  } catch (error) {
    connectionHealthy = false;

    return {
      healthy: false,
      latency: Date.now() - start,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get connection status
 */
export function getConnectionStatus() {
  return {
    healthy: connectionHealthy,
    lastHealthCheck,
    timeSinceLastCheck: Date.now() - lastHealthCheck,
  };
}

/**
 * Graceful shutdown handler
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('Database connection closed gracefully');
  } catch (error) {
    console.error('Error during database disconnect:', error);
  }
}

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Setup graceful shutdown
if (typeof process !== 'undefined') {
  process.on('SIGINT', disconnectDatabase);
  process.on('SIGTERM', disconnectDatabase);
  process.on('beforeExit', disconnectDatabase);
}

export { prisma };
export default prisma;