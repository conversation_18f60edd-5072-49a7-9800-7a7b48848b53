/**
 * Enterprise-Level Security Implementation
 * Comprehensive security features for production environments
 */

import { NextRequest, NextResponse } from 'next/server';
import { rateLimit } from 'express-rate-limit';
import helmet from 'helmet';
import { z } from 'zod';
import crypto from 'crypto';

// Security configuration interface
interface SecurityConfig {
  rateLimit: {
    windowMs: number;
    max: number;
    skipSuccessfulRequests: boolean;
  };
  cors: {
    origins: string[];
    methods: string[];
    allowedHeaders: string[];
  };
  csrf: {
    enabled: boolean;
    secret: string;
  };
  encryption: {
    algorithm: string;
    keyLength: number;
  };
  audit: {
    enabled: boolean;
    sensitiveFields: string[];
  };
}

// Security event types
interface SecurityEvent {
  id: string;
  type: 'authentication' | 'authorization' | 'data_access' | 'security_violation' | 'admin_action';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress: string;
  userAgent: string;
  resource: string;
  action: string;
  timestamp: number;
  details: Record<string, any>;
  success: boolean;
}

// Audit log entry
interface AuditLogEntry {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: number;
  success: boolean;
  errorMessage?: string;
}

class EnterpriseSecurity {
  private config: SecurityConfig;
  private securityEvents: SecurityEvent[] = [];
  private auditLogs: AuditLogEntry[] = [];
  private blockedIPs: Set<string> = new Set();
  private suspiciousActivities: Map<string, number> = new Map();

  constructor() {
    this.config = this.getSecurityConfig();
    this.setupSecurityHeaders();
    this.startSecurityMonitoring();
  }

  /**
   * Advanced rate limiting with adaptive thresholds
   */
  createRateLimiter(options?: Partial<SecurityConfig['rateLimit']>) {
    const config = { ...this.config.rateLimit, ...options };
    
    return rateLimit({
      windowMs: config.windowMs,
      max: config.max,
      skipSuccessfulRequests: config.skipSuccessfulRequests,
      handler: (req, res) => {
        const clientIP = this.getClientIP(req);
        this.logSecurityEvent({
          type: 'security_violation',
          severity: 'medium',
          ipAddress: clientIP,
          userAgent: req.headers['user-agent'] || 'unknown',
          resource: req.url || 'unknown',
          action: 'rate_limit_exceeded',
          details: { limit: config.max, window: config.windowMs },
          success: false
        });

        res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.ceil(config.windowMs / 1000)
        });
      },
      keyGenerator: (req) => {
        return this.getClientIP(req);
      }
    });
  }

  /**
   * Input validation and sanitization
   */
  validateAndSanitizeInput<T>(schema: z.ZodSchema<T>, data: unknown): T {
    try {
      // Sanitize input data
      const sanitized = this.sanitizeInput(data);
      
      // Validate with Zod schema
      const validated = schema.parse(sanitized);
      
      return validated;
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation failed: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  }

  /**
   * Data encryption for sensitive information
   */
  encryptSensitiveData(data: string, key?: string): string {
    const encryptionKey = key || process.env.ENCRYPTION_KEY || this.generateEncryptionKey();
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.config.encryption.algorithm, encryptionKey);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * Data decryption
   */
  decryptSensitiveData(encryptedData: string, key?: string): string {
    const encryptionKey = key || process.env.ENCRYPTION_KEY || this.generateEncryptionKey();
    const [ivHex, encrypted] = encryptedData.split(':');
    
    const decipher = crypto.createDecipher(this.config.encryption.algorithm, encryptionKey);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Comprehensive audit logging
   */
  logAuditEvent(event: Omit<AuditLogEntry, 'id' | 'timestamp'>): void {
    const auditEntry: AuditLogEntry = {
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      ...event
    };

    this.auditLogs.push(auditEntry);
    
    // Persist to database or external audit service
    this.persistAuditLog(auditEntry);
    
    // Keep only last 10000 entries in memory
    if (this.auditLogs.length > 10000) {
      this.auditLogs = this.auditLogs.slice(-10000);
    }
  }

  /**
   * Security event logging
   */
  logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      ...event
    };

    this.securityEvents.push(securityEvent);
    
    // Check for suspicious patterns
    this.analyzeSuspiciousActivity(securityEvent);
    
    // Alert on critical events
    if (securityEvent.severity === 'critical') {
      this.triggerSecurityAlert(securityEvent);
    }
    
    // Persist to security monitoring system
    this.persistSecurityEvent(securityEvent);
    
    // Keep only last 5000 events in memory
    if (this.securityEvents.length > 5000) {
      this.securityEvents = this.securityEvents.slice(-5000);
    }
  }

  /**
   * IP blocking and threat detection
   */
  blockIP(ipAddress: string, reason: string, duration?: number): void {
    this.blockedIPs.add(ipAddress);
    
    this.logSecurityEvent({
      type: 'security_violation',
      severity: 'high',
      ipAddress,
      userAgent: 'system',
      resource: 'security_system',
      action: 'ip_blocked',
      details: { reason, duration },
      success: true
    });

    // Auto-unblock after duration if specified
    if (duration) {
      setTimeout(() => {
        this.unblockIP(ipAddress);
      }, duration);
    }
  }

  /**
   * Check if IP is blocked
   */
  isIPBlocked(ipAddress: string): boolean {
    return this.blockedIPs.has(ipAddress);
  }

  /**
   * Unblock IP address
   */
  unblockIP(ipAddress: string): void {
    this.blockedIPs.delete(ipAddress);
    
    this.logSecurityEvent({
      type: 'security_violation',
      severity: 'low',
      ipAddress,
      userAgent: 'system',
      resource: 'security_system',
      action: 'ip_unblocked',
      details: {},
      success: true
    });
  }

  /**
   * CSRF token generation and validation
   */
  generateCSRFToken(sessionId: string): string {
    const token = crypto.randomBytes(32).toString('hex');
    const signature = crypto
      .createHmac('sha256', this.config.csrf.secret)
      .update(token + sessionId)
      .digest('hex');
    
    return `${token}.${signature}`;
  }

  /**
   * Validate CSRF token
   */
  validateCSRFToken(token: string, sessionId: string): boolean {
    try {
      const [tokenPart, signature] = token.split('.');
      const expectedSignature = crypto
        .createHmac('sha256', this.config.csrf.secret)
        .update(tokenPart + sessionId)
        .digest('hex');
      
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Security middleware for Next.js
   */
  securityMiddleware() {
    return async (request: NextRequest) => {
      const clientIP = this.getClientIP(request);
      
      // Check if IP is blocked
      if (this.isIPBlocked(clientIP)) {
        return new NextResponse('Access Denied', { status: 403 });
      }

      // Add security headers
      const response = NextResponse.next();
      this.addSecurityHeaders(response);
      
      return response;
    };
  }

  /**
   * Private helper methods
   */
  private getSecurityConfig(): SecurityConfig {
    return {
      rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
        max: parseInt(process.env.RATE_LIMIT_MAX || '100'),
        skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESS === 'true'
      },
      cors: {
        origins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token']
      },
      csrf: {
        enabled: process.env.CSRF_ENABLED !== 'false',
        secret: process.env.CSRF_SECRET || crypto.randomBytes(32).toString('hex')
      },
      encryption: {
        algorithm: 'aes-256-cbc',
        keyLength: 32
      },
      audit: {
        enabled: process.env.AUDIT_ENABLED !== 'false',
        sensitiveFields: ['password', 'token', 'secret', 'key', 'ssn', 'creditCard']
      }
    };
  }

  private setupSecurityHeaders(): void {
    // Security headers configuration will be applied via middleware
  }

  private addSecurityHeaders(response: NextResponse): void {
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    response.headers.set('Content-Security-Policy', this.getCSPHeader());
  }

  private getCSPHeader(): string {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https:",
      "connect-src 'self' https://api.gemini.com https://vercel.live",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ');
  }

  private sanitizeInput(data: unknown): unknown {
    if (typeof data === 'string') {
      // Remove potentially dangerous characters
      return data
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeInput(item));
    }
    
    if (data && typeof data === 'object') {
      const sanitized: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeInput(value);
      }
      return sanitized;
    }
    
    return data;
  }

  private getClientIP(request: any): string {
    return (
      request.headers['x-forwarded-for']?.split(',')[0] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      'unknown'
    );
  }

  private generateEncryptionKey(): string {
    return crypto.randomBytes(this.config.encryption.keyLength).toString('hex');
  }

  private analyzeSuspiciousActivity(event: SecurityEvent): void {
    const key = `${event.ipAddress}:${event.type}`;
    const count = this.suspiciousActivities.get(key) || 0;
    this.suspiciousActivities.set(key, count + 1);

    // Block IP if too many suspicious activities
    if (count > 10) {
      this.blockIP(event.ipAddress, 'Suspicious activity detected', 3600000); // 1 hour
    }
  }

  private async triggerSecurityAlert(event: SecurityEvent): Promise<void> {
    console.error('🚨 CRITICAL SECURITY EVENT:', event);
    
    // Send to monitoring system
    // Implement integration with your alerting system
  }

  private async persistAuditLog(entry: AuditLogEntry): Promise<void> {
    // Implement database persistence
    console.log('Audit log entry:', entry);
  }

  private async persistSecurityEvent(event: SecurityEvent): Promise<void> {
    // Implement security event persistence
    console.log('Security event:', event);
  }

  private startSecurityMonitoring(): void {
    // Clean up old suspicious activities every hour
    setInterval(() => {
      this.suspiciousActivities.clear();
    }, 3600000);
  }
}

// Export singleton instance
export const enterpriseSecurity = new EnterpriseSecurity();

// Export types
export type { SecurityConfig, SecurityEvent, AuditLogEntry };
