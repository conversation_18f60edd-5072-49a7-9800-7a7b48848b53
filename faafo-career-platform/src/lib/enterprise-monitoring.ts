/**
 * Enterprise-Level Monitoring and Observability System
 * Provides comprehensive monitoring, alerting, and observability for production environments
 */

import { performance } from 'perf_hooks';
import * as Sentry from '@sentry/nextjs';

// Types for monitoring
interface MetricData {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

interface AlertRule {
  id: string;
  name: string;
  condition: (metric: MetricData) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cooldown: number; // minutes
  channels: string[]; // notification channels
}

interface HealthCheck {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  details?: Record<string, any>;
  timestamp: number;
}

class EnterpriseMonitoring {
  private metrics: MetricData[] = [];
  private alerts: AlertRule[] = [];
  private lastAlertTimes: Map<string, number> = new Map();
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'production';
    this.setupDefaultAlerts();
    this.startMetricsCollection();
  }

  /**
   * Track custom business metrics
   */
  trackMetric(name: string, value: number, tags?: Record<string, string>, metadata?: Record<string, any>) {
    if (!this.isEnabled) return;

    const metric: MetricData = {
      name,
      value,
      timestamp: Date.now(),
      tags,
      metadata
    };

    this.metrics.push(metric);
    this.evaluateAlerts(metric);
    this.sendToAnalytics(metric);

    // Keep only last 1000 metrics in memory
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Track API performance metrics
   */
  trackAPIPerformance(endpoint: string, method: string, statusCode: number, duration: number, userId?: string) {
    this.trackMetric('api_request', duration, {
      endpoint,
      method,
      status: statusCode.toString(),
      user_id: userId || 'anonymous'
    }, {
      type: 'api_performance',
      success: statusCode < 400
    });

    // Track error rates
    if (statusCode >= 400) {
      this.trackMetric('api_error', 1, {
        endpoint,
        method,
        status: statusCode.toString()
      });
    }
  }

  /**
   * Track database query performance
   */
  trackDatabaseQuery(query: string, duration: number, success: boolean) {
    this.trackMetric('db_query', duration, {
      query_type: this.getQueryType(query),
      success: success.toString()
    }, {
      type: 'database_performance',
      query_hash: this.hashQuery(query)
    });
  }

  /**
   * Track user behavior and engagement
   */
  trackUserEvent(event: string, userId: string, properties?: Record<string, any>) {
    this.trackMetric('user_event', 1, {
      event,
      user_id: userId
    }, {
      type: 'user_behavior',
      properties
    });
  }

  /**
   * Track business KPIs
   */
  trackBusinessMetric(metric: string, value: number, dimension?: Record<string, string>) {
    this.trackMetric('business_kpi', value, {
      kpi: metric,
      ...dimension
    }, {
      type: 'business_intelligence'
    });
  }

  /**
   * Comprehensive health check system
   */
  async performHealthCheck(): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    services: HealthCheck[];
    timestamp: string;
  }> {
    const checks: HealthCheck[] = [];

    // Database health check
    checks.push(await this.checkDatabase());

    // Redis cache health check
    checks.push(await this.checkRedis());

    // External API health checks
    checks.push(await this.checkExternalAPIs());

    // File system health check
    checks.push(await this.checkFileSystem());

    // Memory and CPU health check
    checks.push(await this.checkSystemResources());

    // Determine overall health
    const healthyCount = checks.filter(c => c.status === 'healthy').length;
    const degradedCount = checks.filter(c => c.status === 'degraded').length;
    const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (unhealthyCount > 0) {
      overall = 'unhealthy';
    } else if (degradedCount > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    const result = {
      overall,
      services: checks,
      timestamp: new Date().toISOString()
    };

    // Track health check results
    this.trackMetric('health_check', healthyCount / checks.length, {
      overall_status: overall
    });

    return result;
  }

  /**
   * Real-time alerting system
   */
  private evaluateAlerts(metric: MetricData) {
    for (const alert of this.alerts) {
      if (alert.condition(metric)) {
        const lastAlertTime = this.lastAlertTimes.get(alert.id) || 0;
        const cooldownMs = alert.cooldown * 60 * 1000;

        if (Date.now() - lastAlertTime > cooldownMs) {
          this.triggerAlert(alert, metric);
          this.lastAlertTimes.set(alert.id, Date.now());
        }
      }
    }
  }

  /**
   * Setup default monitoring alerts
   */
  private setupDefaultAlerts() {
    this.alerts = [
      {
        id: 'high_api_response_time',
        name: 'High API Response Time',
        condition: (metric) => metric.name === 'api_request' && metric.value > 2000,
        severity: 'high',
        cooldown: 5,
        channels: ['slack', 'email']
      },
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        condition: (metric) => metric.name === 'api_error' && metric.value > 10,
        severity: 'critical',
        cooldown: 1,
        channels: ['slack', 'email', 'sms']
      },
      {
        id: 'slow_database_queries',
        name: 'Slow Database Queries',
        condition: (metric) => metric.name === 'db_query' && metric.value > 1000,
        severity: 'medium',
        cooldown: 10,
        channels: ['slack']
      },
      {
        id: 'low_user_engagement',
        name: 'Low User Engagement',
        condition: (metric) => metric.name === 'user_event' && metric.value < 0.1,
        severity: 'low',
        cooldown: 60,
        channels: ['email']
      }
    ];
  }

  /**
   * Individual health check methods
   */
  private async checkDatabase(): Promise<HealthCheck> {
    const start = performance.now();
    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();
      await prisma.$queryRaw`SELECT 1`;
      await prisma.$disconnect();
      
      const responseTime = performance.now() - start;
      return {
        name: 'database',
        status: responseTime < 100 ? 'healthy' : responseTime < 500 ? 'degraded' : 'unhealthy',
        responseTime,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'database',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: Date.now()
      };
    }
  }

  private async checkRedis(): Promise<HealthCheck> {
    const start = performance.now();
    try {
      if (!process.env.REDIS_URL) {
        return {
          name: 'redis',
          status: 'degraded',
          responseTime: 0,
          details: { message: 'Redis not configured' },
          timestamp: Date.now()
        };
      }

      // Add Redis health check logic here
      const responseTime = performance.now() - start;
      return {
        name: 'redis',
        status: 'healthy',
        responseTime,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'redis',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: Date.now()
      };
    }
  }

  private async checkExternalAPIs(): Promise<HealthCheck> {
    const start = performance.now();
    try {
      // Check critical external APIs (Gemini AI, email service, etc.)
      const responseTime = performance.now() - start;
      return {
        name: 'external_apis',
        status: 'healthy',
        responseTime,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'external_apis',
        status: 'degraded',
        responseTime: performance.now() - start,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: Date.now()
      };
    }
  }

  private async checkFileSystem(): Promise<HealthCheck> {
    const start = performance.now();
    try {
      const fs = require('fs').promises;
      await fs.access('/tmp', fs.constants.W_OK);
      
      const responseTime = performance.now() - start;
      return {
        name: 'filesystem',
        status: 'healthy',
        responseTime,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'filesystem',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: Date.now()
      };
    }
  }

  private async checkSystemResources(): Promise<HealthCheck> {
    const start = performance.now();
    try {
      const memUsage = process.memoryUsage();
      const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      const responseTime = performance.now() - start;
      return {
        name: 'system_resources',
        status: memUsagePercent < 80 ? 'healthy' : memUsagePercent < 95 ? 'degraded' : 'unhealthy',
        responseTime,
        details: {
          memory_usage_percent: memUsagePercent,
          heap_used: memUsage.heapUsed,
          heap_total: memUsage.heapTotal
        },
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'system_resources',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
        timestamp: Date.now()
      };
    }
  }

  /**
   * Utility methods
   */
  private getQueryType(query: string): string {
    const normalized = query.trim().toLowerCase();
    if (normalized.startsWith('select')) return 'select';
    if (normalized.startsWith('insert')) return 'insert';
    if (normalized.startsWith('update')) return 'update';
    if (normalized.startsWith('delete')) return 'delete';
    return 'other';
  }

  private hashQuery(query: string): string {
    // Simple hash function for query identification
    let hash = 0;
    for (let i = 0; i < query.length; i++) {
      const char = query.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  private async triggerAlert(alert: AlertRule, metric: MetricData) {
    console.error(`🚨 ALERT: ${alert.name}`, { alert, metric });

    // Send to Sentry
    Sentry.captureMessage(`Alert: ${alert.name}`, {
      level: alert.severity === 'critical' ? 'error' : 'warning',
      extra: { alert, metric }
    });

    // Send to notification channels
    for (const channel of alert.channels) {
      await this.sendNotification(channel, alert, metric);
    }
  }

  private async sendNotification(channel: string, alert: AlertRule, metric: MetricData) {
    // Implement notification logic for different channels
    switch (channel) {
      case 'slack':
        await this.sendSlackNotification(alert, metric);
        break;
      case 'email':
        await this.sendEmailNotification(alert, metric);
        break;
      case 'sms':
        await this.sendSMSNotification(alert, metric);
        break;
    }
  }

  private async sendSlackNotification(alert: AlertRule, metric: MetricData) {
    // Implement Slack webhook notification
    console.log('Sending Slack notification:', { alert, metric });
  }

  private async sendEmailNotification(alert: AlertRule, metric: MetricData) {
    // Implement email notification
    console.log('Sending email notification:', { alert, metric });
  }

  private async sendSMSNotification(alert: AlertRule, metric: MetricData) {
    // Implement SMS notification
    console.log('Sending SMS notification:', { alert, metric });
  }

  private sendToAnalytics(metric: MetricData) {
    // Send metrics to external analytics service
    if (process.env.NODE_ENV === 'production') {
      // Implement analytics integration (e.g., DataDog, New Relic, etc.)
      console.log('Sending metric to analytics:', metric);
    }
  }

  private startMetricsCollection() {
    if (!this.isEnabled) return;

    // Collect system metrics every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);
  }

  private collectSystemMetrics() {
    const memUsage = process.memoryUsage();
    
    this.trackMetric('system_memory_heap_used', memUsage.heapUsed, { type: 'system' });
    this.trackMetric('system_memory_heap_total', memUsage.heapTotal, { type: 'system' });
    this.trackMetric('system_memory_external', memUsage.external, { type: 'system' });
  }
}

// Export singleton instance
export const enterpriseMonitoring = new EnterpriseMonitoring();

// Export types for use in other modules
export type { MetricData, AlertRule, HealthCheck };
