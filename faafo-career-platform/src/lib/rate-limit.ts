import { NextRequest } from 'next/server';
import { CONFIG } from './config';
import { getRedisClient, isRedisAvailable, executeRedisCommand } from './redis';

// In-memory store for rate limiting fallback
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

interface RateLimitOptions {
  requests: number;
  windowMs: number;
  keyGenerator?: (request: NextRequest) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export class RateLimiter {
  private options: Required<RateLimitOptions>;

  constructor(options: Partial<RateLimitOptions> = {}) {
    this.options = {
      requests: options.requests || CONFIG.API.RATE_LIMIT_REQUESTS,
      windowMs: options.windowMs || CONFIG.API.RATE_LIMIT_WINDOW_MS,
      keyGenerator: options.keyGenerator || this.defaultKeyGenerator,
      skipSuccessfulRequests: options.skipSuccessfulRequests || false,
      skipFailedRequests: options.skipFailedRequests || false,
    };
  }

  private defaultKeyGenerator(request: NextRequest): string {
    // Use IP address as default key
    return getClientIP(request);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    rateLimitStore.forEach((value, key) => {
      if (now > value.resetTime) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => rateLimitStore.delete(key));
  }

  /**
   * Redis-based rate limiting check
   */
  private async checkRedisRateLimit(key: string): Promise<{
    allowed: boolean;
    limit: number;
    remaining: number;
    resetTime: number;
  } | null> {
    return executeRedisCommand(async (redis) => {
      const now = Date.now();
      const windowStart = now;
      const windowEnd = windowStart + this.options.windowMs;

      // Use Redis hash to store rate limit data
      const redisKey = `rate_limit:${key}`;

      // Get current data
      const data = await redis.hmget(redisKey, 'count', 'resetTime');
      let count = parseInt(data[0] || '0');
      let resetTime = parseInt(data[1] || '0');

      // Reset if window expired
      if (now > resetTime) {
        count = 0;
        resetTime = windowEnd;
      }

      const allowed = count < this.options.requests;
      const remaining = Math.max(0, this.options.requests - count - 1);

      if (allowed) {
        count++;
        // Update Redis with new count and set expiration
        await redis.hmset(redisKey, 'count', count.toString(), 'resetTime', resetTime.toString());
        await redis.pexpire(redisKey, this.options.windowMs);
      }

      return {
        allowed,
        limit: this.options.requests,
        remaining,
        resetTime,
      };
    });
  }

  /**
   * Memory-based rate limiting check (fallback)
   */
  private checkMemoryRateLimit(key: string): {
    allowed: boolean;
    limit: number;
    remaining: number;
    resetTime: number;
  } {
    this.cleanupExpiredEntries();

    const now = Date.now();
    const windowStart = now;
    const windowEnd = windowStart + this.options.windowMs;

    let entry = rateLimitStore.get(key);

    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 0,
        resetTime: windowEnd,
      };
      rateLimitStore.set(key, entry);
    }

    const allowed = entry.count < this.options.requests;
    const remaining = Math.max(0, this.options.requests - entry.count - 1);

    if (allowed) {
      entry.count++;
    }

    return {
      allowed,
      limit: this.options.requests,
      remaining,
      resetTime: entry.resetTime,
    };
  }

  public async check(request: NextRequest): Promise<{
    allowed: boolean;
    limit: number;
    remaining: number;
    resetTime: number;
  }> {
    const key = this.options.keyGenerator(request);

    // Try Redis first if available
    if (isRedisAvailable()) {
      try {
        const result = await this.checkRedisRateLimit(key);
        if (result) {
          return result;
        }
      } catch (error) {
        console.warn('Redis rate limiting failed, falling back to memory:', error);
      }
    }

    // Fallback to memory-based rate limiting
    return this.checkMemoryRateLimit(key);
  }

  public async increment(request: NextRequest): Promise<void> {
    const key = this.options.keyGenerator(request);

    // Try Redis first if available
    if (isRedisAvailable()) {
      try {
        await executeRedisCommand(async (redis) => {
          const redisKey = `rate_limit:${key}`;
          await redis.hincrby(redisKey, 'count', 1);
          return true;
        });
        return;
      } catch (error) {
        console.warn('Redis increment failed, falling back to memory:', error);
      }
    }

    // Fallback to memory
    const entry = rateLimitStore.get(key);
    if (entry) {
      entry.count++;
    }
  }
}

// Pre-configured rate limiters for different endpoints
export const rateLimiters = {
  // General API rate limiter
  api: new RateLimiter({
    requests: 100,
    windowMs: 15 * 60 * 1000, // 15 minutes
  }),

  // Stricter rate limiter for authentication endpoints
  auth: new RateLimiter({
    requests: 10,
    windowMs: 15 * 60 * 1000, // 15 minutes
  }),

  // Very strict rate limiter for password reset
  passwordReset: new RateLimiter({
    requests: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
  }),

  // Moderate rate limiter for search/read operations
  search: new RateLimiter({
    requests: 200,
    windowMs: 15 * 60 * 1000, // 15 minutes
  }),

  // Strict rate limiter for write operations
  write: new RateLimiter({
    requests: 50,
    windowMs: 15 * 60 * 1000, // 15 minutes
  }),
};

// Middleware function to apply rate limiting
export function withRateLimit(rateLimiter: RateLimiter) {
  return async (request: NextRequest) => {
    const result = await rateLimiter.check(request);

    return {
      ...result,
      headers: {
        'X-RateLimit-Limit': result.limit.toString(),
        'X-RateLimit-Remaining': result.remaining.toString(),
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
        'X-RateLimit-Backend': isRedisAvailable() ? 'redis' : 'memory',
      },
    };
  };
}

// Helper function to get client IP
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

// Rate limit by user ID for authenticated requests
export function createUserRateLimiter(options: Partial<RateLimitOptions> = {}) {
  return new RateLimiter({
    ...options,
    keyGenerator: (request: NextRequest) => {
      // This would need to be implemented based on your auth system
      // For now, fall back to IP-based limiting
      return getClientIP(request);
    },
  });
}

// Cleanup function to be called periodically
export function cleanupRateLimitStore(): void {
  const now = Date.now();
  let cleaned = 0;
  const keysToDelete: string[] = [];

  rateLimitStore.forEach((value, key) => {
    if (now > value.resetTime) {
      keysToDelete.push(key);
    }
  });

  keysToDelete.forEach(key => {
    rateLimitStore.delete(key);
    cleaned++;
  });

  console.log(`Cleaned up ${cleaned} expired rate limit entries`);
}

// Auto-cleanup every 5 minutes
if (typeof window === 'undefined') {
  setInterval(cleanupRateLimitStore, 5 * 60 * 1000);
}
