/**
 * Enterprise Configuration Management System
 * Centralized configuration management with environment-specific settings,
 * feature flags, and runtime configuration updates
 */

import { z } from 'zod';

// Configuration schemas for validation
const DatabaseConfigSchema = z.object({
  url: z.string().url(),
  maxConnections: z.number().min(1).max(100),
  connectionTimeout: z.number().min(1000),
  queryTimeout: z.number().min(1000),
  ssl: z.boolean().optional(),
  pooling: z.boolean().optional()
});

const CacheConfigSchema = z.object({
  enabled: z.boolean(),
  provider: z.enum(['redis', 'memory', 'none']),
  url: z.string().optional(),
  defaultTTL: z.number().min(60),
  maxMemorySize: z.number().min(1024).optional()
});

const SecurityConfigSchema = z.object({
  rateLimiting: z.object({
    enabled: z.boolean(),
    windowMs: z.number().min(1000),
    maxRequests: z.number().min(1)
  }),
  cors: z.object({
    enabled: z.boolean(),
    origins: z.array(z.string()),
    credentials: z.boolean()
  }),
  csrf: z.object({
    enabled: z.boolean(),
    secret: z.string().min(32)
  }),
  encryption: z.object({
    algorithm: z.string(),
    keyRotationDays: z.number().min(1)
  })
});

const MonitoringConfigSchema = z.object({
  enabled: z.boolean(),
  level: z.enum(['error', 'warn', 'info', 'debug']),
  sentry: z.object({
    enabled: z.boolean(),
    dsn: z.string().optional(),
    environment: z.string(),
    sampleRate: z.number().min(0).max(1)
  }),
  analytics: z.object({
    enabled: z.boolean(),
    provider: z.enum(['internal', 'google', 'mixpanel', 'amplitude']),
    sampleRate: z.number().min(0).max(1)
  })
});

const FeatureFlagsSchema = z.object({
  aiInsights: z.boolean(),
  communityForum: z.boolean(),
  advancedAnalytics: z.boolean(),
  premiumFeatures: z.boolean(),
  betaFeatures: z.boolean(),
  maintenanceMode: z.boolean(),
  newUserRegistration: z.boolean(),
  emailNotifications: z.boolean(),
  socialLogin: z.boolean(),
  apiAccess: z.boolean()
});

// Main configuration schema
const EnterpriseConfigSchema = z.object({
  environment: z.enum(['development', 'staging', 'production']),
  version: z.string(),
  database: DatabaseConfigSchema,
  cache: CacheConfigSchema,
  security: SecurityConfigSchema,
  monitoring: MonitoringConfigSchema,
  featureFlags: FeatureFlagsSchema,
  services: z.object({
    email: z.object({
      provider: z.enum(['resend', 'sendgrid', 'ses', 'console']),
      apiKey: z.string().optional(),
      fromAddress: z.string().email(),
      replyToAddress: z.string().email().optional()
    }),
    ai: z.object({
      enabled: z.boolean(),
      provider: z.enum(['gemini', 'openai', 'anthropic']),
      apiKey: z.string().optional(),
      model: z.string(),
      maxTokens: z.number().min(100),
      temperature: z.number().min(0).max(2)
    }),
    storage: z.object({
      provider: z.enum(['local', 's3', 'gcs', 'azure']),
      bucket: z.string().optional(),
      region: z.string().optional(),
      maxFileSize: z.number().min(1024)
    })
  }),
  limits: z.object({
    maxUsersPerOrganization: z.number().min(1),
    maxAssessmentsPerUser: z.number().min(1),
    maxResourcesPerPath: z.number().min(1),
    apiRateLimit: z.number().min(1),
    uploadSizeLimit: z.number().min(1024)
  }),
  maintenance: z.object({
    enabled: z.boolean(),
    message: z.string().optional(),
    allowedIPs: z.array(z.string()).optional(),
    scheduledStart: z.string().optional(),
    scheduledEnd: z.string().optional()
  })
});

type EnterpriseConfig = z.infer<typeof EnterpriseConfigSchema>;

class EnterpriseConfigManager {
  private config: EnterpriseConfig;
  private configCache: Map<string, any> = new Map();
  private configWatchers: Map<string, Array<(value: any) => void>> = new Map();
  private lastConfigUpdate: number = 0;

  constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
    this.setupConfigurationWatching();
  }

  /**
   * Get configuration value with type safety
   */
  get<K extends keyof EnterpriseConfig>(key: K): EnterpriseConfig[K] {
    return this.config[key];
  }

  /**
   * Get nested configuration value
   */
  getNestedConfig<T = any>(path: string): T {
    const keys = path.split('.');
    let value: any = this.config;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        throw new Error(`Configuration path not found: ${path}`);
      }
    }
    
    return value as T;
  }

  /**
   * Check if feature flag is enabled
   */
  isFeatureEnabled(feature: keyof EnterpriseConfig['featureFlags']): boolean {
    return this.config.featureFlags[feature];
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig(): {
    isDevelopment: boolean;
    isStaging: boolean;
    isProduction: boolean;
    environment: string;
  } {
    const env = this.config.environment;
    return {
      isDevelopment: env === 'development',
      isStaging: env === 'staging',
      isProduction: env === 'production',
      environment: env
    };
  }

  /**
   * Get database configuration with connection string parsing
   */
  getDatabaseConfig(): EnterpriseConfig['database'] & {
    connectionString: string;
    isSSLEnabled: boolean;
    poolConfig: {
      min: number;
      max: number;
      idleTimeoutMillis: number;
    };
  } {
    const dbConfig = this.config.database;
    
    return {
      ...dbConfig,
      connectionString: dbConfig.url,
      isSSLEnabled: dbConfig.ssl || this.config.environment === 'production',
      poolConfig: {
        min: 2,
        max: dbConfig.maxConnections,
        idleTimeoutMillis: 30000
      }
    };
  }

  /**
   * Get cache configuration with provider-specific settings
   */
  getCacheConfig(): EnterpriseConfig['cache'] & {
    isEnabled: boolean;
    connectionOptions: Record<string, any>;
  } {
    const cacheConfig = this.config.cache;
    
    let connectionOptions: Record<string, any> = {};
    
    if (cacheConfig.provider === 'redis' && cacheConfig.url) {
      connectionOptions = {
        url: cacheConfig.url,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true
      };
    } else if (cacheConfig.provider === 'memory') {
      connectionOptions = {
        maxSize: cacheConfig.maxMemorySize || 1000,
        ttl: cacheConfig.defaultTTL * 1000
      };
    }
    
    return {
      ...cacheConfig,
      isEnabled: cacheConfig.enabled && cacheConfig.provider !== 'none',
      connectionOptions
    };
  }

  /**
   * Get security configuration with computed values
   */
  getSecurityConfig(): EnterpriseConfig['security'] & {
    isSecureEnvironment: boolean;
    securityHeaders: Record<string, string>;
    rateLimitConfig: {
      windowMs: number;
      max: number;
      standardHeaders: boolean;
      legacyHeaders: boolean;
    };
  } {
    const securityConfig = this.config.security;
    const isProduction = this.config.environment === 'production';
    
    return {
      ...securityConfig,
      isSecureEnvironment: isProduction,
      securityHeaders: {
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'X-XSS-Protection': '1; mode=block',
        ...(isProduction && {
          'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
        })
      },
      rateLimitConfig: {
        windowMs: securityConfig.rateLimiting.windowMs,
        max: securityConfig.rateLimiting.maxRequests,
        standardHeaders: true,
        legacyHeaders: false
      }
    };
  }

  /**
   * Get monitoring configuration with service-specific settings
   */
  getMonitoringConfig(): EnterpriseConfig['monitoring'] & {
    shouldEnableMonitoring: boolean;
    logLevel: string;
    sentryConfig?: {
      dsn: string;
      environment: string;
      sampleRate: number;
      beforeSend?: (event: any) => any;
    };
  } {
    const monitoringConfig = this.config.monitoring;
    const isProduction = this.config.environment === 'production';
    
    const result: any = {
      ...monitoringConfig,
      shouldEnableMonitoring: monitoringConfig.enabled && isProduction,
      logLevel: monitoringConfig.level
    };
    
    if (monitoringConfig.sentry.enabled && monitoringConfig.sentry.dsn) {
      result.sentryConfig = {
        dsn: monitoringConfig.sentry.dsn,
        environment: monitoringConfig.sentry.environment,
        sampleRate: monitoringConfig.sentry.sampleRate,
        beforeSend: (event: any) => {
          // Filter out sensitive information
          if (event.exception) {
            // Remove sensitive data from error messages
            return event;
          }
          return event;
        }
      };
    }
    
    return result;
  }

  /**
   * Get service configurations
   */
  getServiceConfig<K extends keyof EnterpriseConfig['services']>(
    service: K
  ): EnterpriseConfig['services'][K] & { isConfigured: boolean } {
    const serviceConfig = this.config.services[service];
    
    let isConfigured = false;
    
    switch (service) {
      case 'email':
        isConfigured = !!(serviceConfig as any).apiKey || (serviceConfig as any).provider === 'console';
        break;
      case 'ai':
        isConfigured = !!(serviceConfig as any).enabled && !!(serviceConfig as any).apiKey;
        break;
      case 'storage':
        isConfigured = (serviceConfig as any).provider === 'local' || !!(serviceConfig as any).bucket;
        break;
    }
    
    return {
      ...serviceConfig,
      isConfigured
    } as any;
  }

  /**
   * Update configuration at runtime (for feature flags and non-critical settings)
   */
  updateConfig(path: string, value: any): void {
    const keys = path.split('.');
    let target: any = this.config;
    
    // Navigate to the parent object
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in target)) {
        throw new Error(`Configuration path not found: ${path}`);
      }
      target = target[keys[i]];
    }
    
    const finalKey = keys[keys.length - 1];
    const oldValue = target[finalKey];
    target[finalKey] = value;
    
    // Validate the updated configuration
    try {
      this.validateConfiguration();
    } catch (error) {
      // Rollback on validation failure
      target[finalKey] = oldValue;
      throw new Error(`Configuration update failed validation: ${error}`);
    }
    
    // Notify watchers
    this.notifyConfigWatchers(path, value);
    this.lastConfigUpdate = Date.now();
    
    console.log(`Configuration updated: ${path} = ${JSON.stringify(value)}`);
  }

  /**
   * Watch for configuration changes
   */
  watchConfig(path: string, callback: (value: any) => void): () => void {
    if (!this.configWatchers.has(path)) {
      this.configWatchers.set(path, []);
    }
    
    this.configWatchers.get(path)!.push(callback);
    
    // Return unsubscribe function
    return () => {
      const watchers = this.configWatchers.get(path);
      if (watchers) {
        const index = watchers.indexOf(callback);
        if (index > -1) {
          watchers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Get configuration health status
   */
  getConfigurationHealth(): {
    isValid: boolean;
    lastUpdate: number;
    missingRequired: string[];
    warnings: string[];
    environment: string;
  } {
    const missingRequired: string[] = [];
    const warnings: string[] = [];
    
    // Check required configurations
    if (!this.config.database.url) {
      missingRequired.push('database.url');
    }
    
    if (this.config.environment === 'production') {
      if (!this.config.security.csrf.secret || this.config.security.csrf.secret.length < 32) {
        missingRequired.push('security.csrf.secret');
      }
      
      if (!this.config.monitoring.sentry.dsn && this.config.monitoring.sentry.enabled) {
        warnings.push('Sentry monitoring enabled but DSN not configured');
      }
    }
    
    return {
      isValid: missingRequired.length === 0,
      lastUpdate: this.lastConfigUpdate,
      missingRequired,
      warnings,
      environment: this.config.environment
    };
  }

  /**
   * Export configuration (with sensitive data masked)
   */
  exportConfiguration(includeSensitive: boolean = false): Record<string, any> {
    const config = JSON.parse(JSON.stringify(this.config));
    
    if (!includeSensitive) {
      // Mask sensitive values
      if (config.database?.url) {
        config.database.url = this.maskSensitiveValue(config.database.url);
      }
      if (config.security?.csrf?.secret) {
        config.security.csrf.secret = this.maskSensitiveValue(config.security.csrf.secret);
      }
      if (config.services?.email?.apiKey) {
        config.services.email.apiKey = this.maskSensitiveValue(config.services.email.apiKey);
      }
      if (config.services?.ai?.apiKey) {
        config.services.ai.apiKey = this.maskSensitiveValue(config.services.ai.apiKey);
      }
    }
    
    return config;
  }

  /**
   * Private helper methods
   */
  private loadConfiguration(): EnterpriseConfig {
    const environment = (process.env.NODE_ENV as any) || 'development';
    
    return {
      environment,
      version: process.env.APP_VERSION || '1.0.0',
      database: {
        url: process.env.DATABASE_URL || '',
        maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10'),
        connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
        queryTimeout: parseInt(process.env.DB_QUERY_TIMEOUT || '5000'),
        ssl: process.env.DB_SSL === 'true',
        pooling: process.env.DB_POOLING !== 'false'
      },
      cache: {
        enabled: process.env.CACHE_ENABLED !== 'false',
        provider: (process.env.CACHE_PROVIDER as any) || 'memory',
        url: process.env.REDIS_URL,
        defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL || '3600'),
        maxMemorySize: parseInt(process.env.CACHE_MAX_MEMORY_SIZE || '1000')
      },
      security: {
        rateLimiting: {
          enabled: process.env.RATE_LIMIT_ENABLED !== 'false',
          windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
          maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100')
        },
        cors: {
          enabled: process.env.CORS_ENABLED !== 'false',
          origins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
          credentials: process.env.CORS_CREDENTIALS === 'true'
        },
        csrf: {
          enabled: process.env.CSRF_ENABLED !== 'false',
          secret: process.env.CSRF_SECRET || this.generateSecureSecret()
        },
        encryption: {
          algorithm: process.env.ENCRYPTION_ALGORITHM || 'aes-256-cbc',
          keyRotationDays: parseInt(process.env.ENCRYPTION_KEY_ROTATION_DAYS || '90')
        }
      },
      monitoring: {
        enabled: process.env.MONITORING_ENABLED !== 'false',
        level: (process.env.LOG_LEVEL as any) || (environment === 'production' ? 'error' : 'debug'),
        sentry: {
          enabled: process.env.SENTRY_ENABLED === 'true',
          dsn: process.env.SENTRY_DSN,
          environment,
          sampleRate: parseFloat(process.env.SENTRY_SAMPLE_RATE || '1.0')
        },
        analytics: {
          enabled: process.env.ANALYTICS_ENABLED !== 'false',
          provider: (process.env.ANALYTICS_PROVIDER as any) || 'internal',
          sampleRate: parseFloat(process.env.ANALYTICS_SAMPLE_RATE || '1.0')
        }
      },
      featureFlags: {
        aiInsights: process.env.FEATURE_AI_INSIGHTS !== 'false',
        communityForum: process.env.FEATURE_COMMUNITY_FORUM === 'true',
        advancedAnalytics: process.env.FEATURE_ADVANCED_ANALYTICS === 'true',
        premiumFeatures: process.env.FEATURE_PREMIUM === 'true',
        betaFeatures: process.env.FEATURE_BETA === 'true',
        maintenanceMode: process.env.MAINTENANCE_MODE === 'true',
        newUserRegistration: process.env.FEATURE_NEW_USER_REGISTRATION !== 'false',
        emailNotifications: process.env.FEATURE_EMAIL_NOTIFICATIONS !== 'false',
        socialLogin: process.env.FEATURE_SOCIAL_LOGIN === 'true',
        apiAccess: process.env.FEATURE_API_ACCESS === 'true'
      },
      services: {
        email: {
          provider: (process.env.EMAIL_PROVIDER as any) || 'console',
          apiKey: process.env.RESEND_API_KEY || process.env.SENDGRID_API_KEY,
          fromAddress: process.env.EMAIL_FROM || '<EMAIL>',
          replyToAddress: process.env.EMAIL_REPLY_TO
        },
        ai: {
          enabled: process.env.AI_ENABLED !== 'false',
          provider: (process.env.AI_PROVIDER as any) || 'gemini',
          apiKey: process.env.GOOGLE_GEMINI_API_KEY || process.env.OPENAI_API_KEY,
          model: process.env.AI_MODEL || 'gemini-pro',
          maxTokens: parseInt(process.env.AI_MAX_TOKENS || '2048'),
          temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7')
        },
        storage: {
          provider: (process.env.STORAGE_PROVIDER as any) || 'local',
          bucket: process.env.STORAGE_BUCKET,
          region: process.env.STORAGE_REGION || 'us-east-1',
          maxFileSize: parseInt(process.env.STORAGE_MAX_FILE_SIZE || '10485760') // 10MB
        }
      },
      limits: {
        maxUsersPerOrganization: parseInt(process.env.LIMIT_MAX_USERS_PER_ORG || '1000'),
        maxAssessmentsPerUser: parseInt(process.env.LIMIT_MAX_ASSESSMENTS_PER_USER || '10'),
        maxResourcesPerPath: parseInt(process.env.LIMIT_MAX_RESOURCES_PER_PATH || '100'),
        apiRateLimit: parseInt(process.env.LIMIT_API_RATE || '1000'),
        uploadSizeLimit: parseInt(process.env.LIMIT_UPLOAD_SIZE || '10485760')
      },
      maintenance: {
        enabled: process.env.MAINTENANCE_MODE === 'true',
        message: process.env.MAINTENANCE_MESSAGE,
        allowedIPs: process.env.MAINTENANCE_ALLOWED_IPS?.split(','),
        scheduledStart: process.env.MAINTENANCE_SCHEDULED_START,
        scheduledEnd: process.env.MAINTENANCE_SCHEDULED_END
      }
    };
  }

  private validateConfiguration(): void {
    try {
      EnterpriseConfigSchema.parse(this.config);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(e => `${e.path.join('.')}: ${e.message}`);
        throw new Error(`Configuration validation failed:\n${errorMessages.join('\n')}`);
      }
      throw error;
    }
  }

  private setupConfigurationWatching(): void {
    // In a real implementation, this would watch for configuration file changes
    // or connect to a configuration management service
    console.log('Configuration management initialized');
  }

  private notifyConfigWatchers(path: string, value: any): void {
    const watchers = this.configWatchers.get(path);
    if (watchers) {
      watchers.forEach(callback => {
        try {
          callback(value);
        } catch (error) {
          console.error(`Configuration watcher error for ${path}:`, error);
        }
      });
    }
  }

  private generateSecureSecret(): string {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  private maskSensitiveValue(value: string): string {
    if (value.length <= 8) {
      return '*'.repeat(value.length);
    }
    return value.substring(0, 4) + '*'.repeat(value.length - 8) + value.substring(value.length - 4);
  }
}

// Export singleton instance
export const enterpriseConfig = new EnterpriseConfigManager();

// Export types
export type { EnterpriseConfig };
