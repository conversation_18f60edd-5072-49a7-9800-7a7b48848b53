import { NextRequest, NextResponse } from 'next/server';
import { SimpleSecurity } from './simple-security';

/**
 * CSRF Protection Middleware
 * Provides comprehensive CSRF protection for API routes
 */

export interface CSRFOptions {
  /**
   * Whether to require CSRF protection for this route
   */
  enabled?: boolean;
  
  /**
   * Custom error message for CSRF failures
   */
  errorMessage?: string;
  
  /**
   * Whether to log CSRF violations
   */
  logViolations?: boolean;
  
  /**
   * Custom session ID extractor
   */
  getSessionId?: (req: NextRequest) => string | undefined;
}

/**
 * CSRF protection middleware wrapper
 */
export function withCSRFProtection(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: CSRFOptions = {}
) {
  const {
    enabled = true,
    errorMessage = 'CSRF token validation failed',
    logViolations = true,
    getSessionId = (req) => req.headers.get('x-session-id') || undefined
  } = options;

  return async (req: NextRequest): Promise<NextResponse> => {
    // Skip CSRF protection if disabled or for safe methods
    if (!enabled || ['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return handler(req);
    }

    try {
      // Extract CSRF token from headers
      const csrfToken = req.headers.get('x-csrf-token');
      
      if (!csrfToken) {
        if (logViolations) {
          console.warn('CSRF violation: Missing token', {
            method: req.method,
            url: req.url,
            userAgent: req.headers.get('user-agent'),
            ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')
          });
        }
        
        return NextResponse.json(
          { error: 'CSRF token required' },
          { status: 403 }
        );
      }

      // Get session ID for validation
      const sessionId = getSessionId(req);
      
      // Validate CSRF token
      const validation = await SimpleSecurity.validateCSRFToken(csrfToken, sessionId);
      
      if (!validation.isValid) {
        if (logViolations) {
          console.warn('CSRF violation: Invalid token', {
            method: req.method,
            url: req.url,
            reason: validation.reason,
            userAgent: req.headers.get('user-agent'),
            ip: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')
          });
        }
        
        return NextResponse.json(
          { 
            error: errorMessage,
            reason: validation.reason 
          },
          { status: 403 }
        );
      }

      // CSRF validation passed, proceed with the request
      return handler(req);
      
    } catch (error) {
      console.error('CSRF middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Enhanced middleware that combines CSRF protection with rate limiting
 */
export function withSecurityProtection(
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: {
    csrf?: CSRFOptions;
    rateLimit?: { max: number; windowMs: number };
  } = {}
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    // Apply rate limiting first
    if (options.rateLimit) {
      const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
      const allowed = await SimpleSecurity.checkRateLimitAsync(
        clientIP,
        options.rateLimit.max,
        options.rateLimit.windowMs
      );

      if (!allowed) {
        return NextResponse.json(
          { error: 'Too many requests' },
          { status: 429 }
        );
      }
    }

    // Apply CSRF protection
    const csrfProtectedHandler = withCSRFProtection(handler, options.csrf);
    const response = await csrfProtectedHandler(req);
    
    // Add security headers
    return SimpleSecurity.addSecurityHeaders(response);
  };
}

/**
 * Utility function to check if a route requires CSRF protection
 */
export function requiresCSRFProtection(method: string): boolean {
  return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase());
}

/**
 * Extract CSRF token from various sources (headers, body, query)
 */
export function extractCSRFToken(req: NextRequest): string | null {
  // Try header first (recommended)
  let token = req.headers.get('x-csrf-token');
  if (token) return token;

  // Try alternative header names
  token = req.headers.get('csrf-token');
  if (token) return token;

  // Try query parameter (less secure, for compatibility)
  const url = new URL(req.url);
  token = url.searchParams.get('csrf_token');
  if (token) return token;

  return null;
}

/**
 * Generate CSRF token for client-side use
 */
export async function generateCSRFTokenForClient(sessionId?: string): Promise<{
  token: string;
  expiresAt: number;
}> {
  const token = SimpleSecurity.generateCSRFToken(sessionId);
  await SimpleSecurity.storeCSRFToken(token, sessionId);
  
  return {
    token,
    expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
  };
}

/**
 * Validate CSRF token manually (for custom implementations)
 */
export async function validateCSRFTokenManual(
  token: string,
  sessionId?: string
): Promise<{ isValid: boolean; reason?: string }> {
  return SimpleSecurity.validateCSRFToken(token, sessionId);
}

/**
 * Security level-based CSRF configuration
 */
export const CSRF_CONFIGS = {
  HIGH_SECURITY: {
    enabled: true,
    logViolations: true,
    errorMessage: 'Security validation failed'
  },
  MEDIUM_SECURITY: {
    enabled: true,
    logViolations: true,
    errorMessage: 'CSRF token validation failed'
  },
  LOW_SECURITY: {
    enabled: true,
    logViolations: false,
    errorMessage: 'Invalid request'
  },
  DISABLED: {
    enabled: false
  }
} as const;

/**
 * Route-specific CSRF protection helper
 */
export function createCSRFProtectedRoute(
  handler: (req: NextRequest) => Promise<NextResponse>,
  securityLevel: keyof typeof CSRF_CONFIGS = 'MEDIUM_SECURITY'
) {
  return withCSRFProtection(handler, CSRF_CONFIGS[securityLevel]);
}

export default {
  withCSRFProtection,
  withSecurityProtection,
  requiresCSRFProtection,
  extractCSRFToken,
  generateCSRFTokenForClient,
  validateCSRFTokenManual,
  CSRF_CONFIGS,
  createCSRFProtectedRoute
};
