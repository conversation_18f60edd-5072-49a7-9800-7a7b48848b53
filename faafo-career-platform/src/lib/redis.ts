import Redis from 'ioredis';

// Redis client configuration
let redis: Redis | null = null;
let isConnecting = false;
let connectionError: Error | null = null;

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  connectTimeout: 5000,
  commandTimeout: 5000,
  // Connection pool settings
  family: 4,
  keepAlive: 30000, // 30 seconds
  // Retry strategy
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
  // Reconnect on error
  reconnectOnError: (err: Error) => {
    const targetError = 'READONLY';
    return err.message.includes(targetError);
  },
};

// Parse Redis URL if provided (for Redis Cloud, Heroku, etc.)
if (process.env.REDIS_URL) {
  try {
    const url = new URL(process.env.REDIS_URL);
    redisConfig.host = url.hostname;
    redisConfig.port = parseInt(url.port) || 6379;
    if (url.password) {
      redisConfig.password = url.password;
    }
    if (url.pathname && url.pathname !== '/') {
      redisConfig.db = parseInt(url.pathname.slice(1)) || 0;
    }
  } catch (error) {
    console.warn('Invalid REDIS_URL format, using individual config values');
  }
}

/**
 * Get Redis client instance with connection management
 */
export async function getRedisClient(): Promise<Redis | null> {
  // Return existing connected client
  if (redis && redis.status === 'ready') {
    return redis;
  }

  // Don't create multiple connection attempts
  if (isConnecting) {
    // Wait for current connection attempt
    let attempts = 0;
    while (isConnecting && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    return redis && redis.status === 'ready' ? redis : null;
  }

  try {
    isConnecting = true;
    connectionError = null;

    // Create new Redis instance
    redis = new Redis(redisConfig);

    // Set up event handlers
    redis.on('connect', () => {
      console.log('Redis client connected');
    });

    redis.on('ready', () => {
      console.log('Redis client ready');
      isConnecting = false;
    });

    redis.on('error', (err) => {
      console.warn('Redis connection error:', err.message);
      connectionError = err;
      isConnecting = false;
    });

    redis.on('close', () => {
      console.log('Redis connection closed');
    });

    redis.on('reconnecting', () => {
      console.log('Redis client reconnecting...');
    });

    // Attempt to connect
    await redis.connect();

    // Test the connection
    await redis.ping();

    return redis;
  } catch (error) {
    console.warn('Failed to connect to Redis:', error);
    connectionError = error as Error;
    isConnecting = false;
    
    // Clean up failed connection
    if (redis) {
      try {
        redis.disconnect();
      } catch (e) {
        // Ignore cleanup errors
      }
      redis = null;
    }
    
    return null;
  }
}

/**
 * Check if Redis is available and connected
 */
export function isRedisAvailable(): boolean {
  return redis !== null && redis.status === 'ready';
}

/**
 * Get Redis connection status
 */
export function getRedisStatus(): {
  connected: boolean;
  status: string;
  error: string | null;
} {
  return {
    connected: isRedisAvailable(),
    status: redis?.status || 'disconnected',
    error: connectionError?.message || null,
  };
}

/**
 * Gracefully disconnect Redis client
 */
export async function disconnectRedis(): Promise<void> {
  if (redis) {
    try {
      await redis.quit();
    } catch (error) {
      console.warn('Error disconnecting Redis:', error);
    } finally {
      redis = null;
      connectionError = null;
      isConnecting = false;
    }
  }
}

/**
 * Execute Redis command with error handling
 */
export async function executeRedisCommand<T>(
  command: (client: Redis) => Promise<T>
): Promise<T | null> {
  try {
    const client = await getRedisClient();
    if (!client) {
      return null;
    }
    return await command(client);
  } catch (error) {
    console.warn('Redis command failed:', error);
    return null;
  }
}

// Export Redis instance for direct access (use with caution)
export { redis };
