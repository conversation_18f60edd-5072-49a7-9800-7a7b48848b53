import type { Metadata, Viewport } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import SessionWrapper from '@/components/SessionWrapper';
import { ThemeProviderWrapper } from '@/components/providers/ThemeProviderWrapper';
import VercelAnalyticsWrapper from "@/app/components/layout/VercelAnalyticsWrapper";
import Footer from "@/components/layout/Footer";
import { NavigationBar } from "@/components/layout/NavigationBar";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

// Viewport configuration moved to metadata for compatibility

export const metadata: Metadata = {
  title: "FAAFO Career Platform - Find Your Path to Career Freedom",
  description: "Empowering career transitions through personalized assessments, financial planning, and community support. Take control of your career journey with FAAFO.",
  keywords: "career, freedom, assessment, learning, professional development, career transition",
  authors: [{ name: "FAAFO Team" }],
  robots: "index, follow",
  openGraph: {
    title: "FAAFO Career Platform - Find Your Path to Career Freedom",
    description: "Empowering career transitions through personalized assessments, financial planning, and community support.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "FAAFO Career Platform",
    description: "Find your path to career freedom",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#3b82f6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />
      </head>
      <body className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}>
        <SessionWrapper>
          <ThemeProviderWrapper>
            <VercelAnalyticsWrapper>
              <div className="min-h-screen flex flex-col">
                <NavigationBar />
                <main className="flex-1">
                  {children}
                </main>
                <Footer />
              </div>
            </VercelAnalyticsWrapper>
          </ThemeProviderWrapper>
        </SessionWrapper>
      </body>
    </html>
  );
}
