'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, RefreshCw, TrendingUp, Clock, Users, Bug } from 'lucide-react';

interface ErrorEvent {
  id: string;
  message: string;
  stack?: string;
  url: string;
  timestamp: number;
  metadata?: Record<string, any>;
  level: 'error' | 'warning' | 'info';
  source: string;
  userAgent?: string;
  userId?: string;
  resolved: boolean;
}

interface ErrorStats {
  totalErrors: number;
  errorRate: number;
  topErrors: Array<{ message: string; count: number }>;
  errorsByHour: Array<{ hour: string; count: number }>;
  affectedUsers: number;
}

export default function ErrorMonitoringPage() {
  const [errors, setErrors] = useState<ErrorEvent[]>([]);
  const [stats, setStats] = useState<ErrorStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'error' | 'warning' | 'info'>('all');

  useEffect(() => {
    fetchErrors();
    fetchStats();
    
    // Refresh every 30 seconds
    const interval = setInterval(() => {
      fetchErrors();
      fetchStats();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchErrors = async () => {
    try {
      const response = await fetch('/api/admin/errors');
      if (response.ok) {
        const data = await response.json();
        setErrors(data.errors || []);
      }
    } catch (error) {
      console.error('Failed to fetch errors:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/errors/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch error stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsResolved = async (errorId: string) => {
    try {
      await fetch(`/api/admin/errors/${errorId}/resolve`, {
        method: 'PATCH',
      });
      setErrors(errors.map(error => 
        error.id === errorId ? { ...error, resolved: true } : error
      ));
    } catch (error) {
      console.error('Failed to resolve error:', error);
    }
  };

  const filteredErrors = errors.filter(error => 
    filter === 'all' || error.level === filter
  );

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'destructive';
      case 'warning': return 'default';
      case 'info': return 'secondary';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Error Monitoring</h1>
          <p className="text-muted-foreground">
            Monitor and track application errors in real-time
          </p>
        </div>
        <Button onClick={() => { fetchErrors(); fetchStats(); }} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Error Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Errors</CardTitle>
              <Bug className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalErrors}</div>
              <p className="text-xs text-muted-foreground">
                Last 24 hours
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.errorRate.toFixed(2)}%</div>
              <p className="text-xs text-muted-foreground">
                Of total requests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Affected Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.affectedUsers}</div>
              <p className="text-xs text-muted-foreground">
                Unique users impacted
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Latest Error</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {errors.length > 0 ? formatTimestamp(Math.max(...errors.map(e => e.timestamp))).split(' ')[1] : 'None'}
              </div>
              <p className="text-xs text-muted-foreground">
                Most recent occurrence
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error Details */}
      <Tabs defaultValue="recent" className="space-y-4">
        <TabsList>
          <TabsTrigger value="recent">Recent Errors</TabsTrigger>
          <TabsTrigger value="top">Top Errors</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="recent" className="space-y-4">
          <div className="flex gap-2 mb-4">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
            >
              All
            </Button>
            <Button
              variant={filter === 'error' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('error')}
            >
              Errors
            </Button>
            <Button
              variant={filter === 'warning' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('warning')}
            >
              Warnings
            </Button>
            <Button
              variant={filter === 'info' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('info')}
            >
              Info
            </Button>
          </div>

          <div className="space-y-4">
            {filteredErrors.length === 0 ? (
              <Card>
                <CardContent className="py-8 text-center">
                  <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No errors found</p>
                </CardContent>
              </Card>
            ) : (
              filteredErrors.map((error) => (
                <Card key={error.id} className={error.resolved ? 'opacity-60' : ''}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Badge variant={getLevelColor(error.level) as any}>
                            {error.level}
                          </Badge>
                          {error.resolved && (
                            <Badge variant="outline">Resolved</Badge>
                          )}
                        </div>
                        <CardTitle className="text-lg">{error.message}</CardTitle>
                        <CardDescription>
                          {formatTimestamp(error.timestamp)} • {error.source} • {error.url}
                        </CardDescription>
                      </div>
                      {!error.resolved && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => markAsResolved(error.id)}
                        >
                          Mark Resolved
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  {error.stack && (
                    <CardContent>
                      <details className="space-y-2">
                        <summary className="cursor-pointer text-sm font-medium">
                          Stack Trace
                        </summary>
                        <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
                          {error.stack}
                        </pre>
                      </details>
                    </CardContent>
                  )}
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="top" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Most Frequent Errors</CardTitle>
              <CardDescription>
                Errors that occur most often in the last 24 hours
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats?.topErrors.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No error data available
                </p>
              ) : (
                <div className="space-y-3">
                  {stats?.topErrors.map((error, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <p className="font-medium">{error.message}</p>
                      </div>
                      <Badge variant="secondary">{error.count} occurrences</Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Error Trends</CardTitle>
              <CardDescription>
                Error frequency over the last 24 hours
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats?.errorsByHour.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No trend data available
                </p>
              ) : (
                <div className="space-y-2">
                  {stats?.errorsByHour.map((hour, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <span className="text-sm">{hour.hour}</span>
                      <div className="flex items-center gap-2">
                        <div 
                          className="bg-red-200 h-2 rounded"
                          style={{ 
                            width: `${Math.max(hour.count * 10, 10)}px`,
                            maxWidth: '200px'
                          }}
                        />
                        <span className="text-sm font-medium">{hour.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
