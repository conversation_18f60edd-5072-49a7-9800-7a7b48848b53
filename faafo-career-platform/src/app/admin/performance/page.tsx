'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Zap, 
  RefreshCw, 
  Database, 
  Globe, 
  Clock,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Timer
} from 'lucide-react';

interface PerformanceMetrics {
  pageLoadTime?: number;
  domContentLoaded?: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
  firstInputDelay?: number;
  timeToInteractive?: number;
  memoryUsage?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

interface PerformanceStats {
  averagePageLoadTime: number;
  averageFCP: number;
  averageLCP: number;
  averageCLS: number;
  averageFID: number;
  p95PageLoadTime: number;
  p95FCP: number;
  p95LCP: number;
  slowPagesCount: number;
  totalPages: number;
}

interface DatabasePerformance {
  averageQueryTime: number;
  slowQueries: number;
  totalQueries: number;
  successRate: number;
  topSlowQueries: Array<{
    query: string;
    duration: number;
    timestamp: string;
  }>;
}

export default function PerformanceMonitoringPage() {
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats | null>(null);
  const [databasePerformance, setDatabasePerformance] = useState<DatabasePerformance | null>(null);
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchPerformanceData();
    collectCurrentMetrics();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchPerformanceData();
      collectCurrentMetrics();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      
      // Fetch performance metrics
      const metricsResponse = await fetch('/api/performance-metrics');
      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setPerformanceStats(metricsData.stats);
      }

      // Fetch database performance
      const dbResponse = await fetch('/api/admin/database');
      if (dbResponse.ok) {
        const dbData = await dbResponse.json();
        if (dbData.success && dbData.data.performanceReport) {
          setDatabasePerformance({
            averageQueryTime: dbData.data.performanceReport.averageQueryTime || 0,
            slowQueries: dbData.data.performanceReport.slowQueries || 0,
            totalQueries: dbData.data.performanceReport.totalQueries || 0,
            successRate: dbData.data.performanceReport.successRate || 100,
            topSlowQueries: dbData.data.performanceReport.topSlowQueries || []
          });
        }
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch performance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const collectCurrentMetrics = () => {
    if (typeof window === 'undefined') return;

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    const metrics: PerformanceMetrics = {};

    if (navigation) {
      metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
      metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
    }

    const fcp = paint.find(entry => entry.name === 'first-contentful-paint');
    if (fcp) {
      metrics.firstContentfulPaint = fcp.startTime;
    }

    // Memory usage (if available)
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      metrics.memoryUsage = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }

    setCurrentMetrics(metrics);
  };

  const formatTime = (ms: number | undefined) => {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getPerformanceScore = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return { score: 'good', color: 'text-green-500' };
    if (value <= thresholds.poor) return { score: 'needs-improvement', color: 'text-yellow-500' };
    return { score: 'poor', color: 'text-red-500' };
  };

  const getScoreIcon = (score: string) => {
    switch (score) {
      case 'good': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'needs-improvement': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'poor': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading && !performanceStats) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Performance Monitoring</h1>
          <p className="text-muted-foreground">
            Monitor application performance and optimize user experience
          </p>
        </div>
        <div className="flex items-center gap-4">
          {lastUpdated && (
            <span className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <Button onClick={() => { fetchPerformanceData(); collectCurrentMetrics(); }} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Current Page Performance */}
      {currentMetrics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Current Page Performance
            </CardTitle>
            <CardDescription>
              Real-time performance metrics for this page
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{formatTime(currentMetrics.pageLoadTime)}</div>
                <div className="text-sm text-muted-foreground">Page Load Time</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{formatTime(currentMetrics.firstContentfulPaint)}</div>
                <div className="text-sm text-muted-foreground">First Contentful Paint</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{formatTime(currentMetrics.domContentLoaded)}</div>
                <div className="text-sm text-muted-foreground">DOM Content Loaded</div>
              </div>
              {currentMetrics.memoryUsage && (
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {formatBytes(currentMetrics.memoryUsage.usedJSHeapSize)}
                  </div>
                  <div className="text-sm text-muted-foreground">Memory Usage</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="web-vitals">Web Vitals</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {performanceStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Page Load</CardTitle>
                  <Timer className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatTime(performanceStats.averagePageLoadTime)}</div>
                  <p className="text-xs text-muted-foreground">
                    95th percentile: {formatTime(performanceStats.p95PageLoadTime)}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Pages</CardTitle>
                  <Globe className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{performanceStats.totalPages}</div>
                  <p className="text-xs text-muted-foreground">
                    {performanceStats.slowPagesCount} slow pages
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">First Contentful Paint</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatTime(performanceStats.averageFCP)}</div>
                  <p className="text-xs text-muted-foreground">
                    95th percentile: {formatTime(performanceStats.p95FCP)}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Largest Contentful Paint</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatTime(performanceStats.averageLCP)}</div>
                  <p className="text-xs text-muted-foreground">
                    95th percentile: {formatTime(performanceStats.p95LCP)}
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="web-vitals" className="space-y-4">
          {performanceStats && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* LCP */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getScoreIcon(getPerformanceScore(performanceStats.averageLCP, { good: 2500, poor: 4000 }).score)}
                    Largest Contentful Paint
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">{formatTime(performanceStats.averageLCP)}</div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Good (&lt; 2.5s)</span>
                      <span className="text-green-500">✓</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Needs Improvement (2.5s - 4s)</span>
                      <span className="text-yellow-500">⚠</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Poor (&gt; 4s)</span>
                      <span className="text-red-500">✗</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* FID */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getScoreIcon(getPerformanceScore(performanceStats.averageFID, { good: 100, poor: 300 }).score)}
                    First Input Delay
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">{formatTime(performanceStats.averageFID)}</div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Good (&lt; 100ms)</span>
                      <span className="text-green-500">✓</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Needs Improvement (100ms - 300ms)</span>
                      <span className="text-yellow-500">⚠</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Poor (&gt; 300ms)</span>
                      <span className="text-red-500">✗</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* CLS */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {getScoreIcon(getPerformanceScore(performanceStats.averageCLS, { good: 0.1, poor: 0.25 }).score)}
                    Cumulative Layout Shift
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">{performanceStats.averageCLS.toFixed(3)}</div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Good (&lt; 0.1)</span>
                      <span className="text-green-500">✓</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Needs Improvement (0.1 - 0.25)</span>
                      <span className="text-yellow-500">⚠</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Poor (&gt; 0.25)</span>
                      <span className="text-red-500">✗</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          {databasePerformance && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg Query Time</CardTitle>
                    <Database className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatTime(databasePerformance.averageQueryTime)}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Queries</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{databasePerformance.totalQueries}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Slow Queries</CardTitle>
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{databasePerformance.slowQueries}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{databasePerformance.successRate.toFixed(1)}%</div>
                  </CardContent>
                </Card>
              </div>

              {databasePerformance.topSlowQueries.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Slowest Queries</CardTitle>
                    <CardDescription>
                      Queries that need optimization attention
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {databasePerformance.topSlowQueries.slice(0, 5).map((query, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded">
                          <div className="flex-1">
                            <p className="font-mono text-sm">{query.query.substring(0, 100)}...</p>
                            <p className="text-xs text-muted-foreground">{query.timestamp}</p>
                          </div>
                          <Badge variant="secondary">{formatTime(query.duration)}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Recommendations</CardTitle>
              <CardDescription>
                Automated suggestions to improve application performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {performanceStats && performanceStats.averagePageLoadTime > 3000 && (
                  <div className="flex items-start gap-3 p-4 border rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Slow Page Load Times</h4>
                      <p className="text-sm text-muted-foreground">
                        Average page load time is {formatTime(performanceStats.averagePageLoadTime)}. 
                        Consider optimizing images, enabling compression, and reducing bundle size.
                      </p>
                    </div>
                  </div>
                )}

                {databasePerformance && databasePerformance.slowQueries > 0 && (
                  <div className="flex items-start gap-3 p-4 border rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Slow Database Queries</h4>
                      <p className="text-sm text-muted-foreground">
                        {databasePerformance.slowQueries} slow queries detected. 
                        Consider adding indexes, optimizing queries, or implementing caching.
                      </p>
                    </div>
                  </div>
                )}

                {currentMetrics?.memoryUsage && 
                 currentMetrics.memoryUsage.usedJSHeapSize / currentMetrics.memoryUsage.jsHeapSizeLimit > 0.8 && (
                  <div className="flex items-start gap-3 p-4 border rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">High Memory Usage</h4>
                      <p className="text-sm text-muted-foreground">
                        Memory usage is at {((currentMetrics.memoryUsage.usedJSHeapSize / currentMetrics.memoryUsage.jsHeapSizeLimit) * 100).toFixed(1)}%. 
                        Consider optimizing memory usage and checking for memory leaks.
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-start gap-3 p-4 border rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Performance Monitoring Active</h4>
                    <p className="text-sm text-muted-foreground">
                      Real-time performance monitoring is collecting metrics to help optimize your application.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
