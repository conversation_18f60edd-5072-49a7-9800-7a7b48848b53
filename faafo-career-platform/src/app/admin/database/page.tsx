'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Database, 
  RefreshCw, 
  Zap, 
  Settings,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Activity,
  Cpu,
  HardDrive
} from 'lucide-react';

interface DatabaseStats {
  totalUsers: number;
  totalAssessments: number;
  totalResources: number;
  avgQueryTime: number;
  slowQueries: Array<{
    query: string;
    executionTime: number;
    timestamp: string;
  }>;
  cache: {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: number;
  };
}

interface ConnectionPoolStats {
  activeConnections: number;
  totalConnections: number;
  failedConnections: number;
  averageConnectionTime: number;
  health: 'healthy' | 'degraded' | 'unhealthy';
}

interface OptimizationRecommendations {
  recommendations: string[];
  priority: 'low' | 'medium' | 'high';
  category: string;
}

export default function DatabaseOptimizationPage() {
  const [databaseStats, setDatabaseStats] = useState<DatabaseStats | null>(null);
  const [connectionStats, setConnectionStats] = useState<ConnectionPoolStats | null>(null);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [optimizing, setOptimizing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchDatabaseData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchDatabaseData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchDatabaseData = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/admin/database');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDatabaseStats(data.data.overview);
          setConnectionStats(data.data.connectionPool);
          setRecommendations(data.data.recommendations || []);
          setLastUpdated(new Date());
        }
      }
    } catch (error) {
      console.error('Failed to fetch database data:', error);
    } finally {
      setLoading(false);
    }
  };

  const runOptimization = async () => {
    try {
      setOptimizing(true);
      
      const response = await fetch('/api/admin/database/optimize', {
        method: 'POST',
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Refresh data after optimization
          await fetchDatabaseData();
        }
      }
    } catch (error) {
      console.error('Failed to run optimization:', error);
    } finally {
      setOptimizing(false);
    }
  };

  const formatTime = (ms: number | undefined) => {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-500';
      case 'degraded': return 'text-yellow-500';
      case 'unhealthy': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'unhealthy': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading && !databaseStats) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Database Optimization</h1>
          <p className="text-muted-foreground">
            Monitor and optimize database performance for production
          </p>
        </div>
        <div className="flex items-center gap-4">
          {lastUpdated && (
            <span className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <Button onClick={fetchDatabaseData} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={runOptimization} disabled={optimizing} className="bg-blue-600 hover:bg-blue-700">
            <Zap className={`h-4 w-4 mr-2 ${optimizing ? 'animate-pulse' : ''}`} />
            {optimizing ? 'Optimizing...' : 'Optimize Database'}
          </Button>
        </div>
      </div>

      {/* Database Overview */}
      {databaseStats && connectionStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Connection Health</CardTitle>
              {getHealthIcon(connectionStats.health)}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <Badge variant={connectionStats.health === 'healthy' ? 'default' : 'destructive'}>
                  {connectionStats.health}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                {connectionStats.activeConnections}/{connectionStats.totalConnections} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Query Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(databaseStats.avgQueryTime)}</div>
              <p className="text-xs text-muted-foreground">
                {databaseStats.slowQueries.length} slow queries
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cache Hit Rate</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{databaseStats.cache.hitRate.toFixed(1)}%</div>
              <Progress value={databaseStats.cache.hitRate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Records</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(databaseStats.totalUsers + databaseStats.totalAssessments + databaseStats.totalResources).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all tables
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="connections">Connections</TabsTrigger>
          <TabsTrigger value="queries">Query Performance</TabsTrigger>
          <TabsTrigger value="cache">Cache Performance</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {databaseStats && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Database Statistics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Users:</span>
                    <span className="font-medium">{databaseStats.totalUsers.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Assessments:</span>
                    <span className="font-medium">{databaseStats.totalAssessments.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Resources:</span>
                    <span className="font-medium">{databaseStats.totalResources.toLocaleString()}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Avg Query Time:</span>
                    <span className="font-medium">{formatTime(databaseStats.avgQueryTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Slow Queries:</span>
                    <span className="font-medium">{databaseStats.slowQueries.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cache Hit Rate:</span>
                    <span className="font-medium">{databaseStats.cache.hitRate.toFixed(1)}%</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <HardDrive className="h-5 w-5" />
                    Cache Statistics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Cache Size:</span>
                    <span className="font-medium">{databaseStats.cache.size}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Max Size:</span>
                    <span className="font-medium">{databaseStats.cache.maxSize}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage:</span>
                    <span className="font-medium">{formatBytes(databaseStats.cache.memoryUsage)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="connections" className="space-y-4">
          {connectionStats && (
            <Card>
              <CardHeader>
                <CardTitle>Connection Pool Status</CardTitle>
                <CardDescription>
                  Monitor database connection pool health and performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold">{connectionStats.activeConnections}</div>
                    <div className="text-sm text-muted-foreground">Active Connections</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">{connectionStats.totalConnections}</div>
                    <div className="text-sm text-muted-foreground">Total Connections</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">{connectionStats.failedConnections}</div>
                    <div className="text-sm text-muted-foreground">Failed Connections</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">{formatTime(connectionStats.averageConnectionTime)}</div>
                    <div className="text-sm text-muted-foreground">Avg Connection Time</div>
                  </div>
                </div>
                
                <div className="mt-6">
                  <div className="flex items-center gap-2 mb-2">
                    {getHealthIcon(connectionStats.health)}
                    <span className={`font-medium ${getHealthColor(connectionStats.health)}`}>
                      Connection Pool Health: {connectionStats.health.charAt(0).toUpperCase() + connectionStats.health.slice(1)}
                    </span>
                  </div>
                  <Progress 
                    value={(connectionStats.activeConnections / connectionStats.totalConnections) * 100} 
                    className="mt-2" 
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Connection utilization: {((connectionStats.activeConnections / connectionStats.totalConnections) * 100).toFixed(1)}%
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="queries" className="space-y-4">
          {databaseStats && databaseStats.slowQueries.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Slow Query Analysis</CardTitle>
                <CardDescription>
                  Queries that need optimization attention
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {databaseStats.slowQueries.slice(0, 10).map((query, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <p className="font-mono text-sm">{query.query.substring(0, 100)}...</p>
                        <p className="text-xs text-muted-foreground">{query.timestamp}</p>
                      </div>
                      <Badge variant="secondary">{formatTime(query.executionTime)}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          {databaseStats && (
            <Card>
              <CardHeader>
                <CardTitle>Cache Performance Analysis</CardTitle>
                <CardDescription>
                  Query cache efficiency and memory usage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Cache Efficiency</h4>
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm">Hit Rate</span>
                          <span className="text-sm font-medium">{databaseStats.cache.hitRate.toFixed(1)}%</span>
                        </div>
                        <Progress value={databaseStats.cache.hitRate} />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm">Cache Utilization</span>
                          <span className="text-sm font-medium">
                            {((databaseStats.cache.size / databaseStats.cache.maxSize) * 100).toFixed(1)}%
                          </span>
                        </div>
                        <Progress value={(databaseStats.cache.size / databaseStats.cache.maxSize) * 100} />
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-3">Memory Usage</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Current Size:</span>
                        <span className="font-medium">{databaseStats.cache.size} entries</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Max Size:</span>
                        <span className="font-medium">{databaseStats.cache.maxSize} entries</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Memory Usage:</span>
                        <span className="font-medium">{formatBytes(databaseStats.cache.memoryUsage)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>
                Automated suggestions to improve database performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.length > 0 ? (
                  recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-3 p-4 border rounded-lg">
                      <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5" />
                      <div>
                        <p className="text-sm">{recommendation}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex items-center gap-3 p-4 border rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <h4 className="font-medium">Database Performance Optimal</h4>
                      <p className="text-sm text-muted-foreground">
                        No optimization recommendations at this time. Your database is performing well.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
