'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  RefreshCw, 
  Database, 
  Mail, 
  Brain, 
  Shield, 
  Server,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock
} from 'lucide-react';

interface HealthService {
  status: 'up' | 'down' | 'configured' | 'not_configured';
  responseTime?: number;
  error?: string;
  provider?: string;
  providers?: string[];
}

interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  services: {
    database: HealthService;
    email: HealthService;
    ai: HealthService;
    cache: HealthService;
    errorTracking: HealthService;
  };
  environment: string;
  nodeVersion: string;
}

export default function HealthMonitoringPage() {
  const [healthData, setHealthData] = useState<HealthResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchHealthData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchHealthData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchHealthData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/health');
      if (response.ok) {
        const data = await response.json();
        setHealthData(data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Failed to fetch health data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
      case 'configured':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'degraded':
      case 'not_configured':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'unhealthy':
      case 'down':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
      case 'configured':
        return 'default';
      case 'degraded':
      case 'not_configured':
        return 'secondary';
      case 'unhealthy':
      case 'down':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  if (loading && !healthData) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Health</h1>
          <p className="text-muted-foreground">
            Monitor system status and service availability
          </p>
        </div>
        <div className="flex items-center gap-4">
          {lastUpdated && (
            <span className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <Button onClick={fetchHealthData} variant="outline" disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {healthData && (
        <>
          {/* Overall Status */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(healthData.status)}
                  <div>
                    <CardTitle className="text-2xl">
                      System Status: {healthData.status.charAt(0).toUpperCase() + healthData.status.slice(1)}
                    </CardTitle>
                    <CardDescription>
                      Environment: {healthData.environment} • Version: {healthData.version}
                    </CardDescription>
                  </div>
                </div>
                <Badge variant={getStatusColor(healthData.status) as any} className="text-lg px-4 py-2">
                  {healthData.status.toUpperCase()}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{formatUptime(healthData.uptime)}</div>
                  <div className="text-sm text-muted-foreground">Uptime</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{healthData.nodeVersion}</div>
                  <div className="text-sm text-muted-foreground">Node.js Version</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {new Date(healthData.timestamp).toLocaleTimeString()}
                  </div>
                  <div className="text-sm text-muted-foreground">Last Check</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Service Status */}
          <Tabs defaultValue="services" className="space-y-4">
            <TabsList>
              <TabsTrigger value="services">Services</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>

            <TabsContent value="services" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Database */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Database
                    </CardTitle>
                    {getStatusIcon(healthData.services.database.status)}
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Badge variant={getStatusColor(healthData.services.database.status) as any}>
                        {healthData.services.database.status}
                      </Badge>
                    </div>
                    {healthData.services.database.responseTime && (
                      <p className="text-xs text-muted-foreground">
                        Response: {healthData.services.database.responseTime}ms
                      </p>
                    )}
                    {healthData.services.database.error && (
                      <p className="text-xs text-red-500 mt-1">
                        {healthData.services.database.error}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Email Service */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email Service
                    </CardTitle>
                    {getStatusIcon(healthData.services.email.status)}
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Badge variant={getStatusColor(healthData.services.email.status) as any}>
                        {healthData.services.email.status}
                      </Badge>
                    </div>
                    {healthData.services.email.provider && (
                      <p className="text-xs text-muted-foreground">
                        Provider: {healthData.services.email.provider}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* AI Services */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Brain className="h-4 w-4" />
                      AI Services
                    </CardTitle>
                    {getStatusIcon(healthData.services.ai.status)}
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Badge variant={getStatusColor(healthData.services.ai.status) as any}>
                        {healthData.services.ai.status}
                      </Badge>
                    </div>
                    {healthData.services.ai.providers && healthData.services.ai.providers.length > 0 && (
                      <p className="text-xs text-muted-foreground">
                        Providers: {healthData.services.ai.providers.join(', ')}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Cache Service */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Server className="h-4 w-4" />
                      Cache Service
                    </CardTitle>
                    {getStatusIcon(healthData.services.cache.status)}
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Badge variant={getStatusColor(healthData.services.cache.status) as any}>
                        {healthData.services.cache.status}
                      </Badge>
                    </div>
                    {healthData.services.cache.provider && (
                      <p className="text-xs text-muted-foreground">
                        Provider: {healthData.services.cache.provider}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Error Tracking */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      Error Tracking
                    </CardTitle>
                    {getStatusIcon(healthData.services.errorTracking.status)}
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Badge variant={getStatusColor(healthData.services.errorTracking.status) as any}>
                        {healthData.services.errorTracking.status}
                      </Badge>
                    </div>
                    {healthData.services.errorTracking.provider && (
                      <p className="text-xs text-muted-foreground">
                        Provider: {healthData.services.errorTracking.provider}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* System Status Summary */}
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      Overall Health
                    </CardTitle>
                    {getStatusIcon(healthData.status)}
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      <Badge variant={getStatusColor(healthData.status) as any}>
                        {healthData.status}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      All systems operational
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Service Information</CardTitle>
                  <CardDescription>
                    Comprehensive status and configuration details
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <pre className="text-sm bg-muted p-4 rounded overflow-x-auto">
                    {JSON.stringify(healthData, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Health Check History</CardTitle>
                  <CardDescription>
                    Historical health check data and trends
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground text-center py-8">
                    Health check history feature coming soon...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
}
