"use client";

import React from "react";
import Link from 'next/link';
import { Send, GraduationCap, DollarSign, Lightbulb } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

// Force dynamic rendering
export const dynamic = 'force-dynamic';


interface HeroSectionProps {
  title: string;
  subtitle: string;
  primaryAction: {
    text: string;
    href: string;
    icon?: React.ReactNode;
  };
  secondaryAction: {
    text: string;
    href: string;
  };
}

function HeroSection({
  title,
  subtitle,
  primaryAction,
  secondaryAction,
}: HeroSectionProps) {
  return (
    <section className="relative min-h-[calc(100vh-80px)] flex flex-col items-center justify-center bg-background text-foreground pt-20 pb-32 px-4 overflow-hidden text-center">
      <div className="relative z-10 flex flex-col items-center gap-8">
        <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold leading-tight">
          {title}
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl max-w-2xl text-foreground">
          {subtitle}
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4 mt-6">
          <Button size="lg" asChild className="bg-primary hover:bg-primary/90 text-primary-foreground text-lg px-8 py-3">
            <Link href={primaryAction.href} className="flex items-center gap-2">
              {primaryAction.icon}
              {primaryAction.text}
            </Link>
          </Button>
          <Button size="lg" asChild className="bg-secondary hover:bg-secondary/80 text-secondary-foreground text-lg px-8 py-3">
            <Link href={secondaryAction.href}>
              {secondaryAction.text}
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-card p-6 rounded-lg text-center flex flex-col items-center">
      <div className="text-primary mb-4">{icon}</div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground text-center">{description}</p>
    </div>
  );
}

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      <HeroSection
        title="Find Your Path. Build a Fulfilling Career."
        subtitle="Join 10,000+ users taking control of their careers."
        primaryAction={{
          text: "Get Started",
          href: "/signup",
          icon: <Send className="h-5 w-5 transform -rotate-45" />,
        }}
        secondaryAction={{
          text: "Learn More",
          href: "/",
        }}
      />

      {/* Features Section */}
      <section className="py-20 bg-card text-foreground px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-4xl font-bold mb-12">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FeatureCard
              icon={<GraduationCap className="h-12 w-12" />}
              title="Personalized Career Paths"
              description="Discover tailored career paths based on your skills, interests, and goals."
            />
            <FeatureCard
              icon={<DollarSign className="h-12 w-12" />}
              title="Financial Freedom Planning"
              description="Plan your finances to achieve financial independence and pursue your dream career."
            />
            <FeatureCard
              icon={<Lightbulb className="h-12 w-12" />}
              title="Skill Development Resources"
              description="Access curated resources and courses to bridge your skill gaps."
            />
          </div>
        </div>
      </section>
    </div>
  );
}
