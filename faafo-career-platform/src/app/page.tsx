// Force dynamic rendering
export const dynamic = 'force-dynamic';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative min-h-[calc(100vh-80px)] flex flex-col items-center justify-center bg-white text-gray-900 pt-20 pb-32 px-4 overflow-hidden text-center">
        <div className="relative z-10 flex flex-col items-center gap-8">
          <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold leading-tight">
            Find Your Path. Build a Fulfilling Career.
          </h1>
          <p className="text-lg sm:text-xl md:text-2xl max-w-2xl text-gray-900">
            Join 10,000+ users taking control of their careers.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4 mt-6">
            <a
              href="/signup"
              className="inline-flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-3 rounded-md font-medium transition-colors"
            >
              Get Started
            </a>
            <a
              href="/career-paths"
              className="inline-flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-900 text-lg px-8 py-3 rounded-md font-medium transition-colors"
            >
              Explore Career Paths
            </a>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 text-gray-900 px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-4xl font-bold mb-12">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg text-center flex flex-col items-center border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="text-gray-600 mb-4 text-4xl">🎓</div>
              <h3 className="text-xl font-semibold mb-2">Self-Assessment</h3>
              <p className="text-gray-600 text-center">Take our comprehensive assessment to discover career paths that match your goals and situation.</p>
            </div>
            <div className="bg-white p-6 rounded-lg text-center flex flex-col items-center border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="text-gray-600 mb-4 text-4xl">💼</div>
              <h3 className="text-xl font-semibold mb-2">Career Path Exploration</h3>
              <p className="text-gray-600 text-center">Explore detailed career paths with pros, cons, and actionable step-by-step guides.</p>
            </div>
            <div className="bg-white p-6 rounded-lg text-center flex flex-col items-center border border-gray-200 hover:shadow-lg transition-shadow">
              <div className="text-gray-600 mb-4 text-4xl">💰</div>
              <h3 className="text-xl font-semibold mb-2">Freedom Fund Calculator</h3>
              <p className="text-gray-600 text-center">Calculate your financial runway and track your progress towards career transition security.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}






