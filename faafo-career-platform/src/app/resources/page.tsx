'use client';

import React, { useState, useMemo, useEffect } from 'react';
import Link from 'next/link';
import { ExternalLink, Heart, Brain, DollarSign, Users, BookOpen, Video, Headphones, Search, Filter, Code, Shield, BarChart, Blocks, Megaphone, Cpu, Star, Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import PageLayout from '@/components/layout/PageLayout';

interface Resource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  author?: string;
  duration?: string;
  skillLevel?: string;
  cost?: string;
  averageRating?: number;
  totalRatings?: number;
  careerPaths?: {
    id: string;
    name: string;
    slug: string;
  }[];
}

const mindsetResources: Resource[] = [
  // Fear & Anxiety Resources
  {
    id: '1',
    title: 'Overcoming Six Fears of Midlife Career Change',
    description: 'A comprehensive guide to understanding and conquering the fears that hold you back from making a career transition.',
    url: 'https://www.linkedin.com/pulse/overcoming-six-fears-midlife-career-change-guide-joanne-savoie-malone-xwpme',
    type: 'article',
    category: 'fear',
    author: '<PERSON>',
    duration: '8 min read'
  },
  {
    id: '2',
    title: 'Why Career Transition Is So Hard',
    description: 'Understanding the emotional journey of career transitions and how to navigate psychological challenges.',
    url: 'https://hbr.org/2023/11/why-career-transition-is-so-hard',
    type: 'article',
    category: 'fear',
    author: 'Harvard Business Review',
    duration: '12 min read'
  },
  {
    id: '3',
    title: 'Mindfulness Exercises for Stress Reduction',
    description: 'Meditation and mindfulness techniques to manage anxiety and stress during career changes.',
    url: 'https://www.mayoclinic.org/healthy-lifestyle/consumer-health/in-depth/mindfulness-exercises/art-20046356',
    type: 'article',
    category: 'fear',
    author: 'Mayo Clinic',
    duration: '6 min read'
  },

  // Financial Planning Resources
  {
    id: '4',
    title: 'How to Think Strategically About a Career Transition',
    description: 'Learn strategic approaches to planning and executing your career transition with financial considerations.',
    url: 'https://hbr.org/2023/09/how-to-think-strategically-about-a-career-transition',
    type: 'article',
    category: 'financial',
    author: 'Harvard Business Review',
    duration: '10 min read'
  },
  {
    id: '5',
    title: 'Financial Planning for Transition (TAP)',
    description: 'Comprehensive guide for financial planning during career transitions from military and government resources.',
    url: 'https://www.tapevents.mil/resources',
    type: 'article',
    category: 'financial',
    author: 'U.S. Department of Defense',
    duration: '5 min read'
  },
  {
    id: '6',
    title: 'Side Hustle to Full-Time: A Transition Guide',
    description: 'How to successfully transition from a side project to a full-time career with minimal risk.',
    url: 'https://www.entrepreneur.com/growing-a-business/how-to-turn-your-side-hustle-into-a-full-time-business/325890',
    type: 'article',
    category: 'financial',
    author: 'Entrepreneur',
    duration: '11 min read'
  },

  // Confidence & Imposter Syndrome Resources
  {
    id: '7',
    title: 'Overcoming Career Fears: Building Confidence',
    description: 'Practical strategies to build self-confidence and overcome self-doubt when changing careers.',
    url: 'https://www.linkedin.com/pulse/overcoming-career-fears-guide-building-confidence-moving-sulista-1amwe',
    type: 'article',
    category: 'imposter',
    author: 'Vaclav Sulista',
    duration: '7 min read'
  },
  {
    id: '8',
    title: 'Dealing with Imposter Syndrome',
    description: 'Strategies to overcome self-doubt and imposter syndrome when pursuing new career opportunities.',
    url: 'https://hbr.org/2021/02/stop-telling-women-they-have-imposter-syndrome',
    type: 'article',
    category: 'imposter',
    author: 'Harvard Business Review',
    duration: '12 min read'
  },
  {
    id: '9',
    title: 'The Art of Self-Compassion in Career Transitions',
    description: 'Learning to be kind to yourself during the ups and downs of career change.',
    url: 'https://self-compassion.org/the-three-elements-of-self-compassion-2/',
    type: 'article',
    category: 'imposter',
    author: 'Dr. Kristin Neff',
    duration: '10 min read'
  },

  // Strategic Planning Resources
  {
    id: '10',
    title: 'The Complete Guide to Career Pivoting',
    description: 'A comprehensive resource for planning and executing a successful career pivot at any stage of life.',
    url: 'https://www.linkedin.com/pulse/complete-guide-career-pivoting-jenny-foss/',
    type: 'article',
    category: 'planning',
    author: 'LinkedIn Learning',
    duration: '15 min read'
  },
  {
    id: '11',
    title: 'Designing Your Life: Career Design Thinking',
    description: 'Key insights from the Stanford course on applying design thinking to career and life decisions.',
    url: 'https://designingyour.life/',
    type: 'article',
    category: 'planning',
    author: 'Bill Burnett & Dave Evans',
    duration: '15 min read'
  },

  // Motivation & Inspiration Resources
  {
    id: '12',
    title: 'TED Talk: How to Find Work You Love',
    description: 'Scott Dinsmore shares insights on discovering meaningful work and overcoming fear.',
    url: 'https://www.ted.com/talks/scott_dinsmore_how_to_find_work_you_love',
    type: 'video',
    category: 'motivation',
    author: 'TED',
    duration: '18 min watch'
  },
  {
    id: '13',
    title: 'The Career Change Podcast',
    description: 'Weekly interviews with people who have successfully changed careers, sharing their stories and strategies.',
    url: 'https://thecareerchangepodcast.com/',
    type: 'podcast',
    category: 'motivation',
    author: 'Anna Lundberg',
    duration: 'Various episodes'
  },
  {
    id: '14',
    title: 'Overcoming Job Search Fear in Midlife Career Change',
    description: 'Strategies for building professional networks and overcoming fear during career transitions.',
    url: 'https://www.nocodeinstitute.io/post/overcome-fear-of-job-search-in-midlife-career-change',
    type: 'article',
    category: 'motivation',
    author: 'No Code Institute',
    duration: '9 min read'
  },
  {
    id: '15',
    title: 'Overcoming the Fear of Career Change: Balancing Risk and Reward',
    description: 'How to handle uncertainty and make strategic decisions when making career transitions.',
    url: 'https://www.linkedin.com/pulse/overcoming-fear-career-change-balancing-risk-reward-theresa-white-ghpgf',
    type: 'article',
    category: 'motivation',
    author: 'Theresa White',
    duration: '8 min read'
  }
];

// Learning Resources for Skill Development
const learningResources: Resource[] = [
  // Cybersecurity
  {
    id: 'cyber-1',
    title: 'Ethical Hacking Essentials (E|HE)',
    description: 'Strong foundations in ethical hacking and penetration testing for entry-level careers',
    url: 'https://www.eccouncil.org/train-certify/ethical-hacking-essentials-ehe/',
    type: 'course',
    category: 'cybersecurity',
    author: 'EC-Council',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free'
  },
  {
    id: 'cyber-2',
    title: 'Network Defense Essentials (N|DE)',
    description: 'Fundamentals of network security, protocols, controls, and identity/access management',
    url: 'https://www.eccouncil.org/train-certify/network-defense-essentials-nde/',
    type: 'course',
    category: 'cybersecurity',
    author: 'EC-Council',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free'
  },
  {
    id: 'cyber-3',
    title: 'CISA Cybersecurity Training',
    description: 'Government-backed cybersecurity training from beginner to advanced levels',
    url: 'https://www.cisa.gov/cybersecurity-training-exercises',
    type: 'course',
    category: 'cybersecurity',
    author: 'CISA',
    duration: 'Various modules',
    skillLevel: 'beginner',
    cost: 'free'
  },
  // Data Science
  {
    id: 'ds-1',
    title: 'Data Science: Machine Learning',
    description: 'Basics of machine learning, cross-validation, popular algorithms, and avoiding overtraining',
    url: 'https://pll.harvard.edu/course/data-science-machine-learning',
    type: 'course',
    category: 'data-science',
    author: 'Harvard University',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free'
  },
  {
    id: 'ds-2',
    title: 'Machine Learning Crash Course',
    description: 'Fast-paced introduction with animated videos, interactive visualizations, and hands-on practice',
    url: 'https://developers.google.com/machine-learning/crash-course',
    type: 'course',
    category: 'data-science',
    author: 'Google',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free'
  },
  // AI
  {
    id: 'ai-1',
    title: 'AI For Everyone',
    description: 'Non-technical introduction to AI concepts and applications',
    url: 'https://www.coursera.org/learn/ai-for-everyone',
    type: 'course',
    category: 'ai',
    author: 'DeepLearning.AI',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'freemium'
  },
  {
    id: 'ai-2',
    title: 'Elements of AI',
    description: 'Introduction to AI concepts and their practical applications',
    url: 'https://www.elementsofai.com/',
    type: 'course',
    category: 'ai',
    author: 'University of Helsinki',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free'
  },
  // Digital Marketing
  {
    id: 'dm-1',
    title: 'Fundamentals of Digital Marketing',
    description: 'Comprehensive introduction to digital marketing concepts and tools',
    url: 'https://learndigital.withgoogle.com/digitalgarage/course/digital-marketing',
    type: 'course',
    category: 'digital-marketing',
    author: 'Google Digital Garage',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free'
  },
  {
    id: 'dm-2',
    title: 'Social Media Marketing',
    description: 'Introduction to social media marketing strategies and platforms',
    url: 'https://academy.hubspot.com/courses/social-media',
    type: 'course',
    category: 'digital-marketing',
    author: 'HubSpot Academy',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'free'
  },
  // Blockchain
  {
    id: 'bc-1',
    title: 'Blockchain Basics',
    description: 'Introduction to blockchain technology, cryptocurrency, and smart contracts',
    url: 'https://www.coursera.org/learn/blockchain-basics',
    type: 'course',
    category: 'blockchain',
    author: 'University at Buffalo',
    duration: 'Self-paced',
    skillLevel: 'beginner',
    cost: 'freemium'
  },
  // Project Management
  {
    id: 'pm-1',
    title: 'Project Management Foundations',
    description: 'Introduction to project management principles and methodologies',
    url: 'https://www.linkedin.com/learning/project-management-foundations-2019',
    type: 'course',
    category: 'project-management',
    author: 'LinkedIn Learning',
    duration: 'Video course',
    skillLevel: 'beginner',
    cost: 'freemium'
  }
];

const allResources = [...mindsetResources, ...learningResources];

const categoryInfo = {
  // Mindset Categories
  fear: {
    title: 'Overcoming Fear & Anxiety',
    icon: Heart,
    description: 'Resources to help you manage fear, anxiety, and uncertainty during career transitions.',
    color: 'text-red-600 dark:text-red-400'
  },
  financial: {
    title: 'Financial Planning',
    icon: DollarSign,
    description: 'Practical advice for managing finances and building security during career changes.',
    color: 'text-green-600 dark:text-green-400'
  },
  imposter: {
    title: 'Imposter Syndrome',
    icon: Brain,
    description: 'Strategies to overcome self-doubt and build confidence in your abilities.',
    color: 'text-purple-600 dark:text-purple-400'
  },
  motivation: {
    title: 'Motivation & Mindset',
    icon: Users,
    description: 'Inspiration and practical advice to maintain motivation throughout your journey.',
    color: 'text-gray-600 dark:text-gray-400'
  },
  planning: {
    title: 'Strategic Planning',
    icon: BookOpen,
    description: 'Frameworks and tools for planning and executing your career transition.',
    color: 'text-orange-600 dark:text-orange-400'
  },
  // Skill Development Categories
  cybersecurity: {
    title: 'Cybersecurity',
    icon: Shield,
    description: 'Learn ethical hacking, network security, and digital forensics skills.',
    color: 'text-red-600 dark:text-red-400'
  },
  'data-science': {
    title: 'Data Science',
    icon: BarChart,
    description: 'Master machine learning, data analysis, and statistical modeling.',
    color: 'text-gray-600 dark:text-gray-400'
  },
  blockchain: {
    title: 'Blockchain',
    icon: Blocks,
    description: 'Understand blockchain technology, smart contracts, and cryptocurrency.',
    color: 'text-yellow-600 dark:text-yellow-400'
  },
  'project-management': {
    title: 'Project Management',
    icon: Users,
    description: 'Learn agile methodologies, team leadership, and project planning.',
    color: 'text-green-600 dark:text-green-400'
  },
  'digital-marketing': {
    title: 'Digital Marketing',
    icon: Megaphone,
    description: 'Master SEO, social media marketing, and content strategy.',
    color: 'text-pink-600 dark:text-pink-400'
  },
  ai: {
    title: 'Artificial Intelligence',
    icon: Cpu,
    description: 'Explore AI concepts, machine learning, and neural networks.',
    color: 'text-purple-600 dark:text-purple-400'
  },
  'web-development': {
    title: 'Web Development',
    icon: Code,
    description: 'Learn frontend and backend development skills.',
    color: 'text-indigo-600 dark:text-indigo-400'
  },
  'mobile-development': {
    title: 'Mobile Development',
    icon: Code,
    description: 'Build mobile applications for iOS and Android.',
    color: 'text-teal-600 dark:text-teal-400'
  },
  'cloud-computing': {
    title: 'Cloud Computing',
    icon: Shield,
    description: 'Learn cloud platforms and distributed systems.',
    color: 'text-sky-600 dark:text-sky-400'
  },
  'language-learning': {
    title: 'Language Learning',
    icon: BookOpen,
    description: 'Learn new languages for global opportunities.',
    color: 'text-rose-600 dark:text-rose-400'
  },
  'entrepreneurship': {
    title: 'Entrepreneurship',
    icon: Users,
    description: 'Start and grow your own business.',
    color: 'text-amber-600 dark:text-amber-400'
  }
};

const getResourceIcon = (type: Resource['type']) => {
  switch (type) {
    case 'article':
      return BookOpen;
    case 'video':
      return Video;
    case 'podcast':
      return Headphones;
    case 'book':
      return BookOpen;
    case 'course':
      return BookOpen;
    case 'certification':
      return Shield;
    case 'tutorial':
      return Code;
    default:
      return BookOpen;
  }
};

export default function ResourcesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedResourceType, setSelectedResourceType] = useState<string>('all'); // 'mindset' or 'learning' or 'all'
  const [selectedSkillLevel, setSelectedSkillLevel] = useState<string>('all');
  const [selectedCost, setSelectedCost] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('relevance'); // 'relevance', 'rating', 'title', 'newest'
  const [databaseResources, setDatabaseResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const categories = Object.keys(categoryInfo) as Array<keyof typeof categoryInfo>;
  const types = ['all', 'article', 'video', 'podcast', 'book', 'course', 'certification', 'tutorial'];
  const skillLevels = ['all', 'beginner', 'intermediate', 'advanced'];
  const costOptions = ['all', 'free', 'freemium', 'paid'];
  const sortOptions = [
    { value: 'relevance', label: 'Relevance' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'title', label: 'Title A-Z' },
    { value: 'newest', label: 'Newest First' },
  ];

  // Fetch database resources
  useEffect(() => {
    fetchDatabaseResources();
  }, []);

  const fetchDatabaseResources = async () => {
    try {
      setLoading(true);
      // Request all resources by setting a high limit
      const response = await fetch('/api/learning-resources?limit=1000');

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDatabaseResources(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching database resources:', error);
      setError('Failed to load resources from database');
    } finally {
      setLoading(false);
    }
  };

  const { filteredResources, totalAvailableResources } = useMemo(() => {
    // Combine static and database resources
    let resourcesToFilter = [...allResources, ...databaseResources];

    // Remove duplicates based on URL
    const uniqueResources = resourcesToFilter.reduce((acc, resource) => {
      const existing = acc.find(r => r.url === resource.url);
      if (!existing) {
        acc.push(resource);
      }
      return acc;
    }, [] as Resource[]);

    // Filter by resource type (mindset vs learning)
    if (selectedResourceType === 'mindset') {
      resourcesToFilter = mindsetResources; // Keep static mindset resources
    } else if (selectedResourceType === 'learning') {
      // Combine static learning resources with database resources for skill development
      const combinedLearningResources = [...learningResources, ...databaseResources];
      // Remove duplicates based on URL
      resourcesToFilter = combinedLearningResources.reduce((acc, resource) => {
        const existing = acc.find(r => r.url === resource.url);
        if (!existing) {
          acc.push(resource);
        }
        return acc;
      }, [] as Resource[]);
    } else {
      resourcesToFilter = uniqueResources; // Use combined unique resources
    }

    const totalAvailable = resourcesToFilter.length;

    // Apply filters
    const filtered = resourcesToFilter.filter(resource => {
      const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.author?.toLowerCase().includes(searchTerm.toLowerCase());

      // Handle category matching for both static and database resources
      let matchesCategory = selectedCategory === 'all';
      if (!matchesCategory) {
        // For static resources, use direct comparison
        if (resource.category === selectedCategory) {
          matchesCategory = true;
        }
        // For database resources, handle enum values
        const categoryMap: { [key: string]: string } = {
          'cybersecurity': 'CYBERSECURITY',
          'data-science': 'DATA_SCIENCE',
          'blockchain': 'BLOCKCHAIN',
          'project-management': 'PROJECT_MANAGEMENT',
          'digital-marketing': 'DIGITAL_MARKETING',
          'ai': 'ARTIFICIAL_INTELLIGENCE',
          'web-development': 'WEB_DEVELOPMENT',
          'mobile-development': 'MOBILE_DEVELOPMENT',
          'cloud-computing': 'CLOUD_COMPUTING',
          'financial': 'FINANCIAL_LITERACY',
          'language-learning': 'LANGUAGE_LEARNING',
          'entrepreneurship': 'ENTREPRENEURSHIP'
        };
        if (resource.category === categoryMap[selectedCategory]) {
          matchesCategory = true;
        }
      }

      const matchesType = selectedType === 'all' ||
                         resource.type.toLowerCase() === selectedType ||
                         resource.type === selectedType.toUpperCase();

      const matchesSkillLevel = selectedSkillLevel === 'all' ||
                               resource.skillLevel?.toLowerCase() === selectedSkillLevel ||
                               resource.skillLevel === selectedSkillLevel.toUpperCase();

      const matchesCost = selectedCost === 'all' ||
                         resource.cost?.toLowerCase() === selectedCost ||
                         resource.cost === selectedCost.toUpperCase();

      return matchesSearch && matchesCategory && matchesType && matchesSkillLevel && matchesCost;
    });

    // Apply sorting
    switch (sortBy) {
      case 'title':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'rating':
        filtered.sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0));
        break;
      case 'newest':
        // For database resources, this would use createdAt; for static, maintain order
        break;
      case 'relevance':
      default:
        // Keep original order for relevance
        break;
    }

    return { filteredResources: filtered, totalAvailableResources: totalAvailable };
  }, [searchTerm, selectedCategory, selectedType, selectedResourceType, selectedSkillLevel, selectedCost, sortBy, databaseResources]);

  if (loading) {
    return (
      <PageLayout maxWidth="6xl" padding="lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading resources...</p>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout maxWidth="6xl" padding="lg">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Career Development Resources</h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Comprehensive resources to support your career transition journey. From mindset and emotional support
          to technical skill development across high-demand fields like cybersecurity, data science, AI, and more.
        </p>
        {error && (
          <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-yellow-800 dark:text-yellow-200 text-sm">
              {error} - Showing static resources only.
            </p>
          </div>
        )}
      </div>

      {/* Resource Type Tabs */}
      <div className="mb-8 flex justify-center">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm">
          <div className="flex gap-1">
            <button
              onClick={() => setSelectedResourceType('all')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedResourceType === 'all'
                  ? 'bg-gray-800 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              All Resources
            </button>
            <button
              onClick={() => setSelectedResourceType('mindset')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedResourceType === 'mindset'
                  ? 'bg-gray-800 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              Mindset & Support
            </button>
            <button
              onClick={() => setSelectedResourceType('learning')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedResourceType === 'learning'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              Skill Development
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex flex-col gap-4">
          {/* Search Bar */}
          <div className="relative">
            <label htmlFor="resource-search" className="sr-only">
              Search resources by title, description, or author
            </label>
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" aria-hidden="true" />
            <input
              id="resource-search"
              type="text"
              placeholder="Search resources by title, description, or author..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              aria-label="Search resources"
            />
          </div>

          {/* Filters Row */}
          <div className="flex flex-wrap gap-3 items-center">
            <Filter className="text-gray-400 h-5 w-5" />

            {/* Category Filter */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>
                  {categoryInfo[category].title}
                </option>
              ))}
            </select>

            {/* Type Filter */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Types</option>
              {types.slice(1).map(type => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}s
                </option>
              ))}
            </select>

            {/* Skill Level Filter */}
            <select
              value={selectedSkillLevel}
              onChange={(e) => setSelectedSkillLevel(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Levels</option>
              {skillLevels.slice(1).map(level => (
                <option key={level} value={level}>
                  {level.charAt(0).toUpperCase() + level.slice(1)}
                </option>
              ))}
            </select>

            {/* Cost Filter */}
            <select
              value={selectedCost}
              onChange={(e) => setSelectedCost(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              <option value="all">All Costs</option>
              {costOptions.slice(1).map(cost => (
                <option key={cost} value={cost}>
                  {cost.charAt(0).toUpperCase() + cost.slice(1)}
                </option>
              ))}
            </select>

            {/* Sort Filter */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  Sort by {option.label}
                </option>
              ))}
            </select>

            {/* Clear Filters Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedType('all');
                setSelectedSkillLevel('all');
                setSelectedCost('all');
                setSortBy('relevance');
              }}
              className="text-sm"
            >
              Clear Filters
            </Button>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
          Showing {filteredResources.length} of {totalAvailableResources} resources
          {selectedResourceType !== 'all' && (
            <span className="ml-2 text-blue-600 dark:text-blue-400">
              ({selectedResourceType === 'mindset' ? 'Mindset & Support' : 'Skill Development'} only)
            </span>
          )}
        </div>
      </div>

      {/* Resources Display */}
      {filteredResources.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400 text-lg">
            No resources found matching your criteria. Try adjusting your search or filters.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredResources.map((resource) => {
            const ResourceIcon = getResourceIcon(resource.type);

            // Map database category names to categoryInfo keys
            const getCategoryKey = (category: string) => {
              const categoryMap: { [key: string]: string } = {
                'CYBERSECURITY': 'cybersecurity',
                'DATA_SCIENCE': 'data-science',
                'BLOCKCHAIN': 'blockchain',
                'PROJECT_MANAGEMENT': 'project-management',
                'DIGITAL_MARKETING': 'digital-marketing',
                'ARTIFICIAL_INTELLIGENCE': 'ai',
                'WEB_DEVELOPMENT': 'web-development',
                'MOBILE_DEVELOPMENT': 'mobile-development',
                'CLOUD_COMPUTING': 'cloud-computing',
                'FINANCIAL_LITERACY': 'financial',
                'LANGUAGE_LEARNING': 'language-learning',
                'ENTREPRENEURSHIP': 'entrepreneurship'
              };
              return categoryMap[category] || category.toLowerCase().replace(/_/g, '-');
            };

            const categoryKey = getCategoryKey(resource.category);
            const category = categoryInfo[categoryKey as keyof typeof categoryInfo];

            // Fallback category if not found
            const fallbackCategory = {
              title: resource.category.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
              icon: BookOpen,
              color: 'text-gray-600 dark:text-gray-400'
            };

            const categoryData = category || fallbackCategory;
            const CategoryIcon = categoryData.icon;

            return (
              <div
                key={resource.id}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-start gap-3 mb-3">
                  <ResourceIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <CategoryIcon className={`h-4 w-4 ${categoryData.color}`} />
                      <span className={`text-xs font-medium ${categoryData.color}`}>
                        {categoryData.title}
                      </span>
                    </div>
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                      {resource.title}
                    </h3>
                    {resource.author && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        by {resource.author} • {resource.duration}
                        {resource.skillLevel && (
                          <span className="ml-2 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                            {resource.skillLevel}
                          </span>
                        )}
                        {resource.cost && resource.cost !== 'free' && (
                          <span className="ml-2 px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded text-xs">
                            {resource.cost}
                          </span>
                        )}
                      </p>
                    )}
                  </div>
                </div>

                <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm leading-relaxed">
                  {resource.description}
                </p>

                {/* Rating Display */}
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-3 w-3 ${
                          star <= (resource.averageRating || 0)
                            ? 'text-yellow-500 fill-current'
                            : 'text-gray-300 dark:text-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {resource.averageRating && resource.totalRatings ? (
                      `${resource.averageRating.toFixed(1)} (${resource.totalRatings} ${resource.totalRatings === 1 ? 'review' : 'reviews'})`
                    ) : (
                      'No ratings yet'
                    )}
                  </span>
                </div>

                <div className="flex gap-2">
                  <Button
                    asChild
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Link
                      href={`/resources/${resource.id}`}
                      className="flex items-center justify-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Link>
                  </Button>

                  <Button
                    asChild
                    size="sm"
                    className="flex-1"
                  >
                    <a
                      href={resource.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center gap-2"
                    >
                      <span className="capitalize">
                        {resource.type === 'article' ? 'Read' :
                         resource.type === 'video' ? 'Watch' :
                         resource.type === 'podcast' ? 'Listen' :
                         resource.type === 'course' ? 'Take Course' :
                         resource.type === 'certification' ? 'Get Certified' :
                         resource.type === 'tutorial' ? 'Follow Tutorial' : 'View'}
                      </span>
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      )}

      <div className="mt-12 text-center bg-gray-50 dark:bg-gray-900/20 p-8 rounded-lg">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Ready to Start Your Learning Journey?
        </h3>
        <p className="text-gray-700 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
          Take our assessment to discover which career paths align with your goals, then explore the relevant
          learning resources to build the skills you need for your transition.
        </p>
        <div className="flex gap-4 justify-center flex-wrap">
          <Button asChild>
            <Link href="/assessment">Take Assessment</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/career-paths">Explore Career Paths</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/forum">Join Community</Link>
          </Button>
        </div>
      </div>
    </PageLayout>
  );
}
