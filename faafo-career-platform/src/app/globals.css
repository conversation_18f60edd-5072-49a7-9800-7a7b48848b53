@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: 0 0% 100%;
  --foreground: 0 0% 14.5%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 14.5%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 14.5%;
  --primary: 0 0% 20.5%;
  --primary-foreground: 0 0% 98.5%;
  --secondary: 0 0% 97%;
  --secondary-foreground: 0 0% 20.5%;
  --muted: 0 0% 97%;
  --muted-foreground: 0 0% 55.6%;
  --accent: 0 0% 97%;
  --accent-foreground: 0 0% 20.5%;
  --destructive: 27 24.5% 57.7%;
  --border: 0 0% 92.2%;
  --input: 0 0% 92.2%;
  --ring: 0 0% 70.8%;
  --chart-1: 41 22.2% 64.6%;
  --chart-2: 184 11.8% 60%;
  --chart-3: 227 7% 39.8%;
  --chart-4: 84 18.9% 82.8%;
  --chart-5: 70 18.8% 76.9%;
  --sidebar: 0 0% 98.5%;
  --sidebar-foreground: 0 0% 14.5%;
  --sidebar-primary: 0 0% 20.5%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 97%;
  --sidebar-accent-foreground: 0 0% 20.5%;
  --sidebar-border: 0 0% 92.2%;
  --sidebar-ring: 0 0% 70.8%;
}

.dark {
  --background: 0 0% 14.5%;
  --foreground: 0 0% 98.5%;
  --card: 0 0% 20.5%;
  --card-foreground: 0 0% 98.5%;
  --popover: 0 0% 20.5%;
  --popover-foreground: 0 0% 98.5%;
  --primary: 0 0% 92.2%;
  --primary-foreground: 0 0% 20.5%;
  --secondary: 0 0% 26.9%;
  --secondary-foreground: 0 0% 98.5%;
  --muted: 0 0% 26.9%;
  --muted-foreground: 0 0% 70.8%;
  --accent: 0 0% 26.9%;
  --accent-foreground: 0 0% 98.5%;
  --destructive: 22 19.1% 70.4%;
  --border: 0 0% 100% / 10%;
  --input: 0 0% 100% / 15%;
  --ring: 0 0% 55.6%;
  --chart-1: 264 24.3% 48.8%;
  --chart-2: 162 17% 69.6%;
  --chart-3: 70 18.8% 76.9%;
  --chart-4: 303 26.5% 62.7%;
  --chart-5: 16 24.6% 64.5%;
  --sidebar: 0 0% 20.5%;
  --sidebar-foreground: 0 0% 98.5%;
  --sidebar-primary: 0 0% 20.5%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 26.9%;
  --sidebar-accent-foreground: 0 0% 98.5%;
  --sidebar-border: 0 0% 100% / 10%;
  --sidebar-ring: 0 0% 55.6%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    /* background-color: hsl(var(--background)) !important; */
    /* color: hsl(var(--foreground)) !important; */
  }
  body {
    /* background-color: hsl(var(--background)); */
    /* color: hsl(var(--foreground)); */
    background-color: white;
    color: #1f2937;
  }

  /* Add a clear focus indicator */
  :focus-visible {
    outline: 3px solid hsl(41 22.2% 64.6%);
    outline-offset: 2px;
    border-color: transparent;
  }

  /* Force dropdown menus to appear above everything */
  [data-radix-popper-content-wrapper] {
    z-index: 9999 !important;
  }

  [role="menu"] {
    z-index: 9999 !important;
  }

  /* Ensure minimum touch target sizes for accessibility (44x44px minimum) */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  a[href],
  [tabindex]:not([tabindex="-1"]),
  select,
  textarea,
  input:not([type="hidden"]) {
    min-height: 44px;
    min-width: 44px;
  }

  /* Exception for inline links within text */
  p a,
  span a,
  li a:not(.button):not([role="button"]) {
    min-height: auto;
    min-width: auto;
    display: inline;
  }

  /* Ensure buttons have adequate padding */
  button:not(.no-min-size),
  [role="button"]:not(.no-min-size) {
    padding: 8px 16px;
  }

  /* Improve focus indicators for better accessibility */
  button:focus-visible,
  [role="button"]:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible,
  a:focus-visible {
    outline: 3px solid hsl(41 22.2% 64.6%);
    outline-offset: 2px;
    border-color: transparent;
  }

  /* Ensure adequate spacing between interactive elements */
  button + button,
  [role="button"] + [role="button"],
  a + a {
    margin-left: 8px;
  }

  /* Skip link styling */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 10000;
    border-radius: 4px;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Improve form accessibility */
  label {
    font-weight: 500;
    margin-bottom: 4px;
    display: block;
  }

  /* Error message styling */
  [role="alert"],
  .error-message {
    color: hsl(var(--destructive));
    font-weight: 500;
    margin-top: 4px;
  }

  /* Loading state accessibility */
  [aria-busy="true"] {
    cursor: wait;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :focus-visible {
      outline: 4px solid;
      outline-offset: 2px;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Enhanced Performance Optimizations */

  /* Optimize font loading */
  @font-face {
    font-display: swap;
  }

  /* Optimize image loading */
  img {
    loading: lazy;
    decoding: async;
  }

  /* Critical images should load immediately */
  img[data-priority="high"] {
    loading: eager;
  }

  /* Optimize animations for performance */
  @media (prefers-reduced-motion: no-preference) {
    .animate-fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }

    .animate-slide-up {
      animation: slideUp 0.4s ease-out;
    }

    .animate-scale-in {
      animation: scaleIn 0.2s ease-out;
    }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}
