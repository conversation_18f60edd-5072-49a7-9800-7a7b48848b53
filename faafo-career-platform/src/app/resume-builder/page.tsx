'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  Plus,
  FileText,
  Download,
  Edit3,
  Trash2,
  Eye,
  Star,
  Clock,
  Sparkles,
  Loader2,
  AlertCircle,
  Upload,
  X,
  Copy,
  CheckCircle
} from 'lucide-react';

interface Resume {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  location?: string;
  website?: string;
  linkedin?: string;
  summary?: string;
  templateId: string | null;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  template?: {
    name: string;
    category: string;
  } | null;
}

interface ApiResponse {
  success: boolean;
  data: {
    resumes: Resume[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
  error?: string;
}

export default function ResumeBuilderPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState<string | null>(null);
  const [downloading, setDownloading] = useState<string | null>(null);

  // New feature states
  const [showLinkedInImport, setShowLinkedInImport] = useState(false);
  const [showResumeUpload, setShowResumeUpload] = useState(false);
  const [linkedInText, setLinkedInText] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [uploading, setUploading] = useState(false);

  // Success/feedback states
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [importProgress, setImportProgress] = useState<string | null>(null);

  // Fetch resumes from API
  const fetchResumes = async () => {
    if (status === 'loading') return;
    if (!session) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/resume-builder', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data: ApiResponse = await response.json();

      if (response.ok && data.success) {
        setResumes(data.data.resumes);
      } else {
        setError(data.error || 'Failed to fetch resumes');
      }
    } catch (err) {
      console.error('Error fetching resumes:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchResumes();
  }, [session, status]);

  const handleDeleteResume = async (id: string) => {
    if (!confirm('Are you sure you want to delete this resume?')) {
      return;
    }

    try {
      setDeleting(id);

      const response = await fetch(`/api/resume-builder/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setResumes(resumes.filter(resume => resume.id !== id));
      } else {
        alert(data.error || 'Failed to delete resume');
      }
    } catch (err) {
      console.error('Error deleting resume:', err);
      alert('Network error. Please try again.');
    } finally {
      setDeleting(null);
    }
  };

  const handleDownloadResume = async (resume: Resume) => {
    try {
      setDownloading(resume.id);

      // Create a simple text-based resume for download
      const resumeContent = `
RESUME - ${resume.firstName} ${resume.lastName}

Contact Information:
Email: ${resume.email}
Phone: ${resume.phone || 'Not provided'}
Location: ${resume.location || 'Not provided'}
Website: ${resume.website || 'Not provided'}
LinkedIn: ${resume.linkedin || 'Not provided'}

Professional Summary:
${resume.summary || 'No summary provided'}

Template: ${resume.template?.name || 'Default'}
Last Updated: ${new Date(resume.updatedAt).toLocaleDateString()}
      `;

      const blob = new Blob([resumeContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${resume.firstName}_${resume.lastName}_Resume.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error downloading resume:', error);
      alert('Failed to download resume');
    } finally {
      setDownloading(null);
    }
  };

  // LinkedIn Import Handler with Enhanced Success Messages and Error Handling
  const handleLinkedInImport = async () => {
    if (!linkedInText.trim()) {
      setError('Please paste your LinkedIn profile data');
      return;
    }

    try {
      setImporting(true);
      setError(null);
      setSuccessMessage(null);
      setImportProgress('🔍 Parsing LinkedIn data...');

      // Call new parsing service for LinkedIn text
      const response = await fetch('/api/ai/resume-parsing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resumeText: linkedInText,
          action: 'parse_linkedin'
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const parsedData = result.data.parsedData;
        setImportProgress('✅ Data parsed! Creating resume...');

        // Use the bulk import API for better data persistence
        const importResponse = await fetch('/api/resume-builder/import', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            title: `LinkedIn Import - ${new Date().toLocaleDateString()}`,
            personalInfo: {
              firstName: parsedData.personalInfo?.firstName || '',
              lastName: parsedData.personalInfo?.lastName || '',
              email: parsedData.personalInfo?.email || '',
              phone: parsedData.personalInfo?.phone || '',
              location: parsedData.personalInfo?.location || '',
              website: parsedData.personalInfo?.website || '',
              linkedin: parsedData.personalInfo?.linkedin || '',
            },
            summary: parsedData.summary || '',
            experience: parsedData.experience || [],
            education: parsedData.education || [],
            skills: parsedData.skills || [],
            projects: parsedData.projects || [],
          }),
        });

        const importResult = await importResponse.json();

        if (importResponse.ok && importResult.success) {
          setImportProgress('🎉 Resume created successfully! Redirecting...');
          setSuccessMessage(importResult.message || 'LinkedIn data imported successfully!');

          // Refresh the resumes list
          await fetchResumes();

          // Close modal and reset states
          setShowLinkedInImport(false);
          setLinkedInText('');

          // RACE CONDITION FIX: Verify resume exists before redirecting
          if (importResult.data?.resume?.id) {
            try {
              // Verify the resume was actually created
              const verifyResponse = await fetch(`/api/resume-builder/${importResult.data.resume.id}`);
              const verifyData = await verifyResponse.json();

              if (verifyResponse.ok && verifyData.success) {
                // Resume verified, safe to redirect
                router.push(`/resume-builder/edit/${importResult.data.resume.id}`);
              } else {
                console.warn('Resume verification failed:', verifyData.error);
                setError('Resume created but verification failed. Please refresh the page.');
              }
            } catch (verifyError) {
              console.warn('Resume verification error:', verifyError);
              setError('Resume created but verification failed. Please refresh the page.');
            }
          } else {
            setError('Resume creation succeeded but no ID returned.');
          }
        } else {
          throw new Error(importResult.error || 'Failed to create resume');
        }
      } else {
        throw new Error(result.error || 'Failed to parse LinkedIn data');
      }
    } catch (error) {
      console.error('Error importing LinkedIn data:', error);
      setError(error instanceof Error ? error.message : 'Failed to import LinkedIn data. Please try again.');
      setImportProgress(null);
    } finally {
      setImporting(false);
    }
  };

  // Resume Upload Handler with Enhanced Success Messages and Error Handling
  const handleResumeUpload = async () => {
    if (!uploadedFile) {
      setError('Please select a file to upload');
      return;
    }

    try {
      setUploading(true);
      setError(null);
      setSuccessMessage(null);
      setImportProgress('📄 Processing uploaded file...');

      const formData = new FormData();
      formData.append('file', uploadedFile);

      const response = await fetch('/api/ai/resume-parsing', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        const parsedData = result.data.parsedData;
        setImportProgress('✅ File parsed! Creating resume...');

        // Use the bulk import API for better data persistence
        const importResponse = await fetch('/api/resume-builder/import', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            title: `Uploaded Resume - ${uploadedFile.name}`,
            personalInfo: {
              firstName: parsedData.personalInfo?.firstName || '',
              lastName: parsedData.personalInfo?.lastName || '',
              email: parsedData.personalInfo?.email || '',
              phone: parsedData.personalInfo?.phone || '',
              location: parsedData.personalInfo?.location || '',
              website: parsedData.personalInfo?.website || '',
              linkedin: parsedData.personalInfo?.linkedin || '',
            },
            summary: parsedData.summary || '',
            experience: parsedData.experience || [],
            education: parsedData.education || [],
            skills: parsedData.skills || [],
            projects: parsedData.projects || [],
          }),
        });

        const importResult = await importResponse.json();

        if (importResponse.ok && importResult.success) {
          setImportProgress('🎉 Resume created successfully! Redirecting...');
          setSuccessMessage(importResult.message || 'Resume uploaded and processed successfully!');

          // Refresh the resumes list
          await fetchResumes();

          // Close modal and reset states
          setShowResumeUpload(false);
          setUploadedFile(null);

          // RACE CONDITION FIX: Verify resume exists before redirecting
          if (importResult.data?.resume?.id) {
            try {
              // Verify the resume was actually created
              const verifyResponse = await fetch(`/api/resume-builder/${importResult.data.resume.id}`);
              const verifyData = await verifyResponse.json();

              if (verifyResponse.ok && verifyData.success) {
                // Resume verified, safe to redirect
                router.push(`/resume-builder/edit/${importResult.data.resume.id}`);
              } else {
                console.warn('Resume verification failed:', verifyData.error);
                setError('Resume created but verification failed. Please refresh the page.');
              }
            } catch (verifyError) {
              console.warn('Resume verification error:', verifyError);
              setError('Resume created but verification failed. Please refresh the page.');
            }
          } else {
            setError('Resume creation succeeded but no ID returned.');
          }
        } else {
          throw new Error(importResult.error || 'Failed to create resume');
        }
      } else {
        throw new Error(result.error || 'Failed to parse uploaded resume');
      }
    } catch (error) {
      console.error('Error uploading resume:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload resume. Please try again.');
      setImportProgress(null);
    } finally {
      setUploading(false);
    }
  };

  // Show loading state
  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading your resumes...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Error Loading Resumes</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Show login required state
  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <FileText className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Login Required</h2>
          <p className="text-gray-600 mb-4">Please log in to access your resumes.</p>
          <Link
            href="/auth/signin"
            className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700 transition-colors inline-block"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Enhanced Header */}
          <motion.div 
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-12 text-center"
          >
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl">
                <FileText className="h-8 w-8 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              Resume Builder
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Create stunning, ATS-optimized resumes that get you hired. 
              <span className="text-blue-600 font-semibold"> AI-powered</span> content suggestions included.
            </p>
          </motion.div>

          {/* Interactive Action Cards */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-12 grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="group"
            >
              <Link href="/resume-builder/create">
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer">
                  <div className="flex items-center justify-between mb-4">
                    <div className="p-3 bg-white/20 rounded-xl">
                      <Plus className="h-6 w-6" />
                    </div>
                    <Sparkles className="h-5 w-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Create New Resume</h3>
                  <p className="text-blue-100">Start from scratch with AI assistance</p>
                </div>
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="group cursor-pointer"
              onClick={() => setShowLinkedInImport(true)}
            >
              <div className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-white/20 rounded-xl">
                    <FileText className="h-6 w-6" />
                  </div>
                  <Eye className="h-5 w-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                </div>
                <h3 className="text-xl font-bold mb-2">Import from LinkedIn</h3>
                <p className="text-purple-100">Paste your profile data to auto-fill</p>
              </div>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.02, y: -5 }}
              whileTap={{ scale: 0.98 }}
              className="group cursor-pointer"
              onClick={() => setShowResumeUpload(true)}
            >
              <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-white/20 rounded-xl">
                    <Upload className="h-6 w-6" />
                  </div>
                  <Plus className="h-5 w-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                </div>
                <h3 className="text-xl font-bold mb-2">Upload Existing</h3>
                <p className="text-green-100">Upload PDF/DOCX to enhance</p>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Resume List */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">
                Your Resumes ({resumes.length})
              </h2>
            </div>
            
            {resumes.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No resumes yet</h3>
                <p className="text-gray-600 mb-6">Create your first resume to get started</p>
                <Link
                  href="/resume-builder/create"
                  className="bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
                >
                  <Plus className="h-5 w-5" />
                  Create Your First Resume
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {resumes.map((resume, index) => {
                  const lastModified = new Date(resume.updatedAt).toLocaleDateString();
                  const isPublic = resume.isPublic;

                  return (
                    <motion.div
                      key={resume.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      whileHover={{ y: -5, scale: 1.02 }}
                      className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="text-lg font-bold text-gray-900 truncate">
                                {resume.title}
                              </h3>
                              {isPublic && (
                                <div className="flex items-center gap-1">
                                  <Eye className="h-4 w-4 text-green-600" />
                                  <span className="text-xs text-green-600">Public</span>
                                </div>
                              )}
                            </div>
                            <p className="text-sm text-gray-500 mb-3">
                              {resume.firstName} {resume.lastName}
                            </p>

                            {/* Template Info */}
                            {resume.template && (
                              <div className="mb-4">
                                <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                  {resume.template.name} Template
                                </span>
                              </div>
                            )}

                            {/* Last Modified */}
                            <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>Modified {lastModified}</span>
                              </div>
                            </div>

                            {/* Status Badge */}
                            <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full ${
                              isPublic
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {isPublic ? 'Published' : 'Draft'}
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <Link
                            href={`/resume-builder/edit/${resume.id}`}
                            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors text-sm font-medium text-center flex items-center justify-center gap-2"
                          >
                            <Edit3 className="h-4 w-4" />
                            Edit
                          </Link>
                          <button
                            onClick={() => handleDownloadResume(resume)}
                            disabled={downloading === resume.id}
                            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-200 transition-colors text-sm disabled:opacity-50"
                          >
                            {downloading === resume.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Download className="h-4 w-4" />
                            )}
                          </button>
                          <button
                            onClick={() => handleDeleteResume(resume.id)}
                            disabled={deleting === resume.id}
                            className="bg-red-100 text-red-700 px-4 py-2 rounded-xl hover:bg-red-200 transition-colors text-sm disabled:opacity-50"
                          >
                            {deleting === resume.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </motion.div>
        </div>
      </div>

      {/* LinkedIn Import Modal */}
      {showLinkedInImport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-xl">
                    <FileText className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">Import from LinkedIn</h2>
                    <p className="text-gray-600">Paste your LinkedIn profile data to auto-fill your resume</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowLinkedInImport(false);
                    setError(null);
                    setSuccessMessage(null);
                    setImportProgress(null);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    LinkedIn Profile Data
                  </label>
                  <textarea
                    value={linkedInText}
                    onChange={(e) => setLinkedInText(e.target.value)}
                    placeholder="Copy and paste your LinkedIn profile sections here (About, Experience, Education, Skills, etc.)"
                    className="w-full h-64 p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                  />
                </div>

                {/* Progress and Success Messages */}
                {importProgress && (
                  <div className="bg-purple-50 p-4 rounded-xl border border-purple-200">
                    <div className="flex items-center gap-3">
                      <Loader2 className="h-5 w-5 text-purple-600 animate-spin" />
                      <p className="text-purple-800 font-medium">{importProgress}</p>
                    </div>
                  </div>
                )}

                {successMessage && (
                  <div className="bg-green-50 p-4 rounded-xl border border-green-200">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <p className="text-green-800 font-medium">{successMessage}</p>
                    </div>
                  </div>
                )}

                {error && (
                  <div className="bg-red-50 p-4 rounded-xl border border-red-200">
                    <div className="flex items-center gap-3">
                      <X className="h-5 w-5 text-red-600" />
                      <p className="text-red-800 font-medium">{error}</p>
                    </div>
                  </div>
                )}

                <div className="bg-blue-50 p-4 rounded-xl">
                  <div className="flex items-start gap-3">
                    <Copy className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-blue-900 mb-1">How to get your LinkedIn data:</h3>
                      <ol className="text-sm text-blue-800 space-y-1">
                        <li>1. Go to your LinkedIn profile</li>
                        <li>2. Copy your About section, Experience, Education, and Skills</li>
                        <li>3. Paste all the text in the box above</li>
                        <li>4. Our AI will automatically parse and organize the data</li>
                      </ol>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => {
                      setShowLinkedInImport(false);
                      setError(null);
                      setSuccessMessage(null);
                      setImportProgress(null);
                    }}
                    className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleLinkedInImport}
                    disabled={importing || !linkedInText.trim()}
                    className="flex-1 px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {importing ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Importing...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4" />
                        Import & Create Resume
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Resume Upload Modal */}
      {showResumeUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-xl">
                    <Upload className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">Upload Existing Resume</h2>
                    <p className="text-gray-600">Upload your current resume to enhance and optimize it</p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setShowResumeUpload(false);
                    setError(null);
                    setSuccessMessage(null);
                    setImportProgress(null);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Resume File
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-green-400 transition-colors">
                    <input
                      type="file"
                      accept=".txt"
                      onChange={(e) => setUploadedFile(e.target.files?.[0] || null)}
                      className="hidden"
                      id="resume-upload"
                    />
                    <label htmlFor="resume-upload" className="cursor-pointer">
                      <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-lg font-medium text-gray-900 mb-2">
                        {uploadedFile ? uploadedFile.name : 'Choose a file or drag and drop'}
                      </p>
                      <p className="text-sm text-gray-500">
                        Currently supports TXT files. For PDF/DOCX, please copy text and use LinkedIn Import.
                      </p>
                    </label>
                  </div>
                </div>

                {/* Progress and Success Messages for Upload */}
                {importProgress && (
                  <div className="bg-green-50 p-4 rounded-xl border border-green-200">
                    <div className="flex items-center gap-3">
                      <Loader2 className="h-5 w-5 text-green-600 animate-spin" />
                      <p className="text-green-800 font-medium">{importProgress}</p>
                    </div>
                  </div>
                )}

                {successMessage && (
                  <div className="bg-green-50 p-4 rounded-xl border border-green-200">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <p className="text-green-800 font-medium">{successMessage}</p>
                    </div>
                  </div>
                )}

                {error && (
                  <div className="bg-red-50 p-4 rounded-xl border border-red-200">
                    <div className="flex items-center gap-3">
                      <X className="h-5 w-5 text-red-600" />
                      <p className="text-red-800 font-medium">{error}</p>
                    </div>
                  </div>
                )}

                <div className="bg-green-50 p-4 rounded-xl">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-green-900 mb-1">What happens next:</h3>
                      <ul className="text-sm text-green-800 space-y-1">
                        <li>• AI will extract and parse your resume content</li>
                        <li>• Data will be organized into structured sections</li>
                        <li>• You can edit and enhance the imported information</li>
                        <li>• Choose from professional templates to improve formatting</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => {
                      setShowResumeUpload(false);
                      setError(null);
                      setSuccessMessage(null);
                      setImportProgress(null);
                    }}
                    className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleResumeUpload}
                    disabled={uploading || !uploadedFile}
                    className="flex-1 px-6 py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {uploading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Upload & Create Resume
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
}
