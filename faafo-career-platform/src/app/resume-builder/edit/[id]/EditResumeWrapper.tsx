'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Loader2,
  AlertCircle,
  Save,
  Eye,
  Download,
  User,
  Mail,
  Phone,
  MapPin,
  Globe,
  Linkedin,
  FileText,
  Briefcase,
  GraduationCap,
  Award,
  Code,
  Plus,
  Edit3,
  Trash2,
  CheckCircle,
  X,
  Sparkles,
  Zap
} from 'lucide-react';
import Link from 'next/link';

interface Resume {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  website?: string;
  linkedin?: string;
  summary?: string;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  template?: {
    id: string;
    name: string;
    category: string;
  };
  experiences?: Experience[];
  educations?: Education[];
  skills?: Skill[];
  projects?: Project[];
}

interface Experience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  description?: string;
  achievements?: string[];
  sortOrder: number;
}

interface Education {
  id: string;
  institution: string;
  degree: string;
  fieldOfStudy?: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  gpa?: string;
  achievements?: string[];
  sortOrder: number;
}

interface Skill {
  id: string;
  skillName: string;
  category: string;
  proficiency?: number;
  sortOrder: number;
}

interface Project {
  id: string;
  name: string;
  description?: string;
  technologies?: string[];
  projectUrl?: string;
  repositoryUrl?: string;
  startDate?: string;
  endDate?: string;
  isHighlighted: boolean;
  sortOrder: number;
}

interface EditResumeWrapperProps {
  resumeId: string;
}

export default function EditResumeWrapper({ resumeId }: EditResumeWrapperProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [resume, setResume] = useState<Resume | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('personal');
  const [success, setSuccess] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [optimizing, setOptimizing] = useState(false);
  const [showOptimizationResults, setShowOptimizationResults] = useState(false);
  const [optimizationResults, setOptimizationResults] = useState<any>(null);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
      return;
    }

    if (status === 'authenticated') {
      fetchResume();
    }
  }, [status, resumeId]);

  const fetchResume = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/resume-builder/${resumeId}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Resume not found');
        }
        throw new Error('Failed to load resume');
      }

      const result = await response.json();

      if (result.success) {
        setResume(result.data);
      } else {
        throw new Error(result.error || 'Failed to load resume');
      }
    } catch (error) {
      console.error('Error fetching resume:', error);
      setError(error instanceof Error ? error.message : 'Failed to load resume');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!resume) return;

    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/resume-builder', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: resume.id,
          title: resume.title,
          firstName: resume.firstName,
          lastName: resume.lastName,
          email: resume.email,
          phone: resume.phone,
          location: resume.location,
          website: resume.website,
          linkedin: resume.linkedin,
          summary: resume.summary,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        throw new Error(result.error || 'Failed to save resume');
      }
    } catch (error) {
      console.error('Error saving resume:', error);
      setError(error instanceof Error ? error.message : 'Failed to save resume');
    } finally {
      setSaving(false);
    }
  };

  const updateResumeField = (field: keyof Resume, value: any) => {
    if (!resume) return;
    setResume({ ...resume, [field]: value });
  };

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handleDownload = async () => {
    if (!resume) return;

    try {
      setDownloading(true);

      // Create a simple text-based resume for download
      const resumeContent = `
RESUME - ${resume.firstName} ${resume.lastName}

Contact Information:
Email: ${resume.email}
Phone: ${resume.phone || 'Not provided'}
Location: ${resume.location || 'Not provided'}
Website: ${resume.website || 'Not provided'}
LinkedIn: ${resume.linkedin || 'Not provided'}

Professional Summary:
${resume.summary || 'No summary provided'}

Work Experience:
${resume.experiences?.map(exp => `
• ${exp.position} at ${exp.company}
  ${new Date(exp.startDate).toLocaleDateString()} - ${exp.isCurrent ? 'Present' : (exp.endDate ? new Date(exp.endDate).toLocaleDateString() : 'Present')}
  ${exp.description || ''}
  ${exp.achievements?.map(achievement => `  - ${achievement}`).join('\n') || ''}
`).join('\n') || 'No experience added'}

Education:
${resume.educations?.map(edu => `
• ${edu.degree} in ${edu.fieldOfStudy} - ${edu.institution}
  ${new Date(edu.startDate).toLocaleDateString()} - ${edu.isCurrent ? 'Present' : (edu.endDate ? new Date(edu.endDate).toLocaleDateString() : 'Present')}
  ${edu.gpa ? `GPA: ${edu.gpa}` : ''}
`).join('\n') || 'No education added'}

Skills:
${resume.skills?.map(skill => `• ${skill.skillName} (${skill.category})`).join('\n') || 'No skills added'}

Projects:
${resume.projects?.map(project => `
• ${project.name}${project.isHighlighted ? ' ⭐' : ''}
  ${project.description || ''}
  Technologies: ${project.technologies?.join(', ') || 'None specified'}
  ${project.projectUrl ? `URL: ${project.projectUrl}` : ''}
  ${project.repositoryUrl ? `Repository: ${project.repositoryUrl}` : ''}
`).join('\n') || 'No projects added'}
      `;

      const blob = new Blob([resumeContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${resume.firstName}_${resume.lastName}_Resume.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

    } catch (err) {
      console.error('Error downloading resume:', err);
      setError('Failed to download resume');
    } finally {
      setDownloading(false);
    }
  };

  const tabs = [
    { id: 'personal', name: 'Personal Info', icon: User },
    { id: 'summary', name: 'Summary', icon: FileText },
    { id: 'experience', name: 'Experience', icon: Briefcase },
    { id: 'education', name: 'Education', icon: GraduationCap },
    { id: 'skills', name: 'Skills', icon: Code },
    { id: 'projects', name: 'Projects', icon: Award },
  ];

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading resume...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md mx-4"
        >
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Error Loading Resume
          </h3>
          <p className="text-gray-600 text-center mb-4">
            {error}
          </p>
          <div className="flex gap-2 justify-center">
            <Link
              href="/resume-builder"
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl hover:bg-gray-200 transition-colors"
            >
              Back to Resumes
            </Link>
            <button
              onClick={fetchResume}
              className="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Simplified content for now */}
      <div className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Edit Resume: {resume?.title}
            </h1>
            <p className="text-gray-600">
              Resume ID: {resumeId}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
