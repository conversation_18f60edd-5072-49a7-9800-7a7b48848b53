import { NextRequest, NextResponse } from 'next/server';
import { SecurityValidator } from '@/lib/validation';
import { trackError } from '@/lib/errorTracking';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate and sanitize the error data
    const validation = SecurityValidator.validateSecurity(JSON.stringify(body));
    if (!validation.isValid) {
      return NextResponse.json(
        { error: 'Invalid error data' },
        { status: 400 }
      );
    }

    // Sanitize error data
    const sanitizedError = {
      type: SecurityValidator.sanitizeInput(body.type || 'unknown', { maxLength: 100 }),
      message: SecurityValidator.sanitizeInput(body.error?.message || 'Unknown error', { maxLength: 500 }),
      stack: SecurityValidator.sanitizeInput(body.error?.stack || '', { maxLength: 2000 }),
      url: SecurityValidator.sanitizeInput(body.url || '', { maxLength: 500 }),
      userAgent: SecurityValidator.sanitizeInput(body.userAgent || '', { maxLength: 500 }),
      timestamp: body.timestamp || new Date().toISOString(),
      severity: body.severity || 'low',
      errorId: body.errorId || crypto.randomUUID(),
      sessionId: SecurityValidator.sanitizeInput(body.sessionId || '', { maxLength: 100 })
    };

    // Log the error (in a real app, you'd save to database)
    console.error('Client Error Logged:', sanitizedError);

    // Track the error using our error tracking system
    trackError.ui(
      new Error(sanitizedError.message),
      'client-side',
      sanitizedError.type
    );

    return NextResponse.json({ success: true, errorId: sanitizedError.errorId });
  } catch (error) {
    console.error('Error logging endpoint failed:', error);
    return NextResponse.json(
      { error: 'Failed to log error' },
      { status: 500 }
    );
  }
}

// Only allow POST requests
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
