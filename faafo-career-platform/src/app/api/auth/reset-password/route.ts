import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import bcrypt from 'bcrypt';
import { withCSRFProtection, CSRF_CONFIGS } from '@/lib/csrf-middleware';

export const POST = withCSRFProtection(async (request: NextRequest) => {
  try {
    const { token, password } = await request.json();

    if (!token || !password) {
      return NextResponse.json({ error: 'Token and new password are required.' }, { status: 400 });
    }

    const user = await prisma.user.findFirst({
      where: {
        passwordResetExpires: { gt: new Date() }, // Token must not be expired
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'Invalid or expired password reset token.' }, { status: 400 });
    }

    // Verify the hashed token
    const isTokenValid = await bcrypt.compare(token, user.passwordResetToken || '');

    if (!isTokenValid) {
      return NextResponse.json({ error: 'Invalid or expired password reset token.' }, { status: 400 });
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
      },
    });

    return NextResponse.json({ message: 'Your password has been reset successfully.' }, { status: 200 });
  } catch (error) {
    console.error('Password reset API error:', error);
    return NextResponse.json({ error: 'An error occurred while resetting your password.' }, { status: 500 });
  }
}, CSRF_CONFIGS.HIGH_SECURITY);