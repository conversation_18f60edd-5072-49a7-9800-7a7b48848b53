import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ isAdmin: false });
    }

    // Check if user has moderator privileges (which we'll treat as admin for now)
    const moderatorRole = await prisma.forumModerator.findFirst({
      where: {
        userId: session.user.id,
        isActive: true
      },
      select: { role: true }
    });

    // For now, treat any moderator as admin
    // In the future, you can add a proper role field to the User model
    const isAdmin = !!moderatorRole;

    return NextResponse.json({
      isAdmin,
      role: moderatorRole?.role || 'USER'
    });
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json({ isAdmin: false }, { status: 500 });
  }
}
