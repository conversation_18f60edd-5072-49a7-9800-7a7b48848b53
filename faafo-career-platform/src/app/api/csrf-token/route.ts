import { NextRequest, NextResponse } from 'next/server';
import { SimpleSecurity } from '@/lib/simple-security';

export async function GET(request: NextRequest) {
  try {
    // Get session ID from headers if available
    const sessionId = request.headers.get('x-session-id') || undefined;

    // Generate CSRF token with session binding
    const csrfToken = SimpleSecurity.generateCSRFToken(sessionId);

    // Store the token for validation
    await SimpleSecurity.storeCSRFToken(csrfToken, sessionId);

    const response = NextResponse.json(
      {
        csrfToken,
        timestamp: Date.now(),
        expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
      },
      {
        status: 200,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Pragma': 'no-cache'
        }
      }
    );

    return SimpleSecurity.addSecurityHeaders(response);
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}
