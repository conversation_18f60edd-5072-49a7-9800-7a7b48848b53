import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { z } from 'zod';

// Focused validation schema - only essential fields
const createResumeSchema = z.object({
  title: z.string()
    .min(1, 'Title is required')
    .max(100, 'Title must be less than 100 characters')
    .trim(),
  templateId: z.string().uuid().optional(),
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name too long')
    .trim(),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name too long')
    .trim(),
  email: z.string()
    .email('Invalid email address')
    .max(255, 'Email too long'),
  phone: z.string()
    .max(20, 'Phone number too long')
    .optional()
    .transform(val => val?.trim() || null),
  location: z.string()
    .max(100, 'Location too long')
    .optional()
    .transform(val => val?.trim() || null),
  website: z.string()
    .url('Invalid website URL')
    .max(255, 'Website URL too long')
    .optional()
    .or(z.literal(''))
    .transform(val => val === '' ? null : val),
  linkedin: z.string()
    .url('Invalid LinkedIn URL')
    .max(255, 'LinkedIn URL too long')
    .optional()
    .or(z.literal(''))
    .transform(val => val === '' ? null : val),
  summary: z.string()
    .max(1000, 'Summary too long')
    .optional()
    .transform(val => val?.trim() || null),
});

const updateResumeSchema = createResumeSchema.partial().extend({
  id: z.string().uuid('Invalid resume ID'),
});

// Standardized response types
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

// Helper function for error responses
function errorResponse(
  error: string, 
  status: number = 500, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

// Helper function for success responses
function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// GET - Fetch user's resumes with proper pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const { searchParams } = new URL(request.url);
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const limit = Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '10')));
    const skip = (page - 1) * limit;

    const [resumes, total] = await Promise.all([
      prisma.resume.findMany({
        where: {
          userId: session.user.id,
          isActive: true,
        },
        select: {
          id: true,
          title: true,
          templateId: true,
          firstName: true,
          lastName: true,
          isPublic: true,
          lastOptimized: true,
          atsScore: true,
          createdAt: true,
          updatedAt: true,
          template: {
            select: {
              name: true,
              category: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.resume.count({
        where: {
          userId: session.user.id,
          isActive: true,
        },
      }),
    ]);

    return successResponse({
      resumes,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Error fetching resumes:', error);
    return errorResponse('Failed to fetch resumes', 500, 'FETCH_ERROR');
  }
}

// POST - Create new resume with transaction safety
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const body = await request.json();
    const validation = createResumeSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const data = validation.data;

    // Verify template exists if provided
    if (data.templateId) {
      const template = await prisma.resumeTemplate.findFirst({
        where: {
          id: data.templateId,
          isActive: true,
        },
      });

      if (!template) {
        return errorResponse('Invalid template ID', 400, 'INVALID_TEMPLATE');
      }
    }

    // Create resume with transaction
    const resume = await prisma.resume.create({
      data: {
        userId: session.user.id,
        title: data.title,
        templateId: data.templateId,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        location: data.location,
        website: data.website,
        linkedin: data.linkedin,
        summary: data.summary,
      },
      select: {
        id: true,
        title: true,
        templateId: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        location: true,
        website: true,
        linkedin: true,
        summary: true,
        isPublic: true,
        createdAt: true,
        updatedAt: true,
        template: {
          select: {
            name: true,
            category: true,
          },
        },
      },
    });

    return successResponse(resume, 'Resume created successfully', 201);

  } catch (error) {
    console.error('Error creating resume:', error);
    
    // Handle specific Prisma errors
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return errorResponse('A resume with this title already exists', 409, 'DUPLICATE_TITLE');
    }
    
    return errorResponse('Failed to create resume', 500, 'CREATE_ERROR');
  }
}

// PUT - Update existing resume
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const body = await request.json();
    const validation = updateResumeSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const { id, ...updateData } = validation.data;

    // Verify ownership and existence
    const existingResume = await prisma.resume.findFirst({
      where: {
        id,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!existingResume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Update resume
    const updatedResume = await prisma.resume.update({
      where: { id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        title: true,
        templateId: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        location: true,
        website: true,
        linkedin: true,
        summary: true,
        isPublic: true,
        createdAt: true,
        updatedAt: true,
        template: {
          select: {
            name: true,
            category: true,
          },
        },
      },
    });

    return successResponse(updatedResume, 'Resume updated successfully');

  } catch (error) {
    console.error('Error updating resume:', error);
    return errorResponse('Failed to update resume', 500, 'UPDATE_ERROR');
  }
}
