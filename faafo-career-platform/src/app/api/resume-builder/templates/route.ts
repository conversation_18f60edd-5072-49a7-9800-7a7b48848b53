import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Standardized response types
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

function errorResponse(
  error: string, 
  status: number = 400, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// GET - Fetch all available resume templates
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    // Fetch all active templates
    const templates = await prisma.resumeTemplate.findMany({
      where: {
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        category: true,
        isPremium: true,
        cssStyles: true,
        createdAt: true,
      },
      orderBy: [
        { isPremium: 'asc' }, // Free templates first
        { category: 'asc' },
        { name: 'asc' },
      ],
    });

    // Group templates by category for better organization
    const templatesByCategory = templates.reduce((acc, template) => {
      const category = template.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(template);
      return acc;
    }, {} as Record<string, typeof templates>);

    return successResponse({
      templates,
      templatesByCategory,
      total: templates.length,
    });

  } catch (error) {
    console.error('Error fetching templates:', error);
    return errorResponse('Failed to fetch templates', 500, 'FETCH_ERROR');
  }
}
