import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
const idSchema = z.string().uuid('Invalid ID format');

const createProjectSchema = z.object({
  name: z.string()
    .min(1, 'Project name is required')
    .max(100, 'Project name too long')
    .trim(),
  description: z.string()
    .max(1000, 'Description too long')
    .optional()
    .transform(val => val?.trim() || null),
  technologies: z.array(z.string()).default([]),
  projectUrl: z.string()
    .url('Invalid project URL')
    .max(255, 'Project URL too long')
    .optional()
    .or(z.literal(''))
    .transform(val => val === '' ? null : val),
  repositoryUrl: z.string()
    .url('Invalid repository URL')
    .max(255, 'Repository URL too long')
    .optional()
    .or(z.literal(''))
    .transform(val => val === '' ? null : val),
  startDate: z.string()
    .optional()
    .transform(val => val ? new Date(val) : null),
  endDate: z.string()
    .optional()
    .transform(val => val ? new Date(val) : null),
  isHighlighted: z.boolean().default(false),
  sortOrder: z.number().int().min(0).default(0),
});

const updateProjectSchema = createProjectSchema.partial().extend({
  id: z.string().uuid('Invalid project ID'),
});

// Route params type
interface RouteParams {
  params: Promise<{ id: string }>;
}

// Standardized response types
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

function errorResponse(
  error: string, 
  status: number = 400, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// GET - Fetch all projects for a resume
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Fetch projects
    const projects = await prisma.resumeProject.findMany({
      where: {
        resumeId,
      },
      orderBy: [
        { isHighlighted: 'desc' }, // Highlighted projects first
        { sortOrder: 'asc' },
      ],
    });

    return successResponse({
      projects,
      total: projects.length,
      highlighted: projects.filter(p => p.isHighlighted).length,
    });

  } catch (error) {
    console.error('Error fetching projects:', error);
    return errorResponse('Failed to fetch projects', 500, 'FETCH_ERROR');
  }
}

// POST - Create new project
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    const body = await request.json();
    const validation = createProjectSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const data = validation.data;

    // Create project
    const project = await prisma.resumeProject.create({
      data: {
        resumeId,
        name: data.name,
        description: data.description,
        technologies: data.technologies,
        projectUrl: data.projectUrl,
        repositoryUrl: data.repositoryUrl,
        startDate: data.startDate,
        endDate: data.endDate,
        isHighlighted: data.isHighlighted,
        sortOrder: data.sortOrder,
      },
    });

    return successResponse(project, 'Project created successfully', 201);

  } catch (error) {
    console.error('Error creating project:', error);
    return errorResponse('Failed to create project', 500, 'CREATE_ERROR');
  }
}

// PUT - Update project
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    const body = await request.json();
    const validation = updateProjectSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const { id: projectId, ...updateData } = validation.data;

    // Verify ownership
    const project = await prisma.resumeProject.findFirst({
      where: {
        id: projectId,
        resumeId,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!project) {
      return errorResponse('Project not found', 404, 'NOT_FOUND');
    }

    // Update project
    const updatedProject = await prisma.resumeProject.update({
      where: { id: projectId },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    });

    return successResponse(updatedProject, 'Project updated successfully');

  } catch (error) {
    console.error('Error updating project:', error);
    return errorResponse('Failed to update project', 500, 'UPDATE_ERROR');
  }
}

// DELETE - Delete project
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return errorResponse('Project ID is required', 400, 'MISSING_ID');
    }

    const projectIdValidation = idSchema.safeParse(projectId);
    if (!projectIdValidation.success) {
      return errorResponse('Invalid project ID format', 400, 'INVALID_PROJECT_ID');
    }

    // Verify ownership
    const project = await prisma.resumeProject.findFirst({
      where: {
        id: projectId,
        resumeId,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!project) {
      return errorResponse('Project not found', 404, 'NOT_FOUND');
    }

    // Delete project
    await prisma.resumeProject.delete({
      where: { id: projectId },
    });

    return successResponse(
      { id: projectId },
      'Project deleted successfully'
    );

  } catch (error) {
    console.error('Error deleting project:', error);
    return errorResponse('Failed to delete project', 500, 'DELETE_ERROR');
  }
}
