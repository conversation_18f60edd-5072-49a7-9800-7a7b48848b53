import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { z } from 'zod';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// Validation schema for ID parameter
const idSchema = z.string().uuid('Invalid resume ID');

// Standardized response types
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

// Helper functions
function errorResponse(
  error: string, 
  status: number = 500, 
  code?: string
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// GET - Fetch specific resume with all related data
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    // Await params for Next.js 15 compatibility
    const resolvedParams = await params;

    // Validate ID parameter
    const idValidation = idSchema.safeParse(resolvedParams.id);
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Fetch resume with all related data
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            category: true,
            cssStyles: true,
          },
        },
        experiences: {
          orderBy: {
            sortOrder: 'asc',
          },
        },
        educations: {
          orderBy: {
            sortOrder: 'asc',
          },
        },
        skills: {
          orderBy: [
            { category: 'asc' },
            { sortOrder: 'asc' },
          ],
        },
        projects: {
          orderBy: {
            sortOrder: 'asc',
          },
        },
        optimizations: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1, // Get only the latest optimization
        },
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    return successResponse(resume);

  } catch (error) {
    console.error('Error fetching resume:', error);
    return errorResponse('Failed to fetch resume', 500, 'FETCH_ERROR');
  }
}

// DELETE - Soft delete resume
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    // Await params for Next.js 15 compatibility
    const resolvedParams = await params;

    // Validate ID parameter
    const idValidation = idSchema.safeParse(resolvedParams.id);
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Verify ownership and existence
    const existingResume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!existingResume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Soft delete by setting isActive to false
    await prisma.resume.update({
      where: { id: resumeId },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    });

    return successResponse(
      { id: resumeId },
      'Resume deleted successfully'
    );

  } catch (error) {
    console.error('Error deleting resume:', error);
    return errorResponse('Failed to delete resume', 500, 'DELETE_ERROR');
  }
}
