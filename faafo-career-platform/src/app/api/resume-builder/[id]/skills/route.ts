import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schemas
const idSchema = z.string().uuid('Invalid ID format');

const createSkillSchema = z.object({
  category: z.string()
    .min(1, 'Category is required')
    .max(50, 'Category name too long')
    .trim(),
  skillName: z.string()
    .min(1, 'Skill name is required')
    .max(50, 'Skill name too long')
    .trim(),
  proficiency: z.number()
    .int()
    .min(1, 'Proficiency must be at least 1')
    .max(5, 'Proficiency must be at most 5')
    .optional(),
  sortOrder: z.number().int().min(0).default(0),
});

const updateSkillSchema = createSkillSchema.partial().extend({
  id: z.string().uuid('Invalid skill ID'),
});

// Route params type
interface RouteParams {
  params: Promise<{ id: string }>;
}

// Standardized response types
interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

function errorResponse(
  error: string, 
  status: number = 400, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// GET - Fetch all skills for a resume
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Fetch skills grouped by category
    const skills = await prisma.resumeSkill.findMany({
      where: {
        resumeId,
      },
      orderBy: [
        { category: 'asc' },
        { sortOrder: 'asc' },
      ],
    });

    // Group skills by category
    const skillsByCategory = skills.reduce((acc, skill) => {
      const category = skill.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(skill);
      return acc;
    }, {} as Record<string, typeof skills>);

    return successResponse({
      skills,
      skillsByCategory,
      total: skills.length,
    });

  } catch (error) {
    console.error('Error fetching skills:', error);
    return errorResponse('Failed to fetch skills', 500, 'FETCH_ERROR');
  }
}

// POST - Create new skill
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    const body = await request.json();
    const validation = createSkillSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const data = validation.data;

    // Check for duplicate skill in same category
    const existingSkill = await prisma.resumeSkill.findFirst({
      where: {
        resumeId,
        category: data.category,
        skillName: data.skillName,
      },
    });

    if (existingSkill) {
      return errorResponse(
        'Skill already exists in this category',
        400,
        'DUPLICATE_SKILL'
      );
    }

    // Create skill
    const skill = await prisma.resumeSkill.create({
      data: {
        resumeId,
        category: data.category,
        skillName: data.skillName,
        proficiency: data.proficiency,
        sortOrder: data.sortOrder,
      },
    });

    return successResponse(skill, 'Skill created successfully', 201);

  } catch (error) {
    console.error('Error creating skill:', error);
    return errorResponse('Failed to create skill', 500, 'CREATE_ERROR');
  }
}

// PUT - Update skill
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    const body = await request.json();
    const validation = updateSkillSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const { id: skillId, ...updateData } = validation.data;

    // Verify ownership
    const skill = await prisma.resumeSkill.findFirst({
      where: {
        id: skillId,
        resumeId,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!skill) {
      return errorResponse('Skill not found', 404, 'NOT_FOUND');
    }

    // Check for duplicate if category or skillName is being updated
    if (updateData.category || updateData.skillName) {
      const newCategory = updateData.category || skill.category;
      const newSkillName = updateData.skillName || skill.skillName;
      
      const existingSkill = await prisma.resumeSkill.findFirst({
        where: {
          resumeId,
          category: newCategory,
          skillName: newSkillName,
          id: { not: skillId }, // Exclude current skill
        },
      });

      if (existingSkill) {
        return errorResponse(
          'Skill already exists in this category',
          400,
          'DUPLICATE_SKILL'
        );
      }
    }

    // Update skill
    const updatedSkill = await prisma.resumeSkill.update({
      where: { id: skillId },
      data: updateData,
    });

    return successResponse(updatedSkill, 'Skill updated successfully');

  } catch (error) {
    console.error('Error updating skill:', error);
    return errorResponse('Failed to update skill', 500, 'UPDATE_ERROR');
  }
}

// DELETE - Delete skill
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const resolvedParams = await params;
    const idValidation = idSchema.safeParse(resolvedParams.id);
    
    if (!idValidation.success) {
      return errorResponse('Invalid resume ID format', 400, 'INVALID_ID');
    }

    const resumeId = idValidation.data;

    const { searchParams } = new URL(request.url);
    const skillId = searchParams.get('skillId');

    if (!skillId) {
      return errorResponse('Skill ID is required', 400, 'MISSING_ID');
    }

    const skillIdValidation = idSchema.safeParse(skillId);
    if (!skillIdValidation.success) {
      return errorResponse('Invalid skill ID format', 400, 'INVALID_SKILL_ID');
    }

    // Verify ownership
    const skill = await prisma.resumeSkill.findFirst({
      where: {
        id: skillId,
        resumeId,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!skill) {
      return errorResponse('Skill not found', 404, 'NOT_FOUND');
    }

    // Delete skill
    await prisma.resumeSkill.delete({
      where: { id: skillId },
    });

    return successResponse(
      { id: skillId },
      'Skill deleted successfully'
    );

  } catch (error) {
    console.error('Error deleting skill:', error);
    return errorResponse('Failed to delete skill', 500, 'DELETE_ERROR');
  }
}
