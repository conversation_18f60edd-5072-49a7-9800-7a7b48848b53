import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { z } from 'zod';

const createSkillSchema = z.object({
  resumeId: z.string().uuid('Invalid resume ID'),
  category: z.string().min(1, 'Category is required').max(50, 'Category too long'),
  name: z.string().min(1, 'Skill name is required').max(100, 'Skill name too long'),
  proficiency: z.enum(['Beginner', 'Intermediate', 'Advanced', 'Expert']).default('Intermediate'),
});

const updateSkillSchema = createSkillSchema.partial().extend({
  id: z.string().uuid('Invalid skill ID'),
});

// Helper function to convert proficiency string to number
function convertProficiencyToNumber(proficiency: string): number {
  const proficiencyMap: Record<string, number> = {
    'Beginner': 1,
    'Intermediate': 2,
    'Advanced': 3,
    'Expert': 4,
  };
  return proficiencyMap[proficiency] || 2; // Default to Intermediate
}

interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

function errorResponse(
  error: string, 
  status: number = 500, 
  code?: string, 
  details?: any
): NextResponse<ErrorResponse> {
  return NextResponse.json(
    { success: false, error, code, details },
    { status }
  );
}

function successResponse<T>(
  data: T, 
  message?: string, 
  status: number = 200
): NextResponse<SuccessResponse<T>> {
  return NextResponse.json(
    { success: true, data, message },
    { status }
  );
}

// POST - Create new skill
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const body = await request.json();
    const validation = createSkillSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const data = validation.data;

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: data.resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Check for duplicate skill in same category
    const existingSkill = await prisma.resumeSkill.findFirst({
      where: {
        resumeId: data.resumeId,
        skillName: data.name,
        category: data.category,
      },
    });

    if (existingSkill) {
      return errorResponse('Skill already exists in this category', 409, 'DUPLICATE_SKILL');
    }

    // Create skill
    const skill = await prisma.resumeSkill.create({
      data: {
        resumeId: data.resumeId,
        skillName: data.name,
        category: data.category,
        proficiency: convertProficiencyToNumber(data.proficiency),
      },
    });

    return successResponse(skill, 'Skill created successfully', 201);

  } catch (error) {
    console.error('Error creating skill:', error);
    return errorResponse('Failed to create skill', 500, 'CREATE_ERROR');
  }
}

// PUT - Update existing skill
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const body = await request.json();
    const validation = updateSkillSchema.safeParse(body);
    
    if (!validation.success) {
      return errorResponse(
        'Invalid request data',
        400,
        'VALIDATION_ERROR',
        validation.error.errors
      );
    }

    const { id, ...updateData } = validation.data;

    // Verify ownership through resume
    const skill = await prisma.resumeSkill.findFirst({
      where: {
        id,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!skill) {
      return errorResponse('Skill not found', 404, 'NOT_FOUND');
    }

    // Check for duplicate if name or category is being changed
    if (updateData.name || updateData.category) {
      const existingSkill = await prisma.resumeSkill.findFirst({
        where: {
          resumeId: skill.resumeId,
          skillName: updateData.name || skill.skillName,
          category: updateData.category || skill.category,
          id: { not: id },
        },
      });

      if (existingSkill) {
        return errorResponse('Skill already exists in this category', 409, 'DUPLICATE_SKILL');
      }
    }

    // Update skill
    const updatedSkill = await prisma.resumeSkill.update({
      where: { id },
      data: {
        skillName: updateData.name,
        category: updateData.category,
        proficiency: updateData.proficiency ? convertProficiencyToNumber(updateData.proficiency) : undefined,

      },
    });

    return successResponse(updatedSkill, 'Skill updated successfully');

  } catch (error) {
    console.error('Error updating skill:', error);
    return errorResponse('Failed to update skill', 500, 'UPDATE_ERROR');
  }
}

// DELETE - Remove skill
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return errorResponse('Skill ID is required', 400, 'MISSING_ID');
    }

    // Verify ownership through resume
    const skill = await prisma.resumeSkill.findFirst({
      where: {
        id,
        resume: {
          userId: session.user.id,
          isActive: true,
        },
      },
    });

    if (!skill) {
      return errorResponse('Skill not found', 404, 'NOT_FOUND');
    }

    // Delete skill
    await prisma.resumeSkill.delete({
      where: { id },
    });

    return successResponse({ id }, 'Skill deleted successfully');

  } catch (error) {
    console.error('Error deleting skill:', error);
    return errorResponse('Failed to delete skill', 500, 'DELETE_ERROR');
  }
}

// GET - Fetch skills for a resume
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return errorResponse('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const { searchParams } = new URL(request.url);
    const resumeId = searchParams.get('resumeId');

    if (!resumeId) {
      return errorResponse('Resume ID is required', 400, 'MISSING_RESUME_ID');
    }

    // Verify resume ownership
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!resume) {
      return errorResponse('Resume not found', 404, 'NOT_FOUND');
    }

    // Fetch skills grouped by category
    const skills = await prisma.resumeSkill.findMany({
      where: {
        resumeId,
      },
      orderBy: [
        { category: 'asc' },
        { skillName: 'asc' },
      ],
    });

    // Group skills by category
    const groupedSkills = skills.reduce((acc, skill) => {
      if (!acc[skill.category]) {
        acc[skill.category] = [];
      }
      acc[skill.category].push(skill);
      return acc;
    }, {} as Record<string, typeof skills>);

    return successResponse({ skills, groupedSkills });

  } catch (error) {
    console.error('Error fetching skills:', error);
    return errorResponse('Failed to fetch skills', 500, 'FETCH_ERROR');
  }
}
