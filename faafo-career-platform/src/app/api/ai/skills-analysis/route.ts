import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { unifiedAIService } from '@/lib/unifiedAIService';
import { cacheService, aiCacheKeys } from '@/lib/services/cacheService';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema
const skillsAnalysisSchema = z.object({
  currentSkills: z.array(z.string()).min(1, 'At least one current skill is required').max(50, 'Too many skills'),
  targetCareerPath: z.string().min(2, 'Career path is required').max(200, 'Career path name too long'),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']),
  timeframe: z.enum(['3_months', '6_months', '1_year', '2_years']).optional().default('1_year'),
  focusAreas: z.array(z.string()).optional().default([]),
  includeMarketData: z.boolean().optional().default(true),
});

async function getUserSkillsFromProgress(userId: string): Promise<string[]> {
  try {
    // Get skills from completed learning resources
    const completedProgress = await prisma.userLearningProgress.findMany({
      where: {
        userId: userId,
        status: 'COMPLETED'
      },
      include: {
        resource: {
          include: {
            skills: true
          }
        }
      }
    });

    const skillsFromLearning = completedProgress.flatMap(progress => 
      progress.resource.skills.map(skill => skill.name)
    );

    // Get skills from assessment responses
    const assessment = await prisma.assessment.findFirst({
      where: {
        userId: userId,
        status: 'COMPLETED'
      },
      include: {
        responses: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    });

    const skillsFromAssessment: string[] = [];
    if (assessment) {
      const skillsResponse = assessment.responses.find(r => r.questionKey.includes('skills'));
      if (skillsResponse && Array.isArray(skillsResponse.answerValue)) {
        const skillsArray = skillsResponse.answerValue as string[];
        skillsFromAssessment.push(...skillsArray);
      }
    }

    // Combine and deduplicate
    const combinedSkills = [...skillsFromLearning, ...skillsFromAssessment];
    const uniqueSkills = Array.from(new Set(combinedSkills));
    return uniqueSkills;

  } catch (error) {
    console.error('Error fetching user skills:', error);
    return [];
  }
}

async function getCareerPathRequirements(careerPath: string) {
  try {
    // Try to find matching career path in database
    const dbCareerPath = await prisma.careerPath.findFirst({
      where: {
        OR: [
          { name: { contains: careerPath } },
          { slug: careerPath.toLowerCase().replace(/\s+/g, '-') }
        ]
      },
      include: {
        relatedSkills: true,
        learningResources: {
          where: { isActive: true },
          include: {
            skills: true
          }
        }
      }
    });

    if (dbCareerPath) {
      return {
        requiredSkills: dbCareerPath.relatedSkills.map(skill => skill.name),
        learningResources: dbCareerPath.learningResources.map(resource => ({
          title: resource.title,
          type: resource.type,
          skillLevel: resource.skillLevel,
          skills: resource.skills.map(skill => skill.name)
        }))
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching career path requirements:', error);
    return null;
  }
}

async function handleSkillsAnalysis(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }

  const userId = session.user.id;

  try {
    const body = await request.json();
    const validation = skillsAnalysisSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { 
      currentSkills, 
      targetCareerPath, 
      experienceLevel, 
      timeframe, 
      focusAreas, 
      includeMarketData 
    } = validation.data;

    // Generate cache key
    const cacheKey = aiCacheKeys.skillsAnalysis(userId, `${targetCareerPath}_${experienceLevel}_${timeframe}`);

    // Check cache first
    const cached = await cacheService.getJSON(cacheKey);
    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true,
        message: 'Skills analysis retrieved from cache'
      });
    }

    // Get additional user skills from their learning progress
    const userSkillsFromProgress = await getUserSkillsFromProgress(userId);
    const combinedCurrentSkills = [...currentSkills, ...userSkillsFromProgress];
    const allCurrentSkills = Array.from(new Set(combinedCurrentSkills));

    // Get career path requirements from database
    const careerPathData = await getCareerPathRequirements(targetCareerPath);

    // Perform AI analysis using unified service
    const analysisResult = await unifiedAIService.analyzeSkillsGap(
      allCurrentSkills,
      targetCareerPath,
      experienceLevel,
      userId
    );

    if (!analysisResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: analysisResult.error || 'Failed to analyze skills gap' 
        },
        { status: 500 }
      );
    }

    // Enhance analysis with database information
    const enhancedData = {
      ...analysisResult.data,
      metadata: {
        ...analysisResult.metadata,
        targetCareerPath,
        experienceLevel,
        timeframe,
        focusAreas,
        totalCurrentSkills: allCurrentSkills.length,
        skillsFromProgress: userSkillsFromProgress.length,
        hasCareerPathData: !!careerPathData,
        provider: 'gemini'
      },
      careerPathData: careerPathData ? {
        requiredSkills: careerPathData.requiredSkills,
        availableLearningResources: careerPathData.learningResources.length,
        recommendedResources: careerPathData.learningResources
          .filter(resource => {
            const skillLevel = resource.skillLevel as string;
            return (
              (experienceLevel === 'entry' && skillLevel === 'BEGINNER') ||
              (experienceLevel === 'mid' && ['BEGINNER', 'INTERMEDIATE'].includes(skillLevel)) ||
              (experienceLevel === 'senior' && ['INTERMEDIATE', 'ADVANCED'].includes(skillLevel)) ||
              (experienceLevel === 'executive' && ['ADVANCED', 'EXPERT'].includes(skillLevel))
            );
          })
          .slice(0, 10) // Limit to top 10 recommendations
      } : null,
      actionPlan: {
        timeframe,
        prioritySkills: analysisResult.data.learningRecommendations
          ?.filter((rec: any) => rec.priority === 'high')
          .map((rec: any) => rec.skill) || [],
        estimatedHoursPerWeek: timeframe === '3_months' ? 15 : timeframe === '6_months' ? 10 : 8,
        milestones: generateMilestones(timeframe, analysisResult.data.learningRecommendations || [])
      }
    };

    // Cache is handled automatically by the unified AI service
    // Track usage analytics
    console.log(`Skills analysis completed for user ${userId}, career: ${targetCareerPath}, level: ${experienceLevel}`);

    return NextResponse.json({
      success: true,
      data: enhancedData,
      cached: analysisResult.cached || false,
      metadata: analysisResult.metadata,
      message: 'Skills analysis completed successfully'
    });

  } catch (error) {
    console.error('Skills analysis error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred during skills analysis' 
      },
      { status: 500 }
    );
  }
}

function generateMilestones(timeframe: string, recommendations: any[]): any[] {
  const highPriority = recommendations.filter(rec => rec.priority === 'high');
  const mediumPriority = recommendations.filter(rec => rec.priority === 'medium');
  const lowPriority = recommendations.filter(rec => rec.priority === 'low');

  const milestones = [];
  
  if (timeframe === '3_months') {
    milestones.push(
      { month: 1, focus: 'Foundation', skills: highPriority.slice(0, 2).map(r => r.skill) },
      { month: 2, focus: 'Core Skills', skills: highPriority.slice(2, 4).map(r => r.skill) },
      { month: 3, focus: 'Integration', skills: mediumPriority.slice(0, 2).map(r => r.skill) }
    );
  } else if (timeframe === '6_months') {
    milestones.push(
      { month: 1, focus: 'Foundation', skills: highPriority.slice(0, 2).map(r => r.skill) },
      { month: 2, focus: 'Core Skills', skills: highPriority.slice(2, 4).map(r => r.skill) },
      { month: 4, focus: 'Advanced Skills', skills: mediumPriority.slice(0, 3).map(r => r.skill) },
      { month: 6, focus: 'Specialization', skills: lowPriority.slice(0, 2).map(r => r.skill) }
    );
  } else {
    milestones.push(
      { month: 3, focus: 'Foundation', skills: highPriority.slice(0, 3).map(r => r.skill) },
      { month: 6, focus: 'Core Skills', skills: highPriority.slice(3).concat(mediumPriority.slice(0, 2)).map(r => r.skill) },
      { month: 9, focus: 'Advanced Skills', skills: mediumPriority.slice(2, 5).map(r => r.skill) },
      { month: 12, focus: 'Mastery', skills: lowPriority.slice(0, 3).map(r => r.skill) }
    );
  }

  return milestones.filter(m => m.skills.length > 0);
}

// GET endpoint for retrieving cached analysis
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 25 }, // 25 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const careerPath = searchParams.get('careerPath');
      const experienceLevel = searchParams.get('experienceLevel');
      const timeframe = searchParams.get('timeframe') || '1_year';

      if (!careerPath || !experienceLevel) {
        return NextResponse.json(
          { success: false, error: 'Career path and experience level are required' },
          { status: 400 }
        );
      }

      const cacheKey = aiCacheKeys.skillsAnalysis(userId, `${careerPath}_${experienceLevel}_${timeframe}`);
      const cached = await cacheService.getJSON(cacheKey);

      if (!cached) {
        return NextResponse.json(
          { success: false, error: 'Analysis not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: cached,
        cached: true
      });
    }
  );
}

// POST endpoint for new analysis
export const POST = withErrorHandler(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 8 }, // 8 analyses per 15 minutes
    () => handleSkillsAnalysis(request)
  );
});
