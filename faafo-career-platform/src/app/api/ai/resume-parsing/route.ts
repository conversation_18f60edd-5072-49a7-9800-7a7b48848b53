import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ResumeParsingService } from '@/lib/resumeParsingService';
import { with<PERSON><PERSON>r<PERSON>and<PERSON> } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';

// Validation schema for LinkedIn import
const linkedInImportSchema = z.object({
  resumeText: z.string().min(50, 'LinkedIn profile text must be at least 50 characters').max(50000, 'Text too long'),
  action: z.literal('parse_linkedin'),
});

// SECURITY FIX: Enhanced file validation to prevent DoS attacks
async function extractTextFromFile(file: File): Promise<string> {
  // File size validation (10MB limit)
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxFileSize) {
    throw new Error('File too large. Maximum size is 10MB.');
  }

  // File type validation
  const allowedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'application/rtf'
  ];

  if (!allowedTypes.includes(file.type)) {
    throw new Error('Invalid file type. Only PDF, DOCX, TXT, and RTF files are allowed.');
  }

  // File name validation
  const fileName = file.name.toLowerCase();
  if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
    throw new Error('Invalid file name.');
  }

  const buffer = Buffer.from(await file.arrayBuffer());

  if (file.type === 'text/plain') {
    return buffer.toString('utf-8');
  } else if (file.type === 'application/pdf' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    // For now, we'll ask users to copy/paste content from PDF/DOCX files
    // This avoids dependency issues while still providing functionality
    throw new Error('PDF and DOCX parsing is temporarily unavailable. Please copy the text from your resume and use the LinkedIn Import feature instead.');
  } else {
    throw new Error('Unsupported file format. Please upload TXT files or use the LinkedIn Import feature.');
  }
}

async function handleResumeParsingRequest(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }

  const userId = session.user.id;

  try {
    const contentType = request.headers.get('content-type') || '';
    
    if (contentType.includes('multipart/form-data')) {
      // Handle file upload for resume parsing
      const formData = await request.formData();
      const file = formData.get('file') as File;
      
      if (!file) {
        return NextResponse.json(
          { success: false, error: 'No file provided' },
          { status: 400 }
        );
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        return NextResponse.json(
          { success: false, error: 'File size too large. Maximum 10MB allowed.' },
          { status: 400 }
        );
      }

      // Extract text from file
      const resumeText = await extractTextFromFile(file);
      
      if (!resumeText || resumeText.trim().length < 50) {
        return NextResponse.json(
          { success: false, error: 'Could not extract sufficient text from file' },
          { status: 400 }
        );
      }

      // Parse the resume using AI
      const parsedData = await ResumeParsingService.parseUploadedResume(resumeText, userId);

      return NextResponse.json({
        success: true,
        data: {
          parsedData,
          originalText: resumeText.substring(0, 500) + '...', // Preview
          source: 'upload',
          fileName: file.name
        },
        message: 'Resume parsed successfully'
      });

    } else {
      // Handle JSON request for LinkedIn import
      const body = await request.json();
      
      if (body.action === 'parse_linkedin') {
        const validation = linkedInImportSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const { resumeText } = validation.data;

        // Parse LinkedIn profile using AI
        const parsedData = await ResumeParsingService.parseLinkedInProfile(resumeText, userId);

        return NextResponse.json({
          success: true,
          data: {
            parsedData,
            originalText: resumeText.substring(0, 500) + '...', // Preview
            source: 'linkedin'
          },
          message: 'LinkedIn profile parsed successfully'
        });
      } else {
        return NextResponse.json(
          { success: false, error: 'Invalid action. Use "parse_linkedin" for LinkedIn import.' },
          { status: 400 }
        );
      }
    }

  } catch (error) {
    console.error('Resume parsing error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred during resume parsing' 
      },
      { status: 500 }
    );
  }
}

// POST endpoint for resume parsing
export const POST = withErrorHandler(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 parsing requests per 15 minutes
    () => handleResumeParsingRequest(request)
  );
});

// GET endpoint for health check
export async function GET(request: NextRequest) {
  return NextResponse.json({
    success: true,
    message: 'Resume parsing service is operational',
    supportedFormats: ['PDF', 'DOCX', 'TXT'],
    features: ['LinkedIn import', 'File upload parsing', 'AI-powered extraction']
  });
}
