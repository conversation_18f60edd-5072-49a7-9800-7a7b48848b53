import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { unifiedAIService } from '@/lib/unifiedAIService';
import { cacheService, aiCacheKeys } from '@/lib/services/cacheService';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';
import crypto from 'crypto';
// Dynamic imports for file parsing to avoid build-time issues

// Validation schema
const resumeAnalysisSchema = z.object({
  resumeText: z.string().min(50, 'Resume text must be at least 50 characters').max(50000, 'Resume text too long'),
  format: z.enum(['text', 'pdf', 'docx']).optional().default('text'),
});

// File upload schema
const fileUploadSchema = z.object({
  file: z.any(), // File will be validated separately
});

async function extractTextFromFile(file: File): Promise<string> {
  const buffer = Buffer.from(await file.arrayBuffer());

  if (file.type === 'application/pdf') {
    const pdfParse = (await import('pdf-parse')).default;
    const data = await pdfParse(buffer);
    return data.text;
  } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    const mammoth = await import('mammoth');
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  } else if (file.type === 'text/plain') {
    return buffer.toString('utf-8');
  } else {
    throw new Error('Unsupported file format. Please upload PDF, DOCX, or TXT files.');
  }
}

async function handleResumeAnalysis(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }

  const userId = session.user.id;
  let resumeText: string;
  let resumeHash: string;

  try {
    const contentType = request.headers.get('content-type') || '';
    
    if (contentType.includes('multipart/form-data')) {
      // Handle file upload
      const formData = await request.formData();
      const file = formData.get('file') as File;
      
      if (!file) {
        return NextResponse.json(
          { success: false, error: 'No file provided' },
          { status: 400 }
        );
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        return NextResponse.json(
          { success: false, error: 'File size too large. Maximum 10MB allowed.' },
          { status: 400 }
        );
      }

      // Extract text from file
      resumeText = await extractTextFromFile(file);
      
      if (!resumeText || resumeText.trim().length < 50) {
        return NextResponse.json(
          { success: false, error: 'Could not extract sufficient text from file' },
          { status: 400 }
        );
      }
    } else {
      // Handle JSON request
      const body = await request.json();
      const validation = resumeAnalysisSchema.safeParse(body);
      
      if (!validation.success) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Invalid request data',
            details: validation.error.errors 
          },
          { status: 400 }
        );
      }

      resumeText = validation.data.resumeText;
    }

    // Generate hash for caching
    resumeHash = crypto.createHash('sha256').update(resumeText).digest('hex').slice(0, 16);

    // Check cache first
    const cacheKey = aiCacheKeys.resumeAnalysis(userId, resumeHash);
    const cached = await cacheService.getJSON(cacheKey);
    
    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true,
        message: 'Resume analysis retrieved from cache'
      });
    }

    // Perform AI analysis using unified service
    const analysisResult = await unifiedAIService.analyzeResume(resumeText, userId);

    if (!analysisResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: analysisResult.error || 'Failed to analyze resume'
        },
        { status: 500 }
      );
    }

    // Cache is handled automatically by the unified AI service
    // No need for manual caching here

    // Track usage analytics
    console.log(`Resume analysis completed for user ${userId}, hash: ${resumeHash}`);

    return NextResponse.json({
      success: true,
      data: analysisResult.data,
      cached: analysisResult.cached || false,
      metadata: analysisResult.metadata,
      message: 'Resume analysis completed successfully'
    });

  } catch (error) {
    console.error('Resume analysis error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred during resume analysis' 
      },
      { status: 500 }
    );
  }
}

// GET endpoint for retrieving cached analysis
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const { searchParams } = new URL(request.url);
      const resumeHash = searchParams.get('hash');

      if (!resumeHash) {
        return NextResponse.json(
          { success: false, error: 'Resume hash required' },
          { status: 400 }
        );
      }

      const userId = session.user.id;
      const cacheKey = aiCacheKeys.resumeAnalysis(userId, resumeHash);
      const cached = await cacheService.getJSON(cacheKey);

      if (!cached) {
        return NextResponse.json(
          { success: false, error: 'Analysis not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: cached,
        cached: true
      });
    }
  );
}

// POST endpoint for new analysis
export const POST = withErrorHandler(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 analyses per 15 minutes
    () => handleResumeAnalysis(request)
  );
});

// DELETE endpoint for clearing cached analysis
export async function DELETE(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const { searchParams } = new URL(request.url);
      const resumeHash = searchParams.get('hash');

      if (!resumeHash) {
        return NextResponse.json(
          { success: false, error: 'Resume hash required' },
          { status: 400 }
        );
      }

      const userId = session.user.id;
      const cacheKey = aiCacheKeys.resumeAnalysis(userId, resumeHash);
      await cacheService.delete(cacheKey);

      return NextResponse.json({
        success: true,
        message: 'Analysis cache cleared'
      });
    }
  );
}
