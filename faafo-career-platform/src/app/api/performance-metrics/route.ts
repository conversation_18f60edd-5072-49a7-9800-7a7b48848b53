import { NextRequest, NextResponse } from 'next/server';
import { withSimpleSecurity } from '@/lib/simple-security';

interface PerformanceMetric {
  metrics: {
    pageLoadTime?: number;
    domContentLoaded?: number;
    firstContentfulPaint?: number;
    largestContentfulPaint?: number;
    cumulativeLayoutShift?: number;
    firstInputDelay?: number;
    timeToInteractive?: number;
    memoryUsage?: {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
      jsHeapSizeLimit: number;
    };
  };
  timestamp: number;
  userAgent: string;
  url: string;
}

// In-memory store for performance metrics (in production, use a database)
const performanceMetrics: PerformanceMetric[] = [];
const MAX_METRICS = 1000; // Keep only the latest 1000 metrics

async function handlePOST(req: NextRequest): Promise<NextResponse> {
  try {
    const body: PerformanceMetric = await req.json();

    // Validate the incoming data
    if (!body.metrics || !body.timestamp || !body.url) {
      return NextResponse.json(
        { error: 'Missing required fields: metrics, timestamp, url' },
        { status: 400 }
      );
    }

    // Add the metric to our store
    performanceMetrics.push({
      ...body,
      timestamp: Date.now() // Use server timestamp for consistency
    });

    // Keep only the latest metrics
    if (performanceMetrics.length > MAX_METRICS) {
      performanceMetrics.splice(0, performanceMetrics.length - MAX_METRICS);
    }

    // Log performance issues
    const { metrics } = body;
    const issues = [];

    if (metrics.pageLoadTime && metrics.pageLoadTime > 5000) {
      issues.push(`Slow page load: ${metrics.pageLoadTime}ms`);
    }

    if (metrics.firstContentfulPaint && metrics.firstContentfulPaint > 3000) {
      issues.push(`Slow FCP: ${metrics.firstContentfulPaint}ms`);
    }

    if (metrics.largestContentfulPaint && metrics.largestContentfulPaint > 4000) {
      issues.push(`Slow LCP: ${metrics.largestContentfulPaint}ms`);
    }

    if (metrics.cumulativeLayoutShift && metrics.cumulativeLayoutShift > 0.25) {
      issues.push(`High CLS: ${metrics.cumulativeLayoutShift}`);
    }

    if (metrics.firstInputDelay && metrics.firstInputDelay > 300) {
      issues.push(`High FID: ${metrics.firstInputDelay}ms`);
    }

    if (issues.length > 0) {
      console.warn(`Performance issues detected for ${body.url}:`, issues);
    }

    return NextResponse.json({ success: true, metricsCount: performanceMetrics.length });
  } catch (error) {
    console.error('Error processing performance metrics:', error);
    return NextResponse.json(
      { error: 'Failed to process performance metrics' },
      { status: 500 }
    );
  }
}

async function handleGET(req: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const url = searchParams.get('url');

    let filteredMetrics = performanceMetrics;

    // Filter by URL if specified
    if (url) {
      filteredMetrics = performanceMetrics.filter(metric => 
        metric.url.includes(url)
      );
    }

    // Get the latest metrics
    const recentMetrics = filteredMetrics
      .slice(-limit)
      .sort((a, b) => b.timestamp - a.timestamp);

    // Calculate aggregated statistics
    const stats = calculatePerformanceStats(filteredMetrics);

    return NextResponse.json({
      metrics: recentMetrics,
      stats,
      totalCount: filteredMetrics.length
    });
  } catch (error) {
    console.error('Error retrieving performance metrics:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve performance metrics' },
      { status: 500 }
    );
  }
}

function calculatePerformanceStats(metrics: PerformanceMetric[]) {
  if (metrics.length === 0) {
    return {
      averagePageLoadTime: 0,
      averageFCP: 0,
      averageLCP: 0,
      averageCLS: 0,
      averageFID: 0,
      p95PageLoadTime: 0,
      p95FCP: 0,
      p95LCP: 0,
      slowPagesCount: 0,
      totalPages: 0
    };
  }

  const pageLoadTimes = metrics
    .map(m => m.metrics.pageLoadTime)
    .filter(Boolean) as number[];
  
  const fcpTimes = metrics
    .map(m => m.metrics.firstContentfulPaint)
    .filter(Boolean) as number[];
  
  const lcpTimes = metrics
    .map(m => m.metrics.largestContentfulPaint)
    .filter(Boolean) as number[];
  
  const clsValues = metrics
    .map(m => m.metrics.cumulativeLayoutShift)
    .filter(Boolean) as number[];
  
  const fidValues = metrics
    .map(m => m.metrics.firstInputDelay)
    .filter(Boolean) as number[];

  const slowPagesCount = pageLoadTimes.filter(time => time > 3000).length;

  return {
    averagePageLoadTime: average(pageLoadTimes),
    averageFCP: average(fcpTimes),
    averageLCP: average(lcpTimes),
    averageCLS: average(clsValues),
    averageFID: average(fidValues),
    p95PageLoadTime: percentile(pageLoadTimes, 95),
    p95FCP: percentile(fcpTimes, 95),
    p95LCP: percentile(lcpTimes, 95),
    slowPagesCount,
    totalPages: metrics.length
  };
}

function average(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
}

function percentile(numbers: number[], p: number): number {
  if (numbers.length === 0) return 0;
  
  const sorted = [...numbers].sort((a, b) => a - b);
  const index = Math.ceil((p / 100) * sorted.length) - 1;
  return sorted[Math.max(0, index)];
}

// Export handlers with security middleware
export const POST = withSimpleSecurity(handlePOST, {
  rateLimit: { max: 100, windowMs: 15 * 60 * 1000 } // 100 requests per 15 minutes
});

export const GET = withSimpleSecurity(handleGET, {
  rateLimit: { max: 50, windowMs: 15 * 60 * 1000 } // 50 requests per 15 minutes
});

// Health check endpoint
export async function OPTIONS(): Promise<NextResponse> {
  return NextResponse.json({
    status: 'healthy',
    metricsCount: performanceMetrics.length,
    lastMetricTimestamp: performanceMetrics.length > 0 
      ? performanceMetrics[performanceMetrics.length - 1].timestamp 
      : null
  });
}
