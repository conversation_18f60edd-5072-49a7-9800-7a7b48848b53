import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { dbOptimization } from '@/lib/services/databaseOptimization';
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection, CSRF_CONFIGS } from '@/lib/csrf-middleware';

// GET - Database statistics and health
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 5 * 60 * 1000, maxRequests: 20 }, // 20 requests per 5 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check admin access
      const { requireAdmin } = await import('@/lib/auth');
      await requireAdmin(session);

      try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');

        switch (action) {
          case 'stats':
            const stats = await dbOptimization.getDatabaseStats();
            return NextResponse.json({
              success: true,
              data: stats
            });

          case 'indexes':
            const indexAnalysis = await dbOptimization.analyzeIndexUsage();
            return NextResponse.json({
              success: true,
              data: indexAnalysis
            });

          case 'tables':
            const tableSizes = await dbOptimization.getTableSizes();
            return NextResponse.json({
              success: true,
              data: tableSizes
            });

          case 'connections':
            const connectionStats = await dbOptimization.getConnectionPoolStats();
            return NextResponse.json({
              success: true,
              data: connectionStats
            });

          case 'recommendations':
            const recommendations = await dbOptimization.getPerformanceRecommendations();
            return NextResponse.json({
              success: true,
              data: recommendations
            });

          case 'metrics':
            const limit = parseInt(searchParams.get('limit') || '50');
            const metrics = dbOptimization.getRecentMetrics(limit);
            return NextResponse.json({
              success: true,
              data: metrics
            });

          default:
            // Return comprehensive database health report
            const [
              databaseStats,
              tableInfo,
              performanceRecommendations,
              recentMetrics,
              performanceReport,
              connectionPoolStats,
              realTimeMetrics
            ] = await Promise.all([
              dbOptimization.getDatabaseStats(),
              dbOptimization.getTableSizes(),
              dbOptimization.getPerformanceRecommendations(),
              Promise.resolve(dbOptimization.getRecentMetrics(20)),
              dbOptimization.getPerformanceReport(),
              dbOptimization.getConnectionPoolStats(),
              Promise.resolve(dbOptimization.getRealTimeMetrics())
            ]);

            return NextResponse.json({
              success: true,
              data: {
                overview: databaseStats,
                tables: tableInfo,
                recommendations: performanceRecommendations,
                recentMetrics,
                performanceReport,
                connectionPool: connectionPoolStats,
                realTime: realTimeMetrics,
                timestamp: new Date().toISOString()
              }
            });
        }

      } catch (error) {
        console.error('Database monitoring error:', error);
        return NextResponse.json(
          { 
            success: false, 
            error: error instanceof Error ? error.message : 'Database monitoring failed' 
          },
          { status: 500 }
        );
      }
    }
  );
}

// POST - Database optimization actions
export const POST = withErrorHandler(withCSRFProtection(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 optimization actions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check admin access
      const { requireAdmin } = await import('@/lib/auth');
      await requireAdmin(session);

      try {
        const body = await request.json();
        const { action } = body;

        switch (action) {
          case 'apply_indexes':
            const indexResult = await dbOptimization.applyPerformanceIndexes();
            return NextResponse.json({
              success: indexResult.success,
              message: indexResult.message,
              ...(indexResult.errors && { errors: indexResult.errors })
            });

          case 'optimize':
            const optimizeResult = await dbOptimization.optimizeDatabase();
            return NextResponse.json({
              success: optimizeResult.success,
              message: optimizeResult.message
            });

          case 'clear_metrics':
            dbOptimization.clearMetrics();
            return NextResponse.json({
              success: true,
              message: 'Query metrics cleared successfully'
            });

          case 'analyze_performance':
            // Run a comprehensive performance analysis
            const [stats, indexAnalysis, tableSizes] = await Promise.all([
              dbOptimization.getDatabaseStats(),
              dbOptimization.analyzeIndexUsage(),
              dbOptimization.getTableSizes()
            ]);

            const analysis = {
              timestamp: new Date().toISOString(),
              performance: {
                avgQueryTime: stats.avgQueryTime,
                slowQueriesCount: stats.slowQueries.length,
                totalQueries: dbOptimization.getRecentMetrics().length
              },
              dataVolume: {
                totalUsers: stats.totalUsers,
                totalResources: stats.totalLearningResources,
                totalPaths: stats.totalLearningPaths,
                activeEnrollments: stats.activeEnrollments
              },
              indexes: indexAnalysis,
              tables: tableSizes.slice(0, 10), // Top 10 largest tables
              recommendations: await dbOptimization.getPerformanceRecommendations()
            };

            return NextResponse.json({
              success: true,
              data: analysis,
              message: 'Performance analysis completed'
            });

          case 'auto_optimize':
            const autoOptimizeResult = await dbOptimization.autoOptimize();
            return NextResponse.json({
              success: autoOptimizeResult.success,
              message: autoOptimizeResult.message,
              optimizations: autoOptimizeResult.optimizations
            });

          case 'performance_report':
            const report = await dbOptimization.getPerformanceReport();
            return NextResponse.json({
              success: true,
              data: report,
              message: 'Performance report generated successfully'
            });

          case 'clear_cache':
            const { queryOptimizer } = await import('@/lib/database/query-optimizer');
            queryOptimizer.clearCache();
            return NextResponse.json({
              success: true,
              message: 'Query cache cleared successfully'
            });

          case 'connection_health':
            const { checkDatabaseHealth } = await import('@/lib/prisma');
            const healthResult = await checkDatabaseHealth();
            return NextResponse.json({
              success: healthResult.healthy,
              data: healthResult,
              message: healthResult.healthy ? 'Database connection is healthy' : 'Database connection issues detected'
            });

          default:
            return NextResponse.json(
              {
                success: false,
                error: 'Invalid action. Available: apply_indexes, optimize, clear_metrics, analyze_performance, auto_optimize, performance_report, clear_cache, connection_health'
              },
              { status: 400 }
            );
        }

      } catch (error) {
        console.error('Database optimization error:', error);
        return NextResponse.json(
          { 
            success: false, 
            error: error instanceof Error ? error.message : 'Database optimization failed' 
          },
          { status: 500 }
        );
      }
    }
  );
}, CSRF_CONFIGS.HIGH_SECURITY));

// PUT - Update database configuration
export const PUT = withCSRFProtection(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 configuration updates per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      // Check admin access
      const { requireAdmin } = await import('@/lib/auth');
      await requireAdmin(session);

      try {
        const body = await request.json();
        const { setting, value } = body;

        // This would typically update database configuration
        // For now, we'll just return the current configuration
        const currentConfig = {
          connectionPoolSize: process.env.DATABASE_CONNECTION_POOL_SIZE || 'default',
          queryTimeout: process.env.DATABASE_QUERY_TIMEOUT || 'default',
          maxConnections: 'database-dependent',
          cacheEnabled: !!process.env.REDIS_URL,
        };

        return NextResponse.json({
          success: true,
          message: 'Database configuration retrieved (update functionality requires environment variable changes)',
          data: {
            current: currentConfig,
            requested: { setting, value },
            note: 'Configuration changes require server restart and environment variable updates'
          }
        });

      } catch (error) {
        console.error('Database configuration error:', error);
        return NextResponse.json(
          { 
            success: false, 
            error: error instanceof Error ? error.message : 'Configuration update failed' 
          },
          { status: 500 }
        );
      }
    }
  );
}, CSRF_CONFIGS.HIGH_SECURITY);
