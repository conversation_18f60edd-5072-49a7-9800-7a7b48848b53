import { NextRequest, NextResponse } from 'next/server';
import { queryAnalyzer } from '@/lib/database/query-analyzer';
import { redisCache } from '@/lib/database/redis-cache';
import { queryOptimizer } from '@/lib/database/query-optimizer';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action') || 'overview';

    switch (action) {
      case 'patterns':
        const patternStats = queryAnalyzer.getQueryPatternStats();
        return NextResponse.json({
          success: true,
          data: patternStats
        });

      case 'n1_detection':
        const timeWindow = parseInt(searchParams.get('timeWindow') || '5');
        const n1Analysis = queryAnalyzer.detectN1Queries(timeWindow);
        return NextResponse.json({
          success: true,
          data: n1Analysis
        });

      case 'optimization_suggestions':
        const suggestions = queryAnalyzer.generateOptimizationSuggestions();
        return NextResponse.json({
          success: true,
          data: suggestions
        });

      case 'cache_health':
        const cacheHealth = await redisCache.getHealthStatus();
        const cacheStats = redisCache.getStats();
        return NextResponse.json({
          success: true,
          data: {
            health: cacheHealth,
            stats: cacheStats
          }
        });

      case 'query_optimizer_stats':
        const optimizerStats = queryOptimizer.getStats();
        const cacheStatsOptimizer = queryOptimizer.getCacheStats();
        return NextResponse.json({
          success: true,
          data: {
            queryStats: optimizerStats,
            cacheStats: cacheStatsOptimizer
          }
        });

      case 'overview':
      default:
        const [
          patterns,
          n1Detection,
          optimizationSuggestions,
          cacheHealthStatus,
          optimizerData
        ] = await Promise.all([
          Promise.resolve(queryAnalyzer.getQueryPatternStats()),
          Promise.resolve(queryAnalyzer.detectN1Queries(5)),
          Promise.resolve(queryAnalyzer.generateOptimizationSuggestions()),
          redisCache.getHealthStatus(),
          Promise.resolve({
            queryStats: queryOptimizer.getStats(),
            cacheStats: queryOptimizer.getCacheStats()
          })
        ]);

        return NextResponse.json({
          success: true,
          data: {
            patterns,
            n1Detection,
            optimizationSuggestions,
            cacheHealth: cacheHealthStatus,
            optimizer: optimizerData
          }
        });
    }

  } catch (error) {
    console.error('Query analysis API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to analyze queries',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'clear_cache':
        await redisCache.clear();
        queryOptimizer.clearCache();
        return NextResponse.json({
          success: true,
          message: 'All caches cleared successfully'
        });

      case 'warm_cache':
        await redisCache.warmCache();
        return NextResponse.json({
          success: true,
          message: 'Cache warming initiated'
        });

      case 'cleanup_patterns':
        queryAnalyzer.cleanup();
        return NextResponse.json({
          success: true,
          message: 'Query patterns cleaned up'
        });

      case 'invalidate_model_cache':
        const { model } = await req.json();
        if (!model) {
          return NextResponse.json(
            { success: false, error: 'Model name is required' },
            { status: 400 }
          );
        }
        await redisCache.invalidateByModel(model);
        return NextResponse.json({
          success: true,
          message: `Cache invalidated for model: ${model}`
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Query analysis POST API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to execute query analysis action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
