import { NextRequest, NextResponse } from 'next/server';
import { productionConnectionPool } from '@/lib/database/production-connection-pool';
import { prismaProductionManager } from '@/lib/database/prisma-production-config';
import { runConnectionPoolLoadTest, runStandardConnectionPoolTests } from '@/lib/database/connection-pool-load-test';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action') || 'status';

    switch (action) {
      case 'status':
        const stats = productionConnectionPool.getStats();
        const config = productionConnectionPool.getConfiguration();
        const healthCheck = await prismaProductionManager.healthCheck();
        
        return NextResponse.json({
          success: true,
          data: {
            stats,
            config: {
              environment: config.environment,
              databaseTier: config.databaseTier,
              maxConnections: config.maxConnections,
              minConnections: config.minConnections,
              enableAutoScaling: config.enableAutoScaling,
              connectionTimeout: config.connectionTimeout,
              idleTimeout: config.idleTimeout,
            },
            health: healthCheck,
            timestamp: new Date().toISOString(),
          }
        });

      case 'metrics':
        const metrics = productionConnectionPool.getMetrics(100);
        return NextResponse.json({
          success: true,
          data: {
            metrics,
            count: metrics.length,
          }
        });

      case 'recommendations':
        const recommendations = productionConnectionPool.getProductionRecommendations();
        const prismaRecommendations = prismaProductionManager.getRecommendations();
        
        return NextResponse.json({
          success: true,
          data: {
            connectionPool: recommendations,
            prisma: prismaRecommendations,
            combined: [...recommendations, ...prismaRecommendations],
          }
        });

      case 'configuration':
        const fullConfig = {
          connectionPool: productionConnectionPool.getConfiguration(),
          prisma: prismaProductionManager.getConfiguration(),
        };
        
        return NextResponse.json({
          success: true,
          data: fullConfig
        });

      case 'health':
        const detailedHealth = await prismaProductionManager.healthCheck();
        const poolStats = productionConnectionPool.getStats();
        
        const overallHealth = {
          status: detailedHealth.status,
          components: {
            database: detailedHealth,
            connectionPool: {
              status: poolStats.failedConnections > poolStats.successfulConnections * 0.1 ? 'unhealthy' : 'healthy',
              stats: poolStats,
            },
          },
          timestamp: new Date().toISOString(),
        };
        
        return NextResponse.json({
          success: true,
          data: overallHealth
        });

      case 'load_test_results':
        // Return cached load test results if available
        // In a real implementation, you'd store these in a database or cache
        return NextResponse.json({
          success: true,
          data: {
            message: 'Load test results would be retrieved from storage',
            lastRun: null,
          }
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Connection pool API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve connection pool information',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'run_load_test':
        const { config } = await req.json();
        
        if (!config) {
          return NextResponse.json(
            { success: false, error: 'Load test configuration is required' },
            { status: 400 }
          );
        }

        // Validate configuration
        const requiredFields = ['concurrentConnections', 'testDurationMs', 'queryTypes', 'targetQPS'];
        for (const field of requiredFields) {
          if (!(field in config)) {
            return NextResponse.json(
              { success: false, error: `Missing required field: ${field}` },
              { status: 400 }
            );
          }
        }

        // Run load test
        const result = await runConnectionPoolLoadTest(config);
        
        return NextResponse.json({
          success: true,
          data: result,
          message: 'Load test completed successfully'
        });

      case 'run_standard_tests':
        const standardResults = await runStandardConnectionPoolTests();
        
        return NextResponse.json({
          success: true,
          data: standardResults,
          message: 'Standard load tests completed successfully'
        });

      case 'reset_metrics':
        productionConnectionPool.reset();
        
        return NextResponse.json({
          success: true,
          message: 'Connection pool metrics reset successfully'
        });

      case 'force_health_check':
        const healthResult = await prismaProductionManager.healthCheck();
        
        return NextResponse.json({
          success: true,
          data: healthResult,
          message: 'Health check completed'
        });

      case 'optimize_configuration':
        const currentStats = productionConnectionPool.getStats();
        const optimizationSuggestions = productionConnectionPool.optimizeConfiguration();
        
        return NextResponse.json({
          success: true,
          data: {
            currentStats,
            suggestions: optimizationSuggestions,
            recommendations: productionConnectionPool.getProductionRecommendations(),
          },
          message: 'Configuration optimization analysis completed'
        });

      case 'simulate_load':
        const { duration = 30000, connections = 10 } = await req.json();
        
        // Simple load simulation
        const simulationConfig = {
          concurrentConnections: connections,
          testDurationMs: duration,
          queryTypes: ['read', 'write'] as ('read' | 'write' | 'complex')[],
          rampUpTimeMs: 5000,
          rampDownTimeMs: 5000,
          targetQPS: 20,
          enableMetrics: true,
        };
        
        const simulationResult = await runConnectionPoolLoadTest(simulationConfig);
        
        return NextResponse.json({
          success: true,
          data: simulationResult,
          message: 'Load simulation completed'
        });

      case 'export_metrics':
        const exportMetrics = productionConnectionPool.getMetrics(1000);
        const exportStats = productionConnectionPool.getStats();
        const exportConfig = productionConnectionPool.getConfiguration();
        
        const exportData = {
          timestamp: new Date().toISOString(),
          environment: exportConfig.environment,
          databaseTier: exportConfig.databaseTier,
          stats: exportStats,
          metrics: exportMetrics,
          recommendations: productionConnectionPool.getProductionRecommendations(),
        };
        
        return NextResponse.json({
          success: true,
          data: exportData,
          message: 'Metrics exported successfully'
        });

      case 'validate_configuration':
        const validationResult = await validateConnectionPoolConfiguration();
        
        return NextResponse.json({
          success: true,
          data: validationResult,
          message: 'Configuration validation completed'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Connection pool POST API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to execute connection pool action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Validate connection pool configuration
 */
async function validateConnectionPoolConfiguration() {
  const config = productionConnectionPool.getConfiguration();
  const stats = productionConnectionPool.getStats();
  const health = await prismaProductionManager.healthCheck();
  
  const validation = {
    isValid: true,
    warnings: [] as string[],
    errors: [] as string[],
    recommendations: [] as string[],
  };

  // Validate configuration values
  if (config.maxConnections < config.minConnections) {
    validation.errors.push('maxConnections must be greater than minConnections');
    validation.isValid = false;
  }

  if (config.connectionTimeout < 5000) {
    validation.warnings.push('connectionTimeout is very low, may cause timeouts under load');
  }

  if (config.maxConnections > 200 && config.databaseTier !== 'enterprise') {
    validation.warnings.push('High connection count for non-enterprise tier may cause performance issues');
  }

  // Validate current performance
  const utilizationRate = stats.activeConnections / config.maxConnections;
  if (utilizationRate > 0.9) {
    validation.warnings.push('Connection pool utilization is very high');
    validation.recommendations.push('Consider increasing maxConnections or optimizing queries');
  }

  if (stats.averageConnectionTime > 1000) {
    validation.warnings.push('High average connection time detected');
    validation.recommendations.push('Check database performance and network latency');
  }

  if (health.status === 'unhealthy') {
    validation.errors.push('Database health check failed');
    validation.isValid = false;
  }

  // Environment-specific validations
  if (config.environment === 'production') {
    if (!config.enableAutoScaling) {
      validation.recommendations.push('Consider enabling auto-scaling for production');
    }
    
    if (!config.connectionLeakDetection) {
      validation.recommendations.push('Enable connection leak detection for production');
    }
    
    if (config.healthCheckInterval > 30000) {
      validation.warnings.push('Health check interval is quite long for production');
    }
  }

  return {
    config,
    stats,
    health,
    validation,
    timestamp: new Date().toISOString(),
  };
}
