import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Schema for error logging
const ErrorEventSchema = z.object({
  message: z.string(),
  stack: z.string().optional(),
  url: z.string(),
  metadata: z.record(z.any()).optional(),
  level: z.enum(['error', 'warning', 'info']).default('error'),
  source: z.string().default('unknown'),
  userAgent: z.string().optional(),
  userId: z.string().optional(),
});

// GET /api/admin/errors - Fetch recent errors
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized - Authentication required' },
        { status: 401 }
      );
    }

    // Check user role for admin privileges
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });

    const isAdmin = user?.role === 'ADMIN' || user?.role === 'SUPER_ADMIN';

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const level = searchParams.get('level');

    // Build where clause
    const where: any = {};
    if (level && ['error', 'warning', 'info'].includes(level)) {
      // Map lowercase level to uppercase for Prisma enum
      const levelMap: Record<string, 'ERROR' | 'WARNING' | 'INFO'> = {
        'error': 'ERROR',
        'warning': 'WARNING',
        'info': 'INFO',
      };
      where.level = levelMap[level];
    }

    // Fetch errors from database
    const errors = await prisma.errorLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: limit,
      skip: offset,
      select: {
        id: true,
        message: true,
        stack: true,
        url: true,
        timestamp: true,
        metadata: true,
        level: true,
        source: true,
        userAgent: true,
        userId: true,
        resolved: true,
      },
    });

    return NextResponse.json({
      errors: errors.map(error => ({
        ...error,
        timestamp: error.timestamp.getTime(),
        metadata: error.metadata as Record<string, any> || {},
      })),
      total: await prisma.errorLog.count({ where }),
    });

  } catch (error) {
    console.error('Error fetching error logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch error logs' },
      { status: 500 }
    );
  }
}

// POST /api/admin/errors - Log a new error (for internal use)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = ErrorEventSchema.parse(body);

    // Map lowercase level to uppercase for Prisma enum
    const levelMap: Record<string, 'ERROR' | 'WARNING' | 'INFO'> = {
      'error': 'ERROR',
      'warning': 'WARNING',
      'info': 'INFO',
    };

    // Create error log entry
    const errorLog = await prisma.errorLog.create({
      data: {
        message: validatedData.message,
        stack: validatedData.stack,
        url: validatedData.url,
        metadata: validatedData.metadata || {},
        level: levelMap[validatedData.level] || 'ERROR',
        source: validatedData.source,
        userAgent: validatedData.userAgent,
        userId: validatedData.userId,
        timestamp: new Date(),
        resolved: false,
      },
    });

    // Send critical errors to external monitoring (Sentry, etc.)
    if (validatedData.level === 'error') {
      // Log to console for now, could integrate with external services
      console.error('Critical error logged:', {
        id: errorLog.id,
        message: validatedData.message,
        url: validatedData.url,
        timestamp: new Date().toISOString(),
      });
    }

    return NextResponse.json({
      success: true,
      errorId: errorLog.id,
    });

  } catch (error) {
    console.error('Error logging error event:', error);
    return NextResponse.json(
      { error: 'Failed to log error event' },
      { status: 500 }
    );
  }
}
