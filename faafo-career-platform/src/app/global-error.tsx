'use client';

// Force dynamic rendering for error pages
export const dynamic = 'force-dynamic';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Error - FAAFO Career Platform</title>
        <meta name="description" content="An error occurred on the FAAFO Career Platform" />
      </head>
      <body>
        <main role="main" style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f9fafb',
          padding: '1rem'
        }}>
          <div style={{
            maxWidth: '28rem',
            width: '100%',
            backgroundColor: 'white',
            borderRadius: '0.5rem',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            padding: '2rem',
            textAlign: 'center'
          }}>
            <div style={{
              fontSize: '3rem',
              color: '#ef4444',
              marginBottom: '1.5rem'
            }} aria-hidden="true">
              ⚠️
            </div>

            <h1 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: '#111827',
              marginBottom: '0.5rem'
            }}>
              Something went wrong
            </h1>

            <p style={{
              color: '#6b7280',
              marginBottom: '1.5rem'
            }}>
              We apologize for the inconvenience. A critical error occurred and we're working to fix it.
            </p>

            <div role="alert" style={{
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '0.375rem',
              padding: '0.75rem',
              marginBottom: '1.5rem',
              fontSize: '0.875rem',
              color: '#991b1b'
            }}>
              Error ID: {error.digest || 'Unknown'}
            </div>

            <button
              onClick={reset}
              style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                minHeight: '44px',
                padding: '0.75rem 1rem',
                backgroundColor: '#2563eb',
                color: 'white',
                fontWeight: '500',
                borderRadius: '0.375rem',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.15s',
                fontSize: '1rem'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = '#1d4ed8';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = '#2563eb';
              }}
              onFocus={(e) => {
                e.currentTarget.style.outline = '3px solid #93c5fd';
                e.currentTarget.style.outlineOffset = '2px';
              }}
              onBlur={(e) => {
                e.currentTarget.style.outline = 'none';
              }}
              aria-label="Try to reload the page"
            >
              🔄 Try Again
            </button>

            <div style={{ marginTop: '1rem' }}>
              <a
                href="/"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  minHeight: '44px',
                  padding: '0.75rem 1rem',
                  color: '#2563eb',
                  textDecoration: 'underline',
                  fontSize: '0.875rem'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.outline = '3px solid #93c5fd';
                  e.currentTarget.style.outlineOffset = '2px';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.outline = 'none';
                }}
              >
                🏠 Return to Home
              </a>
            </div>
          </div>
        </main>
      </body>
    </html>
  );
}
