'use client';

import { useEffect } from 'react';

// Force dynamic rendering for error pages
export const dynamic = 'force-dynamic';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '1rem' }}>
      <div style={{ maxWidth: '28rem', width: '100%', backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)', padding: '2rem', textAlign: 'center' }}>
        <div style={{ marginBottom: '1.5rem' }}>
          <div style={{ fontSize: '3rem', color: '#ef4444', marginBottom: '1rem' }}>
            ⚠️
          </div>

          <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#111827', marginBottom: '0.5rem' }}>
            Something went wrong!
          </h1>

          <p style={{ color: '#6b7280', marginBottom: '1rem' }}>
            We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
          </p>

          {process.env.NODE_ENV === 'development' && (
            <details style={{ textAlign: 'left', backgroundColor: '#f3f4f6', padding: '1rem', borderRadius: '0.375rem', marginBottom: '1rem' }}>
              <summary style={{ cursor: 'pointer', fontWeight: '500', fontSize: '0.875rem' }}>
                Error Details (Development Only)
              </summary>
              <pre style={{ marginTop: '0.5rem', fontSize: '0.75rem', overflow: 'auto' }}>
                {error.message}
                {error.stack && (
                  <>
                    {'\n\nStack Trace:\n'}
                    {error.stack}
                  </>
                )}
              </pre>
            </details>
          )}
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          <button
            onClick={reset}
            style={{
              width: '100%',
              padding: '0.75rem 1rem',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '0.375rem',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            🔄 Try Again
          </button>

          <a
            href="/"
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              minHeight: '44px',
              padding: '0.75rem 1rem',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              borderRadius: '0.375rem',
              textDecoration: 'none',
              color: '#374151',
              fontSize: '1rem'
            }}
          >
            🏠 Go to Home
          </a>
        </div>

        <div style={{ marginTop: '1.5rem', fontSize: '0.875rem', color: '#6b7280' }}>
          <p>
            Error ID: {error.digest || 'Unknown'}
          </p>
        </div>
      </div>
    </div>
  );
}
