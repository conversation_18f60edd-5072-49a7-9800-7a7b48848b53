// This file configures the initialization of Sentry on the browser/client side.
// The config you add here will be used whenever a page is visited.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

// Only initialize Sentry if DSN is configured and package is available
if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_SENTRY_DSN) {
  try {
    const Sentry = require("@sentry/nextjs");

    Sentry.init({
      dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
      environment: process.env.NODE_ENV,

      // Adjust this value in production, or use tracesSampler for greater control
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1,

      // Setting this option to true will print useful information to the console while you're setting up Sentry.
      debug: process.env.NODE_ENV === 'development',

      // Enhanced session replay configuration
      replaysOnErrorSampleRate: 1.0,
      replaysSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 0.5,

      // Enhanced integrations
      integrations: [
        Sentry.replayIntegration({
          maskAllText: false,
          blockAllMedia: true,
          maskAllInputs: true,
        }),
        Sentry.browserTracingIntegration(),
        Sentry.feedbackIntegration({
          colorScheme: "system",
        }),
      ],

      // Enhanced error filtering
      beforeSend(event: any, hint: any) {
        // Filter out common non-actionable errors
        if (event.exception?.values?.[0]?.type === 'ChunkLoadError') {
          return null;
        }

        if (event.exception?.values?.[0]?.type === 'ResizeObserver loop limit exceeded') {
          return null;
        }

        // Add user context if available
        if (typeof window !== 'undefined' && (window as any).user) {
          event.user = {
            id: (window as any).user.id,
            email: (window as any).user.email,
          };
        }

        return event;
      },

      // Add release tracking
      release: process.env.NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA || 'development',
    });
  } catch (error) {
    console.warn('Sentry not available for client-side error reporting');
  }
}
