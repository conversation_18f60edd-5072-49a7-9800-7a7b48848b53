#!/usr/bin/env node

/**
 * Diagnostic script to identify Resume Builder issues
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSING RESUME BUILDER ISSUES\n');
console.log('='.repeat(50));

// Check 1: File Structure
console.log('📁 Checking file structure...');

const requiredFiles = [
  'src/app/resume-builder/page.tsx',
  'src/app/resume-builder/create/page.tsx',
  'src/app/api/resume-builder/route.ts',
  'src/app/api/resume-builder/[id]/route.ts',
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    console.log(`✅ ${file} (${stats.size} bytes)`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

// Check 2: Tools Page Integration
console.log('\n🛠️ Checking tools page integration...');

const toolsPath = path.join(__dirname, 'src/app/tools/page.tsx');
if (fs.existsSync(toolsPath)) {
  const toolsContent = fs.readFileSync(toolsPath, 'utf8');
  
  // Count resume-builder entries
  const resumeBuilderMatches = (toolsContent.match(/resume-builder/g) || []).length;
  const resumeMatches = (toolsContent.match(/Resume.*Builder/g) || []).length;
  
  console.log(`📊 Found ${resumeBuilderMatches} 'resume-builder' references`);
  console.log(`📊 Found ${resumeMatches} 'Resume Builder' references`);
  
  // Check for duplicate entries
  if (resumeBuilderMatches > 1) {
    console.log('⚠️  WARNING: Multiple resume-builder entries detected');
    
    // Find line numbers of duplicates
    const lines = toolsContent.split('\n');
    lines.forEach((line, index) => {
      if (line.includes('resume-builder')) {
        console.log(`   Line ${index + 1}: ${line.trim()}`);
      }
    });
  }
  
  // Check for correct href
  if (toolsContent.includes('/resume-builder') && !toolsContent.includes('/tools/resume-builder')) {
    console.log('✅ Correct href found: /resume-builder');
  } else if (toolsContent.includes('/tools/resume-builder')) {
    console.log('❌ WRONG href found: /tools/resume-builder (should be /resume-builder)');
  } else {
    console.log('❌ No resume-builder href found');
  }
  
  // Check for isAvailable: true
  const availablePattern = /resume-builder[\s\S]*?isAvailable:\s*true/;
  if (availablePattern.test(toolsContent)) {
    console.log('✅ Resume Builder is marked as available');
  } else {
    console.log('❌ Resume Builder is NOT marked as available');
  }
  
  // Check for creation category
  if (toolsContent.includes("category: 'creation'")) {
    console.log('✅ Creation category found');
    
    // Check if creation category is defined
    if (toolsContent.includes("creation: 'Creation Tools'")) {
      console.log('✅ Creation category is defined in categoryNames');
    } else {
      console.log('❌ Creation category NOT defined in categoryNames');
    }
  } else {
    console.log('❌ Creation category not found');
  }
  
} else {
  console.log('❌ Tools page not found');
}

// Check 3: Syntax Errors
console.log('\n🔍 Checking for syntax errors...');

const filesToCheck = [
  'src/app/resume-builder/page.tsx',
  'src/app/resume-builder/create/page.tsx',
  'src/app/tools/page.tsx'
];

filesToCheck.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Basic syntax checks
    const issues = [];
    
    // Check for unmatched braces
    const openBraces = (content.match(/{/g) || []).length;
    const closeBraces = (content.match(/}/g) || []).length;
    if (openBraces !== closeBraces) {
      issues.push(`Unmatched braces: ${openBraces} open, ${closeBraces} close`);
    }
    
    // Check for unmatched parentheses
    const openParens = (content.match(/\(/g) || []).length;
    const closeParens = (content.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push(`Unmatched parentheses: ${openParens} open, ${closeParens} close`);
    }
    
    // Check for missing imports
    if (content.includes('useSession') && !content.includes("from 'next-auth/react'")) {
      issues.push('useSession used but not imported');
    }
    
    if (content.includes('useRouter') && !content.includes("from 'next/navigation'")) {
      issues.push('useRouter used but not imported');
    }
    
    if (issues.length === 0) {
      console.log(`✅ ${file} - No obvious syntax issues`);
    } else {
      console.log(`❌ ${file} - Issues found:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
    }
  }
});

// Check 4: Database Schema
console.log('\n🗄️ Checking database schema...');

const schemaPath = path.join(__dirname, 'prisma/schema.prisma');
if (fs.existsSync(schemaPath)) {
  const schemaContent = fs.readFileSync(schemaPath, 'utf8');
  
  const requiredModels = [
    'model Resume',
    'model ResumeExperience',
    'model ResumeEducation',
    'model ResumeSkill',
    'model ResumeProject',
    'model ResumeTemplate'
  ];
  
  const missingModels = requiredModels.filter(model => !schemaContent.includes(model));
  
  if (missingModels.length === 0) {
    console.log('✅ All required database models found');
  } else {
    console.log(`❌ Missing database models: ${missingModels.join(', ')}`);
  }
  
  // Check for proper relationships
  if (schemaContent.includes('userId') && schemaContent.includes('@relation')) {
    console.log('✅ Database relationships configured');
  } else {
    console.log('❌ Database relationships missing');
  }
} else {
  console.log('❌ Prisma schema not found');
}

// Check 5: Next.js Configuration
console.log('\n⚙️ Checking Next.js configuration...');

const nextConfigPath = path.join(__dirname, 'next.config.js');
const nextConfigMjsPath = path.join(__dirname, 'next.config.mjs');

if (fs.existsSync(nextConfigPath) || fs.existsSync(nextConfigMjsPath)) {
  console.log('✅ Next.js config file found');
} else {
  console.log('⚠️  No Next.js config file found (this is usually OK)');
}

// Check package.json for required dependencies
const packagePath = path.join(__dirname, 'package.json');
if (fs.existsSync(packagePath)) {
  const packageContent = fs.readFileSync(packagePath, 'utf8');
  const packageJson = JSON.parse(packageContent);
  
  const requiredDeps = ['next-auth', 'zod', '@prisma/client', 'sonner'];
  const missingDeps = requiredDeps.filter(dep => 
    !packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]
  );
  
  if (missingDeps.length === 0) {
    console.log('✅ All required dependencies found');
  } else {
    console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
  }
}

// Summary and Recommendations
console.log('\n' + '='.repeat(50));
console.log('📋 DIAGNOSIS SUMMARY');
console.log('='.repeat(50));

if (allFilesExist) {
  console.log('✅ All required files exist');
} else {
  console.log('❌ Some required files are missing');
}

console.log('\n💡 RECOMMENDED FIXES:');

console.log('\n1. 🔄 RESTART DEVELOPMENT SERVER');
console.log('   Stop the dev server (Ctrl+C) and restart:');
console.log('   npm run dev');

console.log('\n2. 🗄️ RUN DATABASE MIGRATION');
console.log('   npx prisma generate');
console.log('   npx prisma db push');

console.log('\n3. 🧹 CLEAR NEXT.JS CACHE');
console.log('   rm -rf .next');
console.log('   npm run dev');

console.log('\n4. 🔍 CHECK BROWSER CONSOLE');
console.log('   Open browser dev tools and check for JavaScript errors');

console.log('\n5. 🌐 VERIFY URL');
console.log('   Make sure you\'re visiting: http://localhost:3001/resume-builder');
console.log('   NOT: http://localhost:3001/tools/resume-builder');

console.log('\n6. 🛠️ CHECK TOOLS PAGE');
console.log('   Visit: http://localhost:3001/tools');
console.log('   Look for "Resume & Portfolio Builder" in Creation Tools section');

console.log('\n' + '='.repeat(50));
console.log('Diagnosis completed at:', new Date().toISOString());
