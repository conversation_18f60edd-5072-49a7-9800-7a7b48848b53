import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NotificationSystem } from '@/components/NotificationSystem';

// Mock the notification service with proper implementation
const mockNotificationService = {
  getNotifications: jest.fn(),
  markAsRead: jest.fn(),
  markAllAsRead: jest.fn(),
  subscribe: jest.fn(),
  unsubscribe: jest.fn(),
  hasSubscribers: jest.fn(),
  createNotification: jest.fn(),
};

jest.mock('@/lib/notificationService', () => ({
  notificationService: mockNotificationService,
  __esModule: true,
}));

describe('NotificationSystem', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    mockNotificationService.getNotifications.mockResolvedValue([]);
    mockNotificationService.markAsRead.mockResolvedValue(undefined);
    mockNotificationService.markAllAsRead.mockResolvedValue(undefined);
    mockNotificationService.hasSubscribers.mockReturnValue(false);
  });

  // Basic rendering test (TDD - Test First)
  it('should render notification bell icon', () => {
    render(<NotificationSystem />);
    expect(screen.getByRole('button', { name: /notifications/i })).toBeInTheDocument();
  });

  // Functionality tests (TDD - Test First)
  it('should display notification count badge', async () => {
    const mockNotifications = [
      { id: '1', type: 'achievement', message: 'Goal completed!', read: false },
      { id: '2', type: 'reminder', message: 'Weekly check-in', read: false },
    ];

    mockNotificationService.getNotifications.mockResolvedValue(mockNotifications);

    render(<NotificationSystem />);

    await waitFor(() => {
      expect(screen.getByText('2')).toBeInTheDocument();
    });
  });

  // Error handling tests (TDD - Test First)
  it('should handle notification service errors gracefully', async () => {
    mockNotificationService.getNotifications.mockRejectedValue(new Error('Service unavailable'));

    render(<NotificationSystem />);

    // Should not crash and show fallback state
    expect(screen.getByRole('button', { name: /notifications/i })).toBeInTheDocument();
  });

  // Self-healing tests (TDD - Test First)
  it('should recover from network failures automatically', async () => {
    mockNotificationService.getNotifications
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce([]);

    render(<NotificationSystem />);

    // Should retry and recover after 5 seconds
    await waitFor(() => {
      expect(mockNotificationService.getNotifications).toHaveBeenCalledTimes(1);
    });
  });

  // Real-time updates test (TDD - Test First)
  it('should update notifications in real-time', async () => {
    let subscribeCallback: Function;

    mockNotificationService.subscribe.mockImplementation((callback: Function) => {
      subscribeCallback = callback;
    });

    render(<NotificationSystem />);

    // Simulate real-time notification
    const newNotification = { id: '3', type: 'achievement', message: 'New achievement!', read: false };
    subscribeCallback(newNotification);

    await waitFor(() => {
      expect(screen.getByText('New achievement!')).toBeInTheDocument();
    });
  });

  // Mark as read functionality test (TDD - Test First)
  it('should mark notification as read when clicked', async () => {
    const mockNotifications = [
      { id: '1', type: 'achievement', message: 'Goal completed!', read: false },
    ];

    mockNotificationService.getNotifications.mockResolvedValue(mockNotifications);

    render(<NotificationSystem />);

    // Open notification panel
    const bellButton = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(bellButton);

    await waitFor(() => {
      expect(screen.getByText('Goal completed!')).toBeInTheDocument();
    });

    // Click on notification to mark as read
    const notification = screen.getByText('Goal completed!');
    fireEvent.click(notification);

    await waitFor(() => {
      expect(mockNotificationService.markAsRead).toHaveBeenCalledWith('1');
    });
  });

  // Mark all as read functionality test (TDD - Test First)
  it('should mark all notifications as read', async () => {
    const mockNotifications = [
      { id: '1', type: 'achievement', message: 'Goal completed!', read: false },
      { id: '2', type: 'reminder', message: 'Weekly check-in', read: false },
    ];

    mockNotificationService.getNotifications.mockResolvedValue(mockNotifications);

    render(<NotificationSystem userId="test-user" />);

    // Open notification panel
    const bellButton = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(bellButton);

    await waitFor(() => {
      expect(screen.getByText('Mark all read')).toBeInTheDocument();
    });

    // Click mark all read
    const markAllButton = screen.getByText('Mark all read');
    fireEvent.click(markAllButton);

    await waitFor(() => {
      expect(mockNotificationService.markAllAsRead).toHaveBeenCalledWith('test-user');
    });
  });
});
