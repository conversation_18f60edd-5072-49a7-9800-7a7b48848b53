import { notificationService } from '@/lib/notificationService';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  __esModule: true,
  default: {
    notification: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
    },
  },
}));

describe('notificationService', () => {
  // Basic functionality test (FAILING TEST FIRST - TDD)
  it('should fetch user notifications', async () => {
    const mockNotifications = [
      { id: '1', type: 'achievement', message: 'Goal completed!', read: false },
      { id: '2', type: 'reminder', message: 'Weekly check-in', read: true },
    ];

    const prisma = require('@/lib/prisma').default;
    prisma.notification.findMany.mockResolvedValue(mockNotifications);

    const result = await notificationService.getNotifications('user123');
    
    expect(result).toEqual(mockNotifications);
    expect(prisma.notification.findMany).toHaveBeenCalledWith({
      where: { userId: 'user123' },
      orderBy: { createdAt: 'desc' },
    });
  });

  // Input validation tests (FAILING TEST FIRST - TDD)
  it('should validate inputs properly', async () => {
    // Test invalid inputs
    await expect(notificationService.getNotifications('')).rejects.toThrow();
    await expect(notificationService.getNotifications(null as any)).rejects.toThrow();
    await expect(notificationService.getNotifications(undefined as any)).rejects.toThrow();
  });

  // Error handling tests (FAILING TEST FIRST - TDD)
  it('should handle database errors gracefully', async () => {
    const prisma = require('@/lib/prisma').default;
    prisma.notification.findMany.mockRejectedValue(new Error('Database error'));

    const result = await notificationService.getNotifications('user123');
    
    // Should return empty array as fallback
    expect(result).toEqual([]);
  });

  // Self-healing tests (FAILING TEST FIRST - TDD)
  it('should provide fallback values on failure', async () => {
    const prisma = require('@/lib/prisma').default;
    prisma.notification.findMany.mockRejectedValue(new Error('Service unavailable'));

    const result = await notificationService.getNotifications('user123');
    
    expect(result).toBeDefined();
    expect(Array.isArray(result)).toBe(true);
    // Should return safe default value (empty array)
    expect(result).toEqual([]);
  });

  // Mark as read functionality (FAILING TEST FIRST - TDD)
  it('should mark notification as read', async () => {
    const prisma = require('@/lib/prisma').default;
    prisma.notification.update.mockResolvedValue({ id: '1', read: true });

    await notificationService.markAsRead('1');
    
    expect(prisma.notification.update).toHaveBeenCalledWith({
      where: { id: '1' },
      data: { read: true, readAt: expect.any(Date) },
    });
  });

  // Bulk operations (FAILING TEST FIRST - TDD)
  it('should mark all notifications as read', async () => {
    const prisma = require('@/lib/prisma').default;
    prisma.notification.updateMany.mockResolvedValue({ count: 5 });

    await notificationService.markAllAsRead('user123');
    
    expect(prisma.notification.updateMany).toHaveBeenCalledWith({
      where: { userId: 'user123', read: false },
      data: { read: true, readAt: expect.any(Date) },
    });
  });

  // Real-time subscription (FAILING TEST FIRST - TDD)
  it('should handle real-time subscriptions', () => {
    const callback = jest.fn();
    
    notificationService.subscribe(callback);
    
    // Should store callback for real-time updates
    expect(notificationService.hasSubscribers()).toBe(true);
    
    notificationService.unsubscribe(callback);
    expect(notificationService.hasSubscribers()).toBe(false);
  });
});
