/**
 * SECURITY TESTS FOR RESUME PARSING SYSTEM
 * Comprehensive tests to validate security fixes and prevent regressions
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ResumeParsingService } from '@/lib/resumeParsingService';
import SecurityValidator from '@/lib/securityUtils';

// Mock the unified AI service
jest.mock('@/lib/unifiedAIService', () => ({
  unifiedAIService: {
    generateContent: jest.fn()
  }
}));

describe('Resume Parsing Security Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Cleanup after each test to prevent memory leaks
    jest.clearAllTimers();
    jest.restoreAllMocks();
  });

  describe('Hash Function Security', () => {
    it('should use cryptographically secure hash instead of weak hash', () => {
      const content = 'test content for hashing';
      
      // Test that the hash is consistent
      const hash1 = (ResumeParsingService as any).hashContent(content);
      const hash2 = (ResumeParsingService as any).hashContent(content);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(16); // SHA-256 substring
      expect(hash1).toMatch(/^[a-f0-9]+$/); // Hex format
    });

    it('should handle hash generation errors gracefully', () => {
      // Mock crypto to throw an error
      const originalCrypto = require('crypto');
      jest.doMock('crypto', () => ({
        createHash: () => {
          throw new Error('Crypto error');
        }
      }));

      const content = 'test content';
      const hash = (ResumeParsingService as any).hashContent(content);
      
      expect(hash).toMatch(/^fallback_\d+_[a-z0-9]+$/);
    });
  });

  describe('Input Sanitization', () => {
    it('should sanitize malicious script tags', () => {
      const maliciousInput = '<script>alert("xss")</script>Hello World';
      const sanitized = SecurityValidator.sanitizeString(maliciousInput);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).toBe('Hello World');
    });

    it('should remove javascript protocols', () => {
      const maliciousUrl = 'javascript:alert("xss")';
      
      expect(() => {
        SecurityValidator.sanitizeUrl(maliciousUrl);
      }).toThrow('Invalid URL protocol');
    });

    it('should detect prototype pollution attempts', () => {
      const maliciousObject = {
        name: 'John',
        __proto__: { isAdmin: true }
      };
      
      expect(SecurityValidator.hasPrototypePollution(maliciousObject)).toBe(true);
    });

    it('should sanitize object recursively', () => {
      const maliciousObject = {
        name: '<script>alert("xss")</script>John',
        skills: ['JavaScript', '<img src=x onerror=alert(1)>'],
        nested: {
          description: 'javascript:alert("nested")'
        }
      };
      
      const sanitized = SecurityValidator.sanitizeObject(maliciousObject);
      
      expect(sanitized.name).toBe('John');
      expect(sanitized.skills[1]).not.toContain('<img');
      expect(sanitized.nested.description).not.toContain('javascript:');
    });
  });

  describe('File Upload Validation', () => {
    it('should reject files that are too large', () => {
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', {
        type: 'application/pdf'
      });
      
      expect(() => {
        SecurityValidator.validateFileUpload(largeFile);
      }).toThrow('File too large');
    });

    it('should reject invalid file types', () => {
      const maliciousFile = new File(['content'], 'malicious.exe', {
        type: 'application/x-executable'
      });
      
      expect(() => {
        SecurityValidator.validateFileUpload(maliciousFile);
      }).toThrow('Invalid file type');
    });

    it('should reject files with path traversal attempts', () => {
      const maliciousFile = new File(['content'], '../../../etc/passwd', {
        type: 'text/plain'
      });
      
      expect(() => {
        SecurityValidator.validateFileUpload(maliciousFile);
      }).toThrow('Invalid file name');
    });
  });

  describe('AI Response Validation', () => {
    it('should handle oversized AI responses', async () => {
      const { unifiedAIService } = require('@/lib/unifiedAIService');
      
      // Mock oversized response
      unifiedAIService.generateContent.mockResolvedValue({
        success: true,
        data: 'x'.repeat(200000) // 200KB response
      });

      await expect(
        ResumeParsingService.parseLinkedInProfile('test content', 'user123')
      ).rejects.toThrow('AI response too large');
    });

    it('should detect malformed JSON structures', () => {
      const malformedJson = '{"name": "John", "unclosed": {';
      
      expect(SecurityValidator.isValidJsonStructure(malformedJson)).toBe(false);
    });

    it('should sanitize AI response content', async () => {
      const { unifiedAIService } = require('@/lib/unifiedAIService');
      
      // Mock response with malicious content
      unifiedAIService.generateContent.mockResolvedValue({
        success: true,
        data: {
          content: '{"personalInfo": {"firstName": "<script>alert(1)</script>John"}}'
        }
      });

      const result = await ResumeParsingService.parseLinkedInProfile('test content', 'user123');
      
      expect(result.personalInfo.firstName).not.toContain('<script>');
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should not create memory leaks with large datasets', async () => {
      const { unifiedAIService } = require('@/lib/unifiedAIService');
      
      // Mock successful response
      unifiedAIService.generateContent.mockResolvedValue({
        success: true,
        data: JSON.stringify({
          personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
          summary: 'Test summary',
          experience: [],
          education: [],
          skills: [],
          projects: []
        })
      });

      // Process multiple requests to test for memory leaks
      const promises = Array.from({ length: 100 }, (_, i) => 
        ResumeParsingService.parseLinkedInProfile(`test content ${i}`, `user${i}`)
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(100);
      expect(results[0].personalInfo.firstName).toBe('John');
      
      // Cleanup
      results.length = 0;
    });

    it('should properly cleanup resources on error', async () => {
      const { unifiedAIService } = require('@/lib/unifiedAIService');
      
      // Mock error response
      unifiedAIService.generateContent.mockResolvedValue({
        success: false,
        error: 'AI service error'
      });

      await expect(
        ResumeParsingService.parseLinkedInProfile('test content', 'user123')
      ).rejects.toThrow('Failed to parse LinkedIn profile');
      
      // Verify no hanging promises or resources
      expect(jest.getTimerCount()).toBe(0);
    });
  });

  describe('Race Condition Prevention', () => {
    it('should handle concurrent parsing requests safely', async () => {
      const { unifiedAIService } = require('@/lib/unifiedAIService');
      
      // Mock response with delay to simulate race conditions
      unifiedAIService.generateContent.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return {
          success: true,
          data: JSON.stringify({
            personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
            summary: 'Test summary',
            experience: [],
            education: [],
            skills: [],
            projects: []
          })
        };
      });

      // Start multiple concurrent requests
      const promises = [
        ResumeParsingService.parseLinkedInProfile('content1', 'user1'),
        ResumeParsingService.parseLinkedInProfile('content2', 'user2'),
        ResumeParsingService.parseLinkedInProfile('content3', 'user3')
      ];

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.personalInfo.firstName).toBe('John');
      });
    });
  });

  describe('Error Handling', () => {
    it('should provide specific error messages for validation failures', () => {
      const invalidData = {
        personalInfo: {
          firstName: '', // Invalid: empty
          email: 'invalid-email' // Invalid: not an email
        }
      };

      expect(() => {
        SecurityValidator.validateResumeInput(invalidData);
      }).not.toThrow(); // Basic validation should pass

      // Test with dangerous content
      const dangerousData = {
        personalInfo: {
          firstName: '<script>alert(1)</script>'
        }
      };

      expect(() => {
        SecurityValidator.validateResumeInput(dangerousData);
      }).toThrow('Potentially dangerous content detected');
    });

    it('should handle network timeouts gracefully', async () => {
      const { unifiedAIService } = require('@/lib/unifiedAIService');
      
      // Mock timeout error
      unifiedAIService.generateContent.mockRejectedValue(new Error('Network timeout'));

      await expect(
        ResumeParsingService.parseLinkedInProfile('test content', 'user123')
      ).rejects.toThrow('Failed to parse LinkedIn profile');
    });
  });
});

describe('Performance Tests', () => {
  it('should process large resume data efficiently', async () => {
    const { unifiedAIService } = require('@/lib/unifiedAIService');
    
    const largeResumeData = {
      personalInfo: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
      summary: 'A'.repeat(1000),
      experience: Array.from({ length: 20 }, (_, i) => ({
        company: `Company ${i}`,
        position: `Position ${i}`,
        startDate: '2020-01',
        endDate: '2021-01',
        current: false,
        description: 'B'.repeat(500),
        achievements: Array.from({ length: 5 }, (_, j) => `Achievement ${j}`)
      })),
      education: [],
      skills: [],
      projects: []
    };

    unifiedAIService.generateContent.mockResolvedValue({
      success: true,
      data: JSON.stringify(largeResumeData)
    });

    const startTime = Date.now();
    const result = await ResumeParsingService.parseLinkedInProfile('large content', 'user123');
    const endTime = Date.now();

    expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    expect(result.experience).toHaveLength(20);
  });
});
