/**
 * API Integration Tests
 * Tests API endpoints with real database connections
 */

// Explicitly unmock Prisma
jest.unmock('@prisma/client');

const { PrismaClient } = require('@prisma/client');
import bcrypt from 'bcryptjs';
import { NextRequest } from 'next/server';

// Import API route handlers
import { GET as healthGet } from '@/app/api/health/route';

describe('API Integration Tests', () => {
  let prisma: any;
  let testUser: any;

  beforeAll(async () => {
    // Create fresh Prisma client instance
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
      log: ['error', 'warn'],
    });
    
    await prisma.$connect();
    console.log('✅ API integration test database connected');

    // Create a test user for authenticated endpoints
    try {
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const uniqueEmail = `apitest-${Date.now()}@example.com`;

      console.log('Creating test user with email:', uniqueEmail);

      testUser = await prisma.user.create({
        data: {
          email: uniqueEmail,
          password: hashedPassword,
          name: 'API Test User',
        },
      });

      console.log('Raw testUser result:', testUser);
      console.log('✅ Test user created for API tests:', testUser ? testUser.id : 'undefined');
    } catch (error) {
      console.error('❌ Failed to create test user:', error);
      throw error;
    }
  });

  afterAll(async () => {
    // Clean up test user
    if (testUser) {
      try {
        await prisma.user.delete({
          where: { id: testUser.id },
        });
        console.log('✅ Test user cleaned up');
      } catch (error) {
        console.log('⚠️ Test user cleanup failed (may not exist)');
      }
    }

    if (prisma) {
      await prisma.$disconnect();
      console.log('🔌 API integration test database disconnected');
    }
  });

  describe('Health API', () => {
    it('should return health status', async () => {
      const request = new NextRequest('http://localhost:3000/api/health');
      const response = await healthGet(request);
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty('status', 'healthy');
      expect(data).toHaveProperty('timestamp');
      expect(data).toHaveProperty('services');
      expect(data.services).toHaveProperty('database');
      expect(data.services.database).toHaveProperty('status', 'up');
      
      console.log('✅ Health API test passed');
    });
  });

  describe('Database Operations', () => {
    it('should handle user operations', async () => {
      // Test user creation
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const uniqueEmail = `apidbtest-${Date.now()}@example.com`;

      const user = await prisma.user.create({
        data: {
          email: uniqueEmail,
          password: hashedPassword,
          name: 'API DB Test User',
        },
      });

      expect(user).toBeDefined();
      expect(user.id).toBeDefined();
      expect(user.email).toBe(uniqueEmail);

      // Test user retrieval
      const foundUser = await prisma.user.findUnique({
        where: { email: uniqueEmail },
      });

      expect(foundUser).toBeDefined();
      expect(foundUser.id).toBe(user.id);

      // Clean up
      await prisma.user.delete({
        where: { id: user.id },
      });

      console.log('✅ User operations test passed');
    });

    it('should handle career path operations', async () => {
      const careerPaths = await prisma.careerPath.findMany({
        take: 5,
        select: {
          id: true,
          name: true,
          slug: true,
          isActive: true,
        },
      });

      expect(Array.isArray(careerPaths)).toBe(true);
      console.log(`✅ Found ${careerPaths.length} career paths`);
    });

    it('should handle assessment operations', async () => {
      // Ensure we have a test user
      if (!testUser || !testUser.id) {
        throw new Error('Test user not available for assessment operations test');
      }

      // Create an assessment for the test user
      const assessment = await prisma.assessment.create({
        data: {
          userId: testUser.id,
          status: 'IN_PROGRESS',
          currentStep: 1,
        },
      });

      expect(assessment).toBeDefined();
      expect(assessment.userId).toBe(testUser.id);
      expect(assessment.status).toBe('IN_PROGRESS');

      // Add assessment response
      const response = await prisma.assessmentResponse.create({
        data: {
          assessmentId: assessment.id,
          questionKey: 'api_test_question',
          answerValue: { answer: 'api_test_answer' },
        },
      });

      expect(response).toBeDefined();
      expect(response.assessmentId).toBe(assessment.id);

      // Clean up
      await prisma.assessmentResponse.deleteMany({
        where: { assessmentId: assessment.id },
      });
      
      await prisma.assessment.delete({
        where: { id: assessment.id },
      });

      console.log('✅ Assessment operations test passed');
    });
  });

  describe('Data Integrity', () => {
    it('should maintain referential integrity', async () => {
      // Test that we can't create orphaned records
      const hashedPassword = await bcrypt.hash('testpassword123', 10);
      const uniqueEmail = `integritytest-${Date.now()}@example.com`;

      const user = await prisma.user.create({
        data: {
          email: uniqueEmail,
          password: hashedPassword,
          name: 'Integrity Test User',
        },
      });

      const assessment = await prisma.assessment.create({
        data: {
          userId: user.id,
          status: 'IN_PROGRESS',
          currentStep: 1,
        },
      });

      // Try to delete user with existing assessment (should fail or cascade)
      try {
        await prisma.user.delete({
          where: { id: user.id },
        });
        
        // If deletion succeeded, assessment should be gone too
        const orphanedAssessment = await prisma.assessment.findUnique({
          where: { id: assessment.id },
        });
        
        expect(orphanedAssessment).toBeNull();
        console.log('✅ Cascade delete working correctly');
        
      } catch (error) {
        // If deletion failed due to foreign key constraint, clean up properly
        await prisma.assessment.delete({
          where: { id: assessment.id },
        });
        
        await prisma.user.delete({
          where: { id: user.id },
        });
        
        console.log('✅ Foreign key constraints working correctly');
      }
    });
  });
});
