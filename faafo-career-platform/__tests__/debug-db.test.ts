/**
 * Debug Database Test
 * Minimal test to debug Prisma client issues
 */

// Explicitly unmock Prisma
jest.unmock('@prisma/client');

const { PrismaClient } = require('@prisma/client');
import bcrypt from 'bcryptjs';

describe('Debug Database Test', () => {
  let prisma: any;

  beforeAll(async () => {
    console.log('🔍 Creating Prisma client...');
    
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
      log: ['query', 'info', 'warn', 'error'],
    });
    
    console.log('🔍 Connecting to database...');
    await prisma.$connect();
    console.log('✅ Database connected');
  });

  afterAll(async () => {
    if (prisma) {
      await prisma.$disconnect();
      console.log('🔌 Database disconnected');
    }
  });

  it('should test basic database query', async () => {
    console.log('🔍 Testing basic query...');
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('Query result:', result);
    expect(result).toBeDefined();
  });

  it('should test user count', async () => {
    console.log('🔍 Testing user count...');
    const count = await prisma.user.count();
    console.log('User count:', count);
    expect(typeof count).toBe('number');
  });

  it('should test user creation step by step', async () => {
    console.log('🔍 Testing user creation...');
    
    const hashedPassword = await bcrypt.hash('debugtest123', 10);
    const uniqueEmail = `debugtest-${Date.now()}@example.com`;
    
    console.log('Creating user with data:', {
      email: uniqueEmail,
      password: '[HASHED]',
      name: 'Debug Test User'
    });

    try {
      const user = await prisma.user.create({
        data: {
          email: uniqueEmail,
          password: hashedPassword,
          name: 'Debug Test User',
        },
      });

      console.log('User creation result:', user);
      console.log('User type:', typeof user);
      console.log('User keys:', user ? Object.keys(user) : 'no keys');

      expect(user).toBeDefined();
      expect(user).not.toBeNull();
      expect(typeof user).toBe('object');
      expect(user.id).toBeDefined();
      expect(user.email).toBe(uniqueEmail);

      // Clean up
      if (user && user.id) {
        await prisma.user.delete({
          where: { id: user.id }
        });
        console.log('✅ User cleaned up');
      }

    } catch (error) {
      console.error('❌ User creation failed:', error);
      throw error;
    }
  });
});
