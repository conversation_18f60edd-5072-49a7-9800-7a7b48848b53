/**
 * Simple Database Connection Test
 * Tests basic database connectivity and user creation
 */

// Import Prisma directly to bypass <PERSON><PERSON> mocks
jest.unmock('@prisma/client');
const { PrismaClient } = require('@prisma/client');
import bcrypt from 'bcryptjs';

// Load environment variables
require('dotenv').config();

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: ['error', 'warn'],
});

describe('Simple Database Test', () => {
  beforeAll(async () => {
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  it('should connect to database', async () => {
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    expect(result).toBeDefined();
  });

  it('should create a simple user', async () => {
    const hashedPassword = await bcrypt.hash('testpassword123', 10);
    const uniqueEmail = `simpletest-${Date.now()}@example.com`;

    console.log('Creating user with:', {
      email: uniqueEmail,
      password: '[HASHED]',
      name: 'Simple Test User'
    });

    try {
      const user = await prisma.user.create({
        data: {
          email: uniqueEmail,
          password: hashedPassword,
          name: 'Simple Test User',
        },
      });

      console.log('Created user:', {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt
      });

      expect(user).toBeDefined();
      expect(user.id).toBeDefined();
      expect(user.email).toBe(uniqueEmail);
      expect(user.name).toBe('Simple Test User');

      // Clean up
      await prisma.user.delete({
        where: { id: user.id }
      });

    } catch (error) {
      console.error('User creation failed:', error);
      throw error;
    }
  });

  it('should count existing users', async () => {
    const count = await prisma.user.count();
    console.log('Total users in database:', count);
    expect(count).toBeGreaterThanOrEqual(0);
  });

  it('should list career paths', async () => {
    const careerPaths = await prisma.careerPath.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        slug: true,
        isActive: true
      }
    });

    console.log('Career paths found:', careerPaths.length);
    careerPaths.forEach(path => {
      console.log(`- ${path.name} (${path.slug}) - Active: ${path.isActive}`);
    });

    expect(careerPaths).toBeDefined();
  });
});
