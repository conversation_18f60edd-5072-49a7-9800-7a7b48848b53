/**
 * Comprehensive Tests for Forum Posts API
 * Tests CRUD operations, pagination, security, and edge cases
 */

import { NextRequest, NextResponse } from 'next/server';
import { GET, POST } from '@/app/api/forum/posts/route';
import { getServerSession } from 'next-auth/next';
import prisma from '@/lib/prisma';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/lib/prisma', () => ({
  forumPost: {
    findMany: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
  },
  forumCategory: {
    findUnique: jest.fn(),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Helper function to create mock request
const createMockRequest = (url: string, options: RequestInit = {}) => {
  return new NextRequest(url, options);
};

describe('Forum Posts API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/forum/posts', () => {
    const mockPosts = [
      {
        id: 'post-1',
        title: 'Career Advice Needed',
        content: 'Looking for advice on career transition',
        authorId: 'user-1',
        categoryId: 'category-1',
        isHidden: false,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        author: {
          id: 'user-1',
          name: 'John Doe',
          profile: {
            profilePictureUrl: 'https://example.com/avatar.jpg'
          }
        },
        category: {
          id: 'category-1',
          name: 'Career Advice',
          slug: 'career-advice'
        },
        _count: {
          replies: 5,
          likes: 10
        }
      },
      {
        id: 'post-2',
        title: 'Tech Industry Insights',
        content: 'Sharing insights about tech industry',
        authorId: 'user-2',
        categoryId: 'category-2',
        isHidden: false,
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
        author: {
          id: 'user-2',
          name: 'Jane Smith',
          profile: {
            profilePictureUrl: null
          }
        },
        category: {
          id: 'category-2',
          name: 'Industry News',
          slug: 'industry-news'
        },
        _count: {
          replies: 3,
          likes: 8
        }
      }
    ];

    beforeEach(() => {
      mockPrisma.forumPost.findMany.mockResolvedValue(mockPosts);
      mockPrisma.forumPost.count.mockResolvedValue(2);
    });

    it('should retrieve forum posts successfully', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.posts).toHaveLength(2);
      expect(data.posts[0]).toMatchObject({
        id: 'post-1',
        title: 'Career Advice Needed',
        author: expect.objectContaining({
          name: 'John Doe'
        })
      });
    });

    it('should handle pagination correctly', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts?page=2&limit=10');
      await GET(request);

      expect(mockPrisma.forumPost.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 10,
          take: 10
        })
      );
    });

    it('should filter by category', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts?categoryId=category-1');
      await GET(request);

      expect(mockPrisma.forumPost.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            categoryId: 'category-1'
          })
        })
      );
    });

    it('should return pagination metadata', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts?page=1&limit=20');
      const response = await GET(request);
      const data = await response.json();

      expect(data.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 2,
        pages: 1
      });
    });

    it('should handle invalid pagination parameters', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts?page=invalid&limit=abc');
      const response = await GET(request);

      expect(response.status).toBe(200);
      // Should default to page 1, limit 20
      expect(mockPrisma.forumPost.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 0,
          take: 20
        })
      );
    });

    it('should exclude hidden posts', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts');
      await GET(request);

      expect(mockPrisma.forumPost.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            isHidden: false
          })
        })
      );
    });

    it('should handle database errors gracefully', async () => {
      mockPrisma.forumPost.findMany.mockRejectedValue(new Error('Database connection failed'));

      const request = createMockRequest('http://localhost:3000/api/forum/posts');
      const response = await GET(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Failed to fetch forum posts');
    });

    it('should handle empty results', async () => {
      mockPrisma.forumPost.findMany.mockResolvedValue([]);
      mockPrisma.forumPost.count.mockResolvedValue(0);

      const request = createMockRequest('http://localhost:3000/api/forum/posts');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.posts).toHaveLength(0);
      expect(data.pagination.total).toBe(0);
    });

    it('should handle large page numbers', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts?page=999999');
      const response = await GET(request);

      expect(response.status).toBe(200);
      // Should handle gracefully without errors
    });
  });

  describe('POST /api/forum/posts', () => {
    const mockSession = {
      user: {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Test User'
      },
      expires: '2024-12-31'
    };

    const validPostData = {
      title: 'New Career Question',
      content: 'I need advice on changing careers',
      categoryId: 'category-1'
    };

    beforeEach(() => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.forumCategory.findUnique.mockResolvedValue({
        id: 'category-1',
        name: 'Career Advice',
        slug: 'career-advice'
      });
      mockPrisma.forumPost.create.mockResolvedValue({
        id: 'new-post-id',
        ...validPostData,
        authorId: 'user-1',
        isHidden: false,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });

    it('should create a new forum post successfully', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validPostData)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.post).toMatchObject({
        id: 'new-post-id',
        title: 'New Career Question',
        content: 'I need advice on changing careers'
      });
    });

    it('should require authentication', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validPostData)
      });

      const response = await POST(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });

    it('should validate required fields', async () => {
      const invalidData = {
        title: '',
        content: '',
        categoryId: ''
      };

      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Title, content, and category are required');
    });

    it('should validate category exists', async () => {
      mockPrisma.forumCategory.findUnique.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validPostData)
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Invalid category');
    });

    it('should sanitize input data', async () => {
      const maliciousData = {
        title: '<script>alert("xss")</script>Career Question',
        content: '<img src=x onerror=alert(1)>Need advice',
        categoryId: 'category-1'
      };

      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(maliciousData)
      });

      await POST(request);

      expect(mockPrisma.forumPost.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            title: expect.not.stringContaining('<script>'),
            content: expect.not.stringContaining('<img')
          })
        })
      );
    });

    it('should handle database creation errors', async () => {
      mockPrisma.forumPost.create.mockRejectedValue(new Error('Database error'));

      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validPostData)
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Failed to create forum post');
    });

    it('should handle malformed JSON', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json'
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it('should validate title length', async () => {
      const longTitleData = {
        ...validPostData,
        title: 'a'.repeat(1000)
      };

      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(longTitleData)
      });

      const response = await POST(request);

      // Should either truncate or reject
      expect([200, 201, 400]).toContain(response.status);
    });

    it('should validate content length', async () => {
      const longContentData = {
        ...validPostData,
        content: 'a'.repeat(50000)
      };

      const request = createMockRequest('http://localhost:3000/api/forum/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(longContentData)
      });

      const response = await POST(request);

      // Should either truncate or reject
      expect([200, 201, 400]).toContain(response.status);
    });
  });

  describe('Security and Edge Cases', () => {
    it('should handle SQL injection attempts', async () => {
      const request = createMockRequest("http://localhost:3000/api/forum/posts?categoryId='; DROP TABLE forumPost; --");
      const response = await GET(request);

      expect(response.status).toBe(200);
      // Prisma should handle SQL injection protection
    });

    it('should handle concurrent requests', async () => {
      mockPrisma.forumPost.findMany.mockResolvedValue([]);
      mockPrisma.forumPost.count.mockResolvedValue(0);

      const requests = Array(10).fill(null).map(() =>
        GET(createMockRequest('http://localhost:3000/api/forum/posts'))
      );

      const responses = await Promise.all(requests);

      expect(responses).toHaveLength(10);
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    it('should handle very large result sets', async () => {
      const largeMockPosts = Array(10000).fill(null).map((_, index) => ({
        id: `post-${index}`,
        title: `Post ${index}`,
        content: `Content ${index}`,
        authorId: `user-${index}`,
        categoryId: 'category-1',
        isHidden: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        author: { id: `user-${index}`, name: `User ${index}`, profile: null },
        category: { id: 'category-1', name: 'Category', slug: 'category' },
        _count: { replies: 0, likes: 0 }
      }));

      mockPrisma.forumPost.findMany.mockResolvedValue(largeMockPosts);
      mockPrisma.forumPost.count.mockResolvedValue(10000);

      const request = createMockRequest('http://localhost:3000/api/forum/posts');
      const response = await GET(request);

      expect(response.status).toBe(200);
      // Should handle large datasets without timeout
    });

    it('should handle database connection timeouts', async () => {
      mockPrisma.forumPost.findMany.mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Connection timeout')), 100)
        )
      );

      const request = createMockRequest('http://localhost:3000/api/forum/posts');
      const response = await GET(request);

      expect(response.status).toBe(500);
    });

    it('should validate UUID format for categoryId', async () => {
      const request = createMockRequest('http://localhost:3000/api/forum/posts?categoryId=invalid-uuid');
      const response = await GET(request);

      // Should handle gracefully
      expect(response.status).toBe(200);
    });
  });

  describe('Performance Testing', () => {
    it('should complete requests within reasonable time', async () => {
      mockPrisma.forumPost.findMany.mockResolvedValue([]);
      mockPrisma.forumPost.count.mockResolvedValue(0);

      const startTime = Date.now();
      const request = createMockRequest('http://localhost:3000/api/forum/posts');
      await GET(request);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle high load scenarios', async () => {
      mockPrisma.forumPost.findMany.mockResolvedValue([]);
      mockPrisma.forumPost.count.mockResolvedValue(0);

      const promises = Array(100).fill(null).map(() =>
        GET(createMockRequest('http://localhost:3000/api/forum/posts'))
      );

      const startTime = Date.now();
      const responses = await Promise.all(promises);
      const endTime = Date.now();

      expect(responses).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(10000); // Should complete within 10 seconds
    });
  });
});
