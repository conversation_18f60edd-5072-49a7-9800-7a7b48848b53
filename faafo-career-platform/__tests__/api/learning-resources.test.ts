/**
 * Comprehensive Tests for Learning Resources API
 * Tests CRUD operations, filtering, search, and resource management
 */

import { NextRequest } from 'next/server';
import { GET, POST } from '@/app/api/learning-resources/route';
import { GET as getById, PUT as updateById } from '@/app/api/learning-resources/[id]/route';
import prisma from '@/lib/prisma';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  learningResource: {
    findMany: jest.fn(),
    count: jest.fn(),
    create: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Helper function to create mock request
const createMockRequest = (url: string, options: RequestInit = {}) => {
  return new NextRequest(url, options);
};

describe('Learning Resources API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/learning-resources', () => {
    const mockResources = [
      {
        id: 'resource-1',
        title: 'JavaScript Fundamentals',
        description: 'Learn the basics of JavaScript programming',
        url: 'https://example.com/js-fundamentals',
        type: 'COURSE',
        category: 'PROGRAMMING',
        skillLevel: 'BEGINNER',
        author: 'John Doe',
        duration: 120,
        cost: 'FREE',
        format: 'VIDEO',
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        careerPaths: [
          { id: 'path-1', name: 'Web Developer', slug: 'web-developer' }
        ],
        ratings: [
          { rating: 5 },
          { rating: 4 }
        ],
        skills: [
          { id: 'skill-1', name: 'JavaScript' }
        ],
        averageRating: 4.5,
        ratingCount: 2
      },
      {
        id: 'resource-2',
        title: 'React Advanced Patterns',
        description: 'Advanced React development patterns',
        url: 'https://example.com/react-advanced',
        type: 'COURSE',
        category: 'PROGRAMMING',
        skillLevel: 'ADVANCED',
        author: 'Jane Smith',
        duration: 180,
        cost: 'PAID',
        format: 'VIDEO',
        isActive: true,
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
        careerPaths: [
          { id: 'path-1', name: 'Web Developer', slug: 'web-developer' }
        ],
        ratings: [
          { rating: 5 }
        ],
        skills: [
          { id: 'skill-2', name: 'React' }
        ],
        averageRating: 5.0,
        ratingCount: 1
      }
    ];

    beforeEach(() => {
      mockPrisma.learningResource.findMany.mockResolvedValue(mockResources);
      mockPrisma.learningResource.count.mockResolvedValue(2);
    });

    it('should retrieve learning resources successfully', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.resources).toHaveLength(2);
      expect(data.data.resources[0]).toMatchObject({
        id: 'resource-1',
        title: 'JavaScript Fundamentals',
        type: 'COURSE'
      });
    });

    it('should handle pagination correctly', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources?page=2&limit=10');
      await GET(request);

      expect(mockPrisma.learningResource.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 10,
          take: 10
        })
      );
    });

    it('should filter by category', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources?category=PROGRAMMING');
      await GET(request);

      expect(mockPrisma.learningResource.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            category: 'PROGRAMMING'
          })
        })
      );
    });

    it('should filter by skill level', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources?skillLevel=BEGINNER');
      await GET(request);

      expect(mockPrisma.learningResource.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            skillLevel: 'BEGINNER'
          })
        })
      );
    });

    it('should filter by type', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources?type=COURSE');
      await GET(request);

      expect(mockPrisma.learningResource.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            type: 'COURSE'
          })
        })
      );
    });

    it('should search by title and description', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources?search=JavaScript');
      await GET(request);

      expect(mockPrisma.learningResource.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({
                title: expect.objectContaining({
                  contains: 'JavaScript'
                })
              }),
              expect.objectContaining({
                description: expect.objectContaining({
                  contains: 'JavaScript'
                })
              })
            ])
          })
        })
      );
    });

    it('should sort by different criteria', async () => {
      const sortTests = [
        { sortBy: 'title', sortOrder: 'asc' },
        { sortBy: 'createdAt', sortOrder: 'desc' },
        { sortBy: 'averageRating', sortOrder: 'desc' }
      ];

      for (const { sortBy, sortOrder } of sortTests) {
        const request = createMockRequest(
          `http://localhost:3000/api/learning-resources?sortBy=${sortBy}&sortOrder=${sortOrder}`
        );
        await GET(request);

        expect(mockPrisma.learningResource.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            orderBy: { [sortBy]: sortOrder }
          })
        );
      }
    });

    it('should handle multiple filters simultaneously', async () => {
      const request = createMockRequest(
        'http://localhost:3000/api/learning-resources?category=PROGRAMMING&skillLevel=BEGINNER&type=COURSE&search=JavaScript'
      );
      await GET(request);

      expect(mockPrisma.learningResource.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            category: 'PROGRAMMING',
            skillLevel: 'BEGINNER',
            type: 'COURSE',
            OR: expect.any(Array)
          })
        })
      );
    });

    it('should return pagination metadata', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources?page=1&limit=20');
      const response = await GET(request);
      const data = await response.json();

      expect(data.data.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 2,
        pages: 1
      });
    });

    it('should handle database errors gracefully', async () => {
      mockPrisma.learningResource.findMany.mockRejectedValue(new Error('Database connection failed'));

      const request = createMockRequest('http://localhost:3000/api/learning-resources');
      const response = await GET(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to fetch learning resources');
    });

    it('should handle empty results', async () => {
      mockPrisma.learningResource.findMany.mockResolvedValue([]);
      mockPrisma.learningResource.count.mockResolvedValue(0);

      const request = createMockRequest('http://localhost:3000/api/learning-resources');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.resources).toHaveLength(0);
      expect(data.data.pagination.total).toBe(0);
    });

    it('should validate and sanitize search input', async () => {
      const maliciousSearch = '<script>alert("xss")</script>';
      const request = createMockRequest(
        `http://localhost:3000/api/learning-resources?search=${encodeURIComponent(maliciousSearch)}`
      );
      
      const response = await GET(request);
      expect(response.status).toBe(200);
      // Should handle malicious input gracefully
    });
  });

  describe('POST /api/learning-resources', () => {
    const validResourceData = {
      title: 'New JavaScript Course',
      description: 'A comprehensive JavaScript course',
      url: 'https://example.com/new-course',
      type: 'COURSE',
      category: 'PROGRAMMING',
      skillLevel: 'INTERMEDIATE',
      author: 'Expert Teacher',
      duration: 150,
      cost: 'FREE',
      format: 'VIDEO'
    };

    beforeEach(() => {
      mockPrisma.learningResource.create.mockResolvedValue({
        id: 'new-resource-id',
        ...validResourceData,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });

    it('should create a new learning resource successfully', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validResourceData)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data).toMatchObject({
        id: 'new-resource-id',
        title: 'New JavaScript Course'
      });
    });

    it('should validate required fields', async () => {
      const invalidData = {
        title: '',
        description: '',
        url: '',
        type: '',
        category: '',
        skillLevel: '',
        format: ''
      };

      const request = createMockRequest('http://localhost:3000/api/learning-resources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Missing required fields');
    });

    it('should normalize enum values to uppercase', async () => {
      const dataWithLowercase = {
        ...validResourceData,
        type: 'course',
        category: 'programming',
        skillLevel: 'intermediate',
        cost: 'free',
        format: 'video'
      };

      const request = createMockRequest('http://localhost:3000/api/learning-resources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataWithLowercase)
      });

      await POST(request);

      expect(mockPrisma.learningResource.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          type: 'COURSE',
          category: 'PROGRAMMING',
          skillLevel: 'INTERMEDIATE',
          cost: 'FREE',
          format: 'VIDEO'
        })
      });
    });

    it('should handle database creation errors', async () => {
      mockPrisma.learningResource.create.mockRejectedValue(new Error('Database error'));

      const request = createMockRequest('http://localhost:3000/api/learning-resources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validResourceData)
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to create learning resource');
    });

    it('should validate URL format', async () => {
      const invalidUrlData = {
        ...validResourceData,
        url: 'not-a-valid-url'
      };

      const request = createMockRequest('http://localhost:3000/api/learning-resources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidUrlData)
      });

      const response = await POST(request);
      
      // Should either accept or reject based on validation
      expect([201, 400]).toContain(response.status);
    });

    it('should sanitize input data', async () => {
      const maliciousData = {
        ...validResourceData,
        title: '<script>alert("xss")</script>Course Title',
        description: '<img src=x onerror=alert(1)>Description',
        author: '<svg onload=alert(1)>Author'
      };

      const request = createMockRequest('http://localhost:3000/api/learning-resources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(maliciousData)
      });

      await POST(request);

      expect(mockPrisma.learningResource.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          title: expect.not.stringContaining('<script>'),
          description: expect.not.stringContaining('<img'),
          author: expect.not.stringContaining('<svg')
        })
      });
    });
  });

  describe('GET /api/learning-resources/[id]', () => {
    const mockResource = {
      id: 'resource-1',
      title: 'JavaScript Fundamentals',
      description: 'Learn the basics of JavaScript',
      url: 'https://example.com/js-fundamentals',
      type: 'COURSE',
      category: 'PROGRAMMING',
      skillLevel: 'BEGINNER',
      author: 'John Doe',
      duration: 120,
      cost: 'FREE',
      format: 'VIDEO',
      isActive: true,
      careerPaths: [],
      ratings: [],
      skills: []
    };

    it('should retrieve a specific resource successfully', async () => {
      mockPrisma.learningResource.findUnique.mockResolvedValue(mockResource);

      const request = createMockRequest('http://localhost:3000/api/learning-resources/resource-1');
      const response = await getById(request, { params: Promise.resolve({ id: 'resource-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toMatchObject({
        id: 'resource-1',
        title: 'JavaScript Fundamentals'
      });
    });

    it('should return 404 for non-existent resource', async () => {
      mockPrisma.learningResource.findUnique.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/learning-resources/non-existent');
      const response = await getById(request, { params: Promise.resolve({ id: 'non-existent' }) });

      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Resource not found');
    });

    it('should validate resource ID parameter', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources/');
      const response = await getById(request, { params: Promise.resolve({ id: '' }) });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Resource ID is required');
    });
  });

  describe('PUT /api/learning-resources/[id]', () => {
    const updateData = {
      title: 'Updated Course Title',
      description: 'Updated description',
      skillLevel: 'ADVANCED'
    };

    const existingResource = {
      id: 'resource-1',
      title: 'Original Title',
      description: 'Original description',
      url: 'https://example.com/course',
      type: 'COURSE',
      category: 'PROGRAMMING',
      skillLevel: 'BEGINNER',
      author: 'Author',
      duration: 120,
      cost: 'FREE',
      format: 'VIDEO',
      isActive: true
    };

    beforeEach(() => {
      mockPrisma.learningResource.findUnique.mockResolvedValue(existingResource);
      mockPrisma.learningResource.update.mockResolvedValue({
        ...existingResource,
        ...updateData,
        updatedAt: new Date()
      });
    });

    it('should update a resource successfully', async () => {
      const request = createMockRequest('http://localhost:3000/api/learning-resources/resource-1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      const response = await updateById(request, { params: Promise.resolve({ id: 'resource-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.title).toBe('Updated Course Title');
    });

    it('should return 404 for non-existent resource', async () => {
      mockPrisma.learningResource.findUnique.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/learning-resources/non-existent', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      });

      const response = await updateById(request, { params: Promise.resolve({ id: 'non-existent' }) });

      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Resource not found');
    });

    it('should handle partial updates', async () => {
      const partialUpdate = { title: 'New Title Only' };

      const request = createMockRequest('http://localhost:3000/api/learning-resources/resource-1', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(partialUpdate)
      });

      await updateById(request, { params: Promise.resolve({ id: 'resource-1' }) });

      expect(mockPrisma.learningResource.update).toHaveBeenCalledWith({
        where: { id: 'resource-1' },
        data: expect.objectContaining({
          title: 'New Title Only'
        })
      });
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should handle large result sets efficiently', async () => {
      const largeResourceSet = Array(1000).fill(null).map((_, index) => ({
        id: `resource-${index}`,
        title: `Resource ${index}`,
        description: `Description ${index}`,
        url: `https://example.com/resource-${index}`,
        type: 'COURSE',
        category: 'PROGRAMMING',
        skillLevel: 'BEGINNER',
        author: 'Author',
        duration: 120,
        cost: 'FREE',
        format: 'VIDEO',
        isActive: true,
        careerPaths: [],
        ratings: [],
        skills: [],
        averageRating: 4.0,
        ratingCount: 1
      }));

      mockPrisma.learningResource.findMany.mockResolvedValue(largeResourceSet);
      mockPrisma.learningResource.count.mockResolvedValue(1000);

      const request = createMockRequest('http://localhost:3000/api/learning-resources');
      const response = await GET(request);

      expect(response.status).toBe(200);
      // Should handle large datasets without timeout
    });

    it('should handle concurrent requests', async () => {
      mockPrisma.learningResource.findMany.mockResolvedValue([]);
      mockPrisma.learningResource.count.mockResolvedValue(0);

      const requests = Array(20).fill(null).map(() =>
        GET(createMockRequest('http://localhost:3000/api/learning-resources'))
      );

      const responses = await Promise.all(requests);

      expect(responses).toHaveLength(20);
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    it('should handle database connection timeouts', async () => {
      mockPrisma.learningResource.findMany.mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Connection timeout')), 100)
        )
      );

      const request = createMockRequest('http://localhost:3000/api/learning-resources');
      const response = await GET(request);

      expect(response.status).toBe(500);
    });
  });
});
