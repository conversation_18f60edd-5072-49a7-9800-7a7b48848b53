/**
 * Comprehensive Tests for AI Skills Analysis API
 * Tests AI-powered skills gap analysis, recommendations, and error handling
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/ai/skills-analysis/route';
import { getServerSession } from 'next-auth/next';
import { geminiService } from '@/lib/services/geminiService';
import prisma from '@/lib/prisma';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/lib/services/geminiService');
jest.mock('@/lib/prisma', () => ({
  user: {
    findUnique: jest.fn(),
  },
  userProgress: {
    findMany: jest.fn(),
  },
  careerPath: {
    findFirst: jest.fn(),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockGeminiService = geminiService as jest.Mocked<typeof geminiService>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

// Helper function to create mock request
const createMockRequest = (url: string, options: RequestInit = {}) => {
  return new NextRequest(url, options);
};

describe('AI Skills Analysis API', () => {
  const mockSession = {
    user: {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Test User'
    },
    expires: '2024-12-31'
  };

  const validAnalysisData = {
    currentSkills: ['javascript', 'react', 'html', 'css'],
    targetCareerPath: 'Full-Stack Developer',
    experienceLevel: 'intermediate'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetServerSession.mockResolvedValue(mockSession);
    
    // Mock user data
    mockPrisma.user.findUnique.mockResolvedValue({
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Test User'
    });

    // Mock user progress data
    mockPrisma.userProgress.findMany.mockResolvedValue([
      {
        id: 'progress-1',
        userId: 'user-1',
        resourceId: 'resource-1',
        status: 'COMPLETED',
        resource: {
          skills: [
            { id: 'skill-1', name: 'Node.js' },
            { id: 'skill-2', name: 'Express.js' }
          ]
        }
      }
    ]);

    // Mock career path data
    mockPrisma.careerPath.findFirst.mockResolvedValue({
      id: 'career-1',
      name: 'Full-Stack Developer',
      description: 'Develop full-stack applications',
      requiredSkills: ['javascript', 'react', 'nodejs', 'databases', 'apis']
    });

    // Mock successful AI analysis
    mockGeminiService.analyzeSkillsGap.mockResolvedValue({
      success: true,
      data: {
        skillsGapAnalysis: {
          criticalGaps: ['nodejs', 'databases'],
          minorGaps: ['typescript', 'testing'],
          strengths: ['javascript', 'react'],
          recommendations: [
            'Learn Node.js for backend development',
            'Study database design and SQL',
            'Practice with TypeScript for better code quality'
          ],
          timeToFill: '3-6 months',
          confidence: 0.85
        },
        learningPath: [
          {
            skill: 'nodejs',
            priority: 'high',
            estimatedTime: '4-6 weeks',
            resources: ['Node.js Official Tutorial', 'Express.js Course']
          },
          {
            skill: 'databases',
            priority: 'high',
            estimatedTime: '6-8 weeks',
            resources: ['SQL Fundamentals', 'Database Design Course']
          }
        ],
        careerReadiness: {
          currentLevel: 65,
          targetLevel: 90,
          readinessScore: 'intermediate',
          nextSteps: ['Complete backend development skills', 'Build full-stack projects']
        }
      }
    });
  });

  describe('POST /api/ai/skills-analysis', () => {
    it('should perform skills gap analysis successfully', async () => {
      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.skillsGapAnalysis).toBeDefined();
      expect(data.data.learningPath).toBeDefined();
      expect(data.data.careerReadiness).toBeDefined();
    });

    it('should require authentication', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Authentication required');
    });

    it('should validate required fields', async () => {
      const invalidData = {
        currentSkills: [],
        targetCareerPath: '',
        experienceLevel: ''
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidData)
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('required');
    });

    it('should validate experience level enum', async () => {
      const invalidExperienceData = {
        ...validAnalysisData,
        experienceLevel: 'invalid_level'
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidExperienceData)
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('experience level');
    });

    it('should sanitize input skills', async () => {
      const maliciousData = {
        currentSkills: ['<script>alert("xss")</script>', 'react', 'javascript'],
        targetCareerPath: '<img src=x onerror=alert(1)>Developer',
        experienceLevel: 'intermediate'
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(maliciousData)
      });

      await POST(request);

      expect(mockGeminiService.analyzeSkillsGap).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.not.stringContaining('<script>'),
          'react',
          'javascript'
        ]),
        expect.not.stringContaining('<img'),
        'intermediate',
        'user-1'
      );
    });

    it('should combine user skills from progress data', async () => {
      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      await POST(request);

      expect(mockGeminiService.analyzeSkillsGap).toHaveBeenCalledWith(
        expect.arrayContaining(['javascript', 'react', 'html', 'css', 'Node.js', 'Express.js']),
        'Full-Stack Developer',
        'intermediate',
        'user-1'
      );
    });

    it('should handle AI service failures gracefully', async () => {
      mockGeminiService.analyzeSkillsGap.mockResolvedValue({
        success: false,
        error: 'AI service temporarily unavailable'
      });

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('AI service temporarily unavailable');
    });

    it('should handle database errors gracefully', async () => {
      mockPrisma.user.findUnique.mockRejectedValue(new Error('Database connection failed'));

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Failed to analyze skills gap');
    });

    it('should handle missing career path data', async () => {
      mockPrisma.careerPath.findFirst.mockResolvedValue(null);

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);

      // Should still proceed with analysis
      expect(response.status).toBe(200);
      expect(mockGeminiService.analyzeSkillsGap).toHaveBeenCalled();
    });

    it('should handle empty user progress data', async () => {
      mockPrisma.userProgress.findMany.mockResolvedValue([]);

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
      expect(mockGeminiService.analyzeSkillsGap).toHaveBeenCalledWith(
        validAnalysisData.currentSkills,
        validAnalysisData.targetCareerPath,
        validAnalysisData.experienceLevel,
        'user-1'
      );
    });

    it('should validate skills array length', async () => {
      const tooManySkillsData = {
        ...validAnalysisData,
        currentSkills: Array(100).fill('skill')
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tooManySkillsData)
      });

      const response = await POST(request);

      // Should either limit skills or reject
      expect([200, 400]).toContain(response.status);
    });

    it('should handle malformed JSON', async () => {
      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json'
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
    });

    it('should validate career path name length', async () => {
      const longCareerPathData = {
        ...validAnalysisData,
        targetCareerPath: 'a'.repeat(1000)
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(longCareerPathData)
      });

      const response = await POST(request);

      // Should either truncate or reject
      expect([200, 400]).toContain(response.status);
    });
  });

  describe('AI Analysis Quality and Validation', () => {
    it('should validate AI response structure', async () => {
      // Mock incomplete AI response
      mockGeminiService.analyzeSkillsGap.mockResolvedValue({
        success: true,
        data: {
          skillsGapAnalysis: {
            criticalGaps: ['nodejs'],
            // Missing other required fields
          }
        }
      });

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);

      // Should handle incomplete responses gracefully
      expect(response.status).toBe(200);
    });

    it('should validate confidence scores', async () => {
      mockGeminiService.analyzeSkillsGap.mockResolvedValue({
        success: true,
        data: {
          skillsGapAnalysis: {
            criticalGaps: ['nodejs'],
            minorGaps: [],
            strengths: ['javascript'],
            recommendations: ['Learn Node.js'],
            timeToFill: '4 weeks',
            confidence: 1.5 // Invalid confidence score
          },
          learningPath: [],
          careerReadiness: {
            currentLevel: 65,
            targetLevel: 90,
            readinessScore: 'intermediate',
            nextSteps: []
          }
        }
      });

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      // Should normalize or handle invalid confidence scores
      if (data.data.skillsGapAnalysis.confidence) {
        expect(data.data.skillsGapAnalysis.confidence).toBeLessThanOrEqual(1);
        expect(data.data.skillsGapAnalysis.confidence).toBeGreaterThanOrEqual(0);
      }
    });

    it('should handle AI timeout scenarios', async () => {
      mockGeminiService.analyzeSkillsGap.mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI request timeout')), 100)
        )
      );

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
    });
  });

  describe('Performance and Edge Cases', () => {
    it('should complete analysis within reasonable time', async () => {
      const startTime = Date.now();
      
      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validAnalysisData)
      });

      await POST(request);
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent analysis requests', async () => {
      const requests = Array(5).fill(null).map(() =>
        POST(createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(validAnalysisData)
        }))
      );

      const responses = await Promise.all(requests);

      expect(responses).toHaveLength(5);
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    it('should handle very large skill lists', async () => {
      const largeSkillsData = {
        ...validAnalysisData,
        currentSkills: Array(50).fill(null).map((_, i) => `skill-${i}`)
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(largeSkillsData)
      });

      const response = await POST(request);

      // Should handle large skill lists gracefully
      expect([200, 400]).toContain(response.status);
    });

    it('should handle special characters in skills', async () => {
      const specialCharSkillsData = {
        ...validAnalysisData,
        currentSkills: ['C++', 'C#', '.NET', 'Node.js', 'Vue.js', 'React.js']
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(specialCharSkillsData)
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
    });

    it('should handle Unicode characters in career path', async () => {
      const unicodeCareerData = {
        ...validAnalysisData,
        targetCareerPath: 'Développeur Full-Stack 开发者'
      };

      const request = createMockRequest('http://localhost:3000/api/ai/skills-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(unicodeCareerData)
      });

      const response = await POST(request);

      expect(response.status).toBe(200);
    });
  });
});
