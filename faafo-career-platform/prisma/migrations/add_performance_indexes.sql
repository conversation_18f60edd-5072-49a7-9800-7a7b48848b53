-- Performance Indexes for FAAFO Career Platform
-- Optimized indexes for common query patterns and performance improvements

-- User table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_email_active ON "User" (email) WHERE "lockedUntil" IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_role ON "User" (role);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_created_at ON "User" (created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_updated_at ON "User" (updated_at);

-- Profile table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profile_user_id ON "Profile" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profile_experience_level ON "Profile" (experience_level);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profile_current_industry ON "Profile" (current_industry);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profile_target_industry ON "Profile" (target_industry);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profile_last_active ON "Profile" (last_active_at);

-- Assessment table indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assessment_user_status ON "Assessment" (user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assessment_created_at ON "Assessment" (created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assessment_completed_at ON "Assessment" (completed_at) WHERE completed_at IS NOT NULL;

-- Assessment Response indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assessment_response_assessment_id ON "AssessmentResponse" (assessment_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assessment_response_question_key ON "AssessmentResponse" (question_key);

-- Career Path indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_career_path_active ON "CareerPath" (is_active) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_career_path_slug ON "CareerPath" (slug);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_career_path_name ON "CareerPath" (name);

-- Learning Resource indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_resource_active ON "LearningResource" (is_active) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_resource_type_category ON "LearningResource" (type, category);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_resource_skill_level ON "LearningResource" (skill_level);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_resource_cost ON "LearningResource" (cost);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_resource_created_at ON "LearningResource" (created_at);

-- Learning Path indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_active ON "LearningPath" (is_active) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_difficulty ON "LearningPath" (difficulty);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_category ON "LearningPath" (category);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_slug ON "LearningPath" (slug);

-- Learning Path Step indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_step_path_order ON "LearningPathStep" (learning_path_id, step_order);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_step_resource ON "LearningPathStep" (resource_id) WHERE resource_id IS NOT NULL;

-- User Learning Progress indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_progress_user_status ON "UserLearningProgress" (user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_progress_resource ON "UserLearningProgress" (resource_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_progress_completed ON "UserLearningProgress" (completed_at) WHERE completed_at IS NOT NULL;

-- User Learning Path indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_path_user_status ON "UserLearningPath" (user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_path_path ON "UserLearningPath" (learning_path_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_path_progress ON "UserLearningPath" (progress_percentage);

-- User Learning Path Progress indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_path_progress_user_path ON "UserLearningPathProgress" (user_id, learning_path_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_path_progress_step ON "UserLearningPathProgress" (step_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_path_progress_completed ON "UserLearningPathProgress" (completed_at) WHERE completed_at IS NOT NULL;

-- Forum Post indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_author_created ON "ForumPost" (author_id, created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_category_visible ON "ForumPost" (category_id, is_hidden) WHERE is_hidden = false;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_pinned ON "ForumPost" (is_pinned) WHERE is_pinned = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_view_count ON "ForumPost" (view_count);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_like_count ON "ForumPost" (like_count);

-- Forum Reply indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_reply_post_created ON "ForumReply" (post_id, created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_reply_author ON "ForumReply" (author_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_reply_visible ON "ForumReply" (is_hidden) WHERE is_hidden = false;

-- Forum Category indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_category_active ON "ForumCategory" (is_active) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_category_parent ON "ForumCategory" (parent_id) WHERE parent_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_category_sort_order ON "ForumCategory" (sort_order);

-- Resource Rating indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_resource_rating_resource ON "ResourceRating" (resource_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_resource_rating_user ON "ResourceRating" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_resource_rating_rating ON "ResourceRating" (rating);

-- User Goal indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_goal_user_status ON "UserGoal" (user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_goal_type_category ON "UserGoal" (type, category);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_goal_target_date ON "UserGoal" (target_date) WHERE target_date IS NOT NULL;

-- User Achievement indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievement_user ON "UserAchievement" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievement_achievement ON "UserAchievement" (achievement_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievement_unlocked ON "UserAchievement" (unlocked_at);

-- Skill indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_skill_name ON "Skill" (name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_skill_category ON "Skill" (category) WHERE category IS NOT NULL;

-- User Skill Progress indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_skill_progress_user ON "UserSkillProgress" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_skill_progress_skill ON "UserSkillProgress" (skill_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_skill_progress_level ON "UserSkillProgress" (current_level);

-- Session indexes for authentication
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_session_user_id ON "Session" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_session_expires ON "Session" (expires);

-- Account indexes for authentication
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_account_user_id ON "Account" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_account_provider ON "Account" (provider, provider_account_id);

-- Suggestion Rule indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_suggestion_rule_career_path ON "SuggestionRule" (career_path_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_suggestion_rule_question ON "SuggestionRule" (question_key);

-- Forum Bookmark indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_bookmark_user ON "ForumBookmark" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_bookmark_post ON "ForumBookmark" (post_id);

-- Career Path Bookmark indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_career_path_bookmark_user ON "CareerPathBookmark" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_career_path_bookmark_career_path ON "CareerPathBookmark" (career_path_id);

-- Forum Reaction indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_reaction_post ON "ForumPostReaction" (post_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_reaction_user ON "ForumPostReaction" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_reply_reaction_reply ON "ForumReplyReaction" (reply_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_reply_reaction_user ON "ForumReplyReaction" (user_id);

-- Forum Report indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_report_status ON "ForumReport" (status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_report_created ON "ForumReport" (created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_report_reporter ON "ForumReport" (reporter_id);

-- Forum Moderator indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_moderator_user ON "ForumModerator" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_moderator_category ON "ForumModerator" (category_id) WHERE category_id IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_moderator_active ON "ForumModerator" (is_active) WHERE is_active = true;

-- Learning Analytics indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_analytics_user ON "LearningAnalytics" (user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_analytics_date ON "LearningAnalytics" (date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_analytics_metric ON "LearningAnalytics" (metric_type);

-- Freedom Fund indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_freedom_fund_user ON "FreedomFund" (user_id);

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_learning_progress_composite ON "UserLearningProgress" (user_id, status, completed_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_composite ON "ForumPost" (category_id, is_hidden, is_pinned, created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_resource_composite ON "LearningResource" (is_active, type, category, skill_level);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_goal_composite ON "UserGoal" (user_id, status, type, target_date);

-- Partial indexes for better performance on filtered queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assessment_in_progress ON "Assessment" (user_id, created_at) WHERE status = 'IN_PROGRESS';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_path_active_difficulty ON "LearningPath" (difficulty, estimated_hours) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_recent_active ON "ForumPost" (created_at DESC) WHERE is_hidden = false AND is_locked = false;

-- Text search indexes (if using PostgreSQL full-text search)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_resource_search ON "LearningResource" USING gin(to_tsvector('english', title || ' ' || description)) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forum_post_search ON "ForumPost" USING gin(to_tsvector('english', title || ' ' || content)) WHERE is_hidden = false;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_career_path_search ON "CareerPath" USING gin(to_tsvector('english', name || ' ' || overview)) WHERE is_active = true;
