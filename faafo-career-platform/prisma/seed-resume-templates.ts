import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Simplified resume templates - focus on essentials
const resumeTemplates = [
  {
    name: 'Professional',
    description: 'Clean and professional template suitable for most industries',
    category: 'professional',
    isActive: true,
    isPremium: false,
    cssStyles: `
      .resume { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .header { text-align: center; margin-bottom: 2rem; }
      .name { font-size: 2rem; font-weight: bold; margin-bottom: 0.5rem; }
      .contact { font-size: 0.9rem; color: #666; }
      .section { margin-bottom: 1.5rem; }
      .section-title { font-size: 1.2rem; font-weight: bold; border-bottom: 2px solid #333; padding-bottom: 0.25rem; margin-bottom: 1rem; }
    `,
  },
  {
    name: 'Modern',
    description: 'Contemporary design with clean lines and modern typography',
    category: 'modern',
    isActive: true,
    isPremium: false,
    cssStyles: `
      .resume { font-family: 'Helvetica Neue', Arial, sans-serif; line-height: 1.5; color: #2d3748; }
      .header { margin-bottom: 2rem; }
      .name { font-size: 2.5rem; font-weight: 300; margin-bottom: 0.5rem; color: #1a202c; }
      .contact { font-size: 0.9rem; color: #4a5568; }
      .section { margin-bottom: 1.5rem; }
      .section-title { font-size: 1.1rem; font-weight: 600; color: #4299e1; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 1rem; }
    `,
  },
  {
    name: 'Creative',
    description: 'Eye-catching design perfect for creative professionals',
    category: 'creative',
    isActive: true,
    isPremium: true,
    cssStyles: `
      .resume { font-family: Georgia, serif; line-height: 1.7; color: #2d3748; }
      .header { text-align: center; margin-bottom: 2.5rem; border-bottom: 3px solid #ed8936; padding-bottom: 1rem; }
      .name { font-size: 2.8rem; font-weight: normal; margin-bottom: 0.5rem; color: #2d3748; }
      .contact { font-size: 1rem; color: #4a5568; }
      .section { margin-bottom: 2rem; }
      .section-title { font-size: 1.4rem; font-weight: bold; color: #ed8936; margin-bottom: 1rem; }
    `,
  },
  {
    name: 'Executive',
    description: 'Sophisticated template for senior-level positions',
    category: 'executive',
    isActive: true,
    isPremium: true,
    cssStyles: `
      .resume { font-family: 'Times New Roman', serif; line-height: 1.6; color: #1a202c; }
      .header { text-align: center; margin-bottom: 2rem; }
      .name { font-size: 2.2rem; font-weight: bold; margin-bottom: 0.5rem; }
      .contact { font-size: 0.9rem; color: #2d3748; }
      .section { margin-bottom: 2rem; }
      .section-title { font-size: 1.3rem; font-weight: bold; color: #2b6cb0; border-bottom: 1px solid #2b6cb0; padding-bottom: 0.25rem; margin-bottom: 1rem; }
    `,
  },
];

async function seedResumeTemplates() {
  console.log('Seeding resume templates...');

  for (const template of resumeTemplates) {
    // Check if template with this name already exists
    const existingTemplate = await prisma.resumeTemplate.findFirst({
      where: { name: template.name },
    });

    if (!existingTemplate) {
      await prisma.resumeTemplate.create({
        data: template,
      });
      console.log(`✅ Created template: ${template.name}`);
    } else {
      // Update existing template
      await prisma.resumeTemplate.update({
        where: { id: existingTemplate.id },
        data: template,
      });
      console.log(`🔄 Updated template: ${template.name}`);
    }
  }

  console.log('Resume templates seeded successfully!');
}

export { seedResumeTemplates };

// Run if called directly
if (require.main === module) {
  seedResumeTemplates()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
