#!/usr/bin/env node

/**
 * Comprehensive integration test for Resume Builder
 * Tests the complete flow without requiring database
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 COMPREHENSIVE RESUME BUILDER INTEGRATION TEST\n');
console.log('='.repeat(60));

// Test Results Tracking
let totalTests = 0;
let passedTests = 0;
let criticalIssues = [];
let warnings = [];

function runTest(testName, testFunction) {
  totalTests++;
  console.log(`\n🔍 Testing: ${testName}`);
  
  try {
    const result = testFunction();
    if (result.success) {
      passedTests++;
      console.log(`✅ PASS: ${result.message || 'Test passed'}`);
    } else {
      console.log(`❌ FAIL: ${result.message}`);
      if (result.critical) {
        criticalIssues.push(`${testName}: ${result.message}`);
      } else {
        warnings.push(`${testName}: ${result.message}`);
      }
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
    criticalIssues.push(`${testName}: ${error.message}`);
  }
}

// Test 1: Database Schema Integrity
runTest('Database Schema Structure', () => {
  const schemaPath = path.join(__dirname, 'prisma/schema.prisma');
  if (!fs.existsSync(schemaPath)) {
    return { success: false, message: 'Schema file not found', critical: true };
  }
  
  const schema = fs.readFileSync(schemaPath, 'utf8');
  
  // Check for required models
  const requiredModels = [
    'model Resume {',
    'model ResumeExperience {',
    'model ResumeEducation {',
    'model ResumeSkill {',
    'model ResumeProject {',
    'model ResumeTemplate {'
  ];
  
  const missingModels = requiredModels.filter(model => !schema.includes(model));
  
  if (missingModels.length > 0) {
    return { 
      success: false, 
      message: `Missing models: ${missingModels.join(', ')}`, 
      critical: true 
    };
  }
  
  // Check for proper relationships
  const relationships = [
    'user.*User.*@relation',
    'resume.*Resume.*@relation',
    'userId.*String',
    'resumeId.*String'
  ];
  
  const missingRelations = relationships.filter(rel => !new RegExp(rel, 'i').test(schema));
  
  if (missingRelations.length > 0) {
    return { 
      success: false, 
      message: `Missing relationships: ${missingRelations.length}`, 
      critical: false 
    };
  }
  
  return { success: true, message: 'All required models and relationships present' };
});

// Test 2: API Endpoint Completeness
runTest('API Endpoint Structure', () => {
  const apiPaths = [
    'src/app/api/resume-builder/route.ts',
    'src/app/api/resume-builder/[id]/route.ts',
    'src/app/api/resume-builder/[id]/experience/route.ts'
  ];
  
  const missingFiles = apiPaths.filter(path => !fs.existsSync(path.replace('src/app/api/', __dirname + '/src/app/api/')));
  
  if (missingFiles.length > 0) {
    return { 
      success: false, 
      message: `Missing API files: ${missingFiles.join(', ')}`, 
      critical: true 
    };
  }
  
  // Check main API file for required methods
  const mainApiPath = path.join(__dirname, 'src/app/api/resume-builder/route.ts');
  const apiContent = fs.readFileSync(mainApiPath, 'utf8');
  
  const requiredMethods = ['export async function GET', 'export async function POST', 'export async function PUT'];
  const missingMethods = requiredMethods.filter(method => !apiContent.includes(method));
  
  if (missingMethods.length > 0) {
    return { 
      success: false, 
      message: `Missing HTTP methods: ${missingMethods.join(', ')}`, 
      critical: true 
    };
  }
  
  return { success: true, message: 'All API endpoints and methods present' };
});

// Test 3: Security Implementation
runTest('Security Best Practices', () => {
  const apiPath = path.join(__dirname, 'src/app/api/resume-builder/route.ts');
  const apiContent = fs.readFileSync(apiPath, 'utf8');
  
  const securityChecks = [
    { pattern: 'getServerSession', name: 'Authentication check' },
    { pattern: 'session\\?\\.user\\?\\.id', name: 'Safe user access' },
    { pattern: 'safeParse', name: 'Input validation' },
    { pattern: 'trim\\(\\)', name: 'Input sanitization' },
    { pattern: 'AUTH_REQUIRED', name: 'Auth error codes' }
  ];
  
  const failedChecks = securityChecks.filter(check => 
    !new RegExp(check.pattern, 'i').test(apiContent)
  );
  
  if (failedChecks.length > 0) {
    return { 
      success: false, 
      message: `Missing security: ${failedChecks.map(c => c.name).join(', ')}`, 
      critical: true 
    };
  }
  
  return { success: true, message: 'All security checks passed' };
});

// Test 4: Frontend Component Quality
runTest('Frontend Component Structure', () => {
  const mainPagePath = path.join(__dirname, 'src/app/resume-builder/page.tsx');
  const createPagePath = path.join(__dirname, 'src/app/resume-builder/create/page.tsx');
  
  if (!fs.existsSync(mainPagePath) || !fs.existsSync(createPagePath)) {
    return { 
      success: false, 
      message: 'Missing frontend components', 
      critical: true 
    };
  }
  
  const mainContent = fs.readFileSync(mainPagePath, 'utf8');
  const createContent = fs.readFileSync(createPagePath, 'utf8');
  
  // Check for essential React patterns
  const patterns = [
    { content: mainContent, pattern: 'useState', name: 'State management' },
    { content: mainContent, pattern: 'useEffect', name: 'Effect hooks' },
    { content: mainContent, pattern: 'useSession', name: 'Authentication' },
    { content: createContent, pattern: 'validateForm', name: 'Form validation' },
    { content: createContent, pattern: 'handleInputChange', name: 'Input handling' },
    { content: mainContent, pattern: 'toast\\.', name: 'User feedback' }
  ];
  
  const failedPatterns = patterns.filter(p => !new RegExp(p.pattern, 'i').test(p.content));
  
  if (failedPatterns.length > 0) {
    return { 
      success: false, 
      message: `Missing patterns: ${failedPatterns.map(p => p.name).join(', ')}`, 
      critical: false 
    };
  }
  
  return { success: true, message: 'All frontend patterns present' };
});

// Test 5: Error Handling Implementation
runTest('Error Handling Coverage', () => {
  const files = [
    'src/app/api/resume-builder/route.ts',
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let errorHandlingScore = 0;
  let totalChecks = 0;
  
  files.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      const errorPatterns = [
        'try.*catch',
        'error.*message',
        'toast\\.error',
        'console\\.error',
        'throw new Error'
      ];
      
      errorPatterns.forEach(pattern => {
        totalChecks++;
        if (new RegExp(pattern, 'i').test(content)) {
          errorHandlingScore++;
        }
      });
    }
  });
  
  const errorCoverage = (errorHandlingScore / totalChecks) * 100;
  
  if (errorCoverage < 60) {
    return { 
      success: false, 
      message: `Low error handling coverage: ${errorCoverage.toFixed(1)}%`, 
      critical: false 
    };
  }
  
  return { success: true, message: `Good error handling coverage: ${errorCoverage.toFixed(1)}%` };
});

// Test 6: TypeScript Type Safety
runTest('TypeScript Type Definitions', () => {
  const files = [
    'src/app/resume-builder/page.tsx',
    'src/app/resume-builder/create/page.tsx'
  ];
  
  let typeScore = 0;
  let totalTypeChecks = 0;
  
  files.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      const typePatterns = [
        'interface.*{',
        'useState<.*>',
        'Promise<.*>',
        ':\\s*string',
        ':\\s*number',
        ':\\s*boolean'
      ];
      
      typePatterns.forEach(pattern => {
        totalTypeChecks++;
        if (new RegExp(pattern, 'i').test(content)) {
          typeScore++;
        }
      });
    }
  });
  
  const typeCoverage = (typeScore / totalTypeChecks) * 100;
  
  if (typeCoverage < 70) {
    return { 
      success: false, 
      message: `Low TypeScript coverage: ${typeCoverage.toFixed(1)}%`, 
      critical: false 
    };
  }
  
  return { success: true, message: `Good TypeScript coverage: ${typeCoverage.toFixed(1)}%` };
});

// Test 7: User Experience Features
runTest('User Experience Implementation', () => {
  const mainPagePath = path.join(__dirname, 'src/app/resume-builder/page.tsx');
  const createPagePath = path.join(__dirname, 'src/app/resume-builder/create/page.tsx');
  
  const mainContent = fs.readFileSync(mainPagePath, 'utf8');
  const createContent = fs.readFileSync(createPagePath, 'utf8');
  
  const uxFeatures = [
    { content: mainContent, pattern: 'Loader2.*animate-spin', name: 'Loading indicators' },
    { content: mainContent, pattern: 'confirm\\(', name: 'Confirmation dialogs' },
    { content: createContent, pattern: 'placeholder=', name: 'Input placeholders' },
    { content: createContent, pattern: 'disabled={loading}', name: 'Loading states' },
    { content: mainContent, pattern: 'pagination', name: 'Pagination' },
    { content: createContent, pattern: 'border-red', name: 'Error styling' }
  ];
  
  const missingFeatures = uxFeatures.filter(f => !new RegExp(f.pattern, 'i').test(f.content));
  
  if (missingFeatures.length > 2) {
    return { 
      success: false, 
      message: `Missing UX features: ${missingFeatures.map(f => f.name).join(', ')}`, 
      critical: false 
    };
  }
  
  return { success: true, message: `Good UX implementation (${uxFeatures.length - missingFeatures.length}/${uxFeatures.length} features)` };
});

// Test 8: Integration Points
runTest('Integration with Existing System', () => {
  const toolsPagePath = path.join(__dirname, 'src/app/tools/page.tsx');
  
  if (!fs.existsSync(toolsPagePath)) {
    return { 
      success: false, 
      message: 'Tools page not found', 
      critical: false 
    };
  }
  
  const toolsContent = fs.readFileSync(toolsPagePath, 'utf8');
  
  const integrationChecks = [
    'resume-builder',
    'Resume.*Builder',
    'FileText'
  ];
  
  const missingIntegration = integrationChecks.filter(check => 
    !new RegExp(check, 'i').test(toolsContent)
  );
  
  if (missingIntegration.length > 0) {
    return { 
      success: false, 
      message: 'Not integrated with tools page', 
      critical: false 
    };
  }
  
  return { success: true, message: 'Successfully integrated with existing tools' };
});

// Final Summary
console.log('\n' + '='.repeat(60));
console.log('🏁 INTEGRATION TEST SUMMARY');
console.log('='.repeat(60));
console.log(`✅ Passed Tests: ${passedTests}/${totalTests}`);
console.log(`📊 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (criticalIssues.length > 0) {
  console.log('\n🚨 CRITICAL ISSUES:');
  criticalIssues.forEach(issue => console.log(`   ❌ ${issue}`));
}

if (warnings.length > 0) {
  console.log('\n⚠️  WARNINGS:');
  warnings.forEach(warning => console.log(`   ⚠️  ${warning}`));
}

// Overall Assessment
console.log('\n📋 OVERALL ASSESSMENT:');
const successRate = (passedTests / totalTests) * 100;

if (criticalIssues.length === 0 && successRate >= 90) {
  console.log('🎉 EXCELLENT: Production ready with minor optimizations needed');
} else if (criticalIssues.length === 0 && successRate >= 75) {
  console.log('✅ GOOD: Ready for testing with some improvements needed');
} else if (criticalIssues.length <= 2 && successRate >= 60) {
  console.log('⚠️  FAIR: Needs significant improvements before deployment');
} else {
  console.log('❌ POOR: Major issues need to be resolved');
}

console.log('\n' + '='.repeat(60));
console.log('Test completed at:', new Date().toISOString());
