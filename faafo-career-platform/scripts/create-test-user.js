#!/usr/bin/env node

/**
 * Create Test User for Resume Builder Testing
 * Creates a verified test user in the database
 */

const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword',
  name: 'Test User',
  firstName: 'Test',
  lastName: 'User'
};

async function createTestUser() {
  console.log('🔧 Creating Test User for Resume Builder Testing...');
  console.log('==================================================');

  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: TEST_USER.email }
    });

    if (existingUser) {
      console.log('✅ Test user already exists');
      console.log(`   User ID: ${existingUser.id}`);
      console.log(`   Email: ${existingUser.email}`);
      console.log(`   Email Verified: ${existingUser.emailVerified ? 'Yes' : 'No'}`);

      // Update to ensure email is verified
      if (!existingUser.emailVerified) {
        await prisma.user.update({
          where: { id: existingUser.id },
          data: { emailVerified: new Date() }
        });
        console.log('✅ Email verification updated');
      }

      return existingUser;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(TEST_USER.password, 12);

    // Create the user
    const user = await prisma.user.create({
      data: {
        email: TEST_USER.email,
        password: hashedPassword,
        name: TEST_USER.name,
        emailVerified: new Date(), // Mark as verified for testing
      },
    });

    console.log('✅ Test user created successfully');
    console.log(`   User ID: ${user.id}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Name: ${user.name}`);

    // Create a basic profile for the user
    const profile = await prisma.profile.create({
      data: {
        userId: user.id,
        firstName: TEST_USER.firstName,
        lastName: TEST_USER.lastName,
        bio: 'Test user for Resume Builder testing',
        profileVisibility: 'COMMUNITY_ONLY',
        emailNotifications: true,
        profilePublic: false,
        showEmail: false,
        showPhone: false,
        profileCompletionScore: 50,
        forumReputation: 0,
        forumPostCount: 0,
        forumReplyCount: 0,
      },
    });

    console.log('✅ Test user profile created');
    console.log(`   Profile ID: ${profile.id}`);

    return user;

  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  }
}

async function verifyTestUser() {
  console.log('\n🔍 Verifying Test User Setup...');

  try {
    const user = await prisma.user.findUnique({
      where: { email: TEST_USER.email },
      include: {
        profile: true,
        resumes: true
      }
    });

    if (!user) {
      console.log('❌ Test user not found');
      return false;
    }

    console.log('✅ Test user verification complete');
    console.log(`   User ID: ${user.id}`);
    console.log(`   Email: ${user.email}`);
    console.log(`   Email Verified: ${user.emailVerified ? 'Yes' : 'No'}`);
    console.log(`   Profile: ${user.profile ? 'Yes' : 'No'}`);
    console.log(`   Existing Resumes: ${user.resumes.length}`);

    return true;

  } catch (error) {
    console.error('❌ Error verifying test user:', error);
    return false;
  }
}

async function testAuthentication() {
  console.log('\n🔐 Testing Password Authentication...');

  try {
    const user = await prisma.user.findUnique({
      where: { email: TEST_USER.email }
    });

    if (!user) {
      console.log('❌ User not found for authentication test');
      return false;
    }

    const isValidPassword = await bcrypt.compare(TEST_USER.password, user.password);

    if (isValidPassword) {
      console.log('✅ Password authentication test passed');
      return true;
    } else {
      console.log('❌ Password authentication test failed');
      return false;
    }

  } catch (error) {
    console.error('❌ Error testing authentication:', error);
    return false;
  }
}

async function main() {
  try {
    await createTestUser();
    const verified = await verifyTestUser();
    const authTest = await testAuthentication();

    if (verified && authTest) {
      console.log('\n🎉 TEST USER SETUP COMPLETE!');
      console.log('============================');
      console.log('✅ Test user created and verified');
      console.log('✅ Authentication working');
      console.log('✅ Profile created');
      console.log('✅ Ready for Resume Builder testing');
      console.log('');
      console.log('Test Credentials:');
      console.log(`   Email: ${TEST_USER.email}`);
      console.log(`   Password: ${TEST_USER.password}`);
      console.log('');
      console.log('🚀 Ready to run testerat!');
    } else {
      console.log('\n❌ Test user setup failed');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createTestUser, verifyTestUser, testAuthentication };
