#!/usr/bin/env node

/**
 * 🐭 TESTERAT - Simplified Admin Security Testing
 * 
 * Security testing that doesn't require importing TypeScript modules
 * Tests database-level security and role validation
 */

const { PrismaClient, UserRole } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  critical: 0,
  errors: [],
  details: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '🔍',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    critical: '🚨'
  }[type];
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function addTestResult(testName, passed, details = '', error = null, critical = false) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`${testName}: PASSED ${details}`, 'success');
  } else {
    testResults.failed++;
    if (critical) {
      testResults.critical++;
      log(`${testName}: CRITICAL FAILURE ${details}`, 'critical');
    } else {
      log(`${testName}: FAILED ${details}`, 'error');
    }
    if (error) {
      testResults.errors.push({ test: testName, error: error.message, critical });
    }
  }
  
  testResults.details.push({
    test: testName,
    passed,
    details,
    critical,
    timestamp: new Date().toISOString()
  });
}

// Security test functions
async function testDatabaseRoleSecurity() {
  log('Testing database role security...', 'info');
  
  const createdUsers = [];
  
  try {
    // Test 1: Create users with different roles
    const testUsers = [
      { email: '<EMAIL>', role: UserRole.USER, name: 'Security Test User' },
      { email: '<EMAIL>', role: UserRole.ADMIN, name: 'Security Test Admin' },
      { email: '<EMAIL>', role: UserRole.SUPER_ADMIN, name: 'Security Test Super Admin' }
    ];
    
    for (const userData of testUsers) {
      const hashedPassword = await bcrypt.hash('testpassword123', 12);
      
      const user = await prisma.user.create({
        data: {
          email: userData.email,
          password: hashedPassword,
          name: userData.name,
          role: userData.role,
          emailVerified: new Date()
        }
      });
      
      createdUsers.push(user);
      
      addTestResult(
        `Role Creation: ${userData.role}`,
        user.role === userData.role,
        `User ${user.email} created with role ${user.role}`
      );
    }
    
    // Test 2: Verify role queries work correctly
    const regularUser = createdUsers.find(u => u.role === 'USER');
    const adminUser = createdUsers.find(u => u.role === 'ADMIN');
    const superAdminUser = createdUsers.find(u => u.role === 'SUPER_ADMIN');
    
    // Test role-based queries
    const adminUsers = await prisma.user.findMany({
      where: {
        role: {
          in: ['ADMIN', 'SUPER_ADMIN']
        }
      },
      select: { id: true, email: true, role: true }
    });
    
    const hasRegularUser = adminUsers.some(u => u.id === regularUser.id);
    const hasAdminUser = adminUsers.some(u => u.id === adminUser.id);
    const hasSuperAdminUser = adminUsers.some(u => u.id === superAdminUser.id);
    
    addTestResult(
      'Role Query: Admin filter excludes regular users',
      !hasRegularUser,
      `Regular user in admin query: ${hasRegularUser}`,
      null,
      hasRegularUser // Critical if regular user appears in admin query
    );
    
    addTestResult(
      'Role Query: Admin filter includes admin users',
      hasAdminUser && hasSuperAdminUser,
      `Admin: ${hasAdminUser}, Super Admin: ${hasSuperAdminUser}`
    );
    
    // Test 3: Role update security
    const originalRole = regularUser.role;
    await prisma.user.update({
      where: { id: regularUser.id },
      data: { role: UserRole.ADMIN }
    });
    
    const updatedUser = await prisma.user.findUnique({
      where: { id: regularUser.id },
      select: { role: true }
    });
    
    addTestResult(
      'Role Update: Database allows role changes',
      updatedUser.role === 'ADMIN',
      `Role changed from ${originalRole} to ${updatedUser.role}`
    );
    
    // Revert the change
    await prisma.user.update({
      where: { id: regularUser.id },
      data: { role: originalRole }
    });
    
  } catch (error) {
    addTestResult(
      'Database Role Security: Test execution',
      false,
      'Failed to execute database role security tests',
      error,
      true
    );
  }
  
  return createdUsers;
}

async function testSQLInjectionProtection() {
  log('Testing SQL injection protection...', 'info');
  
  const maliciousPayloads = [
    "'; DROP TABLE users; --",
    "<EMAIL>' OR '1'='1",
    "' UNION SELECT * FROM users --",
    "'; UPDATE users SET role='ADMIN' WHERE '1'='1'; --",
    "admin'; DELETE FROM users; --"
  ];
  
  try {
    // Test 4: SQL injection in email queries
    for (const payload of maliciousPayloads) {
      try {
        const result = await prisma.user.findUnique({
          where: { email: payload },
          select: { role: true, email: true }
        });
        
        addTestResult(
          `SQL Injection: Email query with "${payload.substring(0, 20)}..."`,
          result === null,
          result ? 'Payload returned unexpected result' : 'Payload safely handled'
        );
      } catch (error) {
        // Errors are expected for malicious payloads
        addTestResult(
          `SQL Injection: Email query error handling "${payload.substring(0, 20)}..."`,
          true,
          'Malicious payload properly rejected'
        );
      }
    }
    
    // Test 5: SQL injection in role queries
    for (const payload of maliciousPayloads) {
      try {
        // This should fail at the TypeScript/Prisma level
        const result = await prisma.user.findMany({
          where: { role: payload },
          select: { role: true, email: true }
        });
        
        addTestResult(
          `SQL Injection: Role query with "${payload.substring(0, 20)}..."`,
          result.length === 0,
          result.length > 0 ? 'Payload returned unexpected results' : 'Payload safely handled'
        );
      } catch (error) {
        addTestResult(
          `SQL Injection: Role query error handling "${payload.substring(0, 20)}..."`,
          true,
          'Malicious payload properly rejected'
        );
      }
    }
    
  } catch (error) {
    addTestResult(
      'SQL Injection Protection: Test execution',
      false,
      'Failed to execute SQL injection tests',
      error
    );
  }
}

async function testDataIntegrityAndValidation() {
  log('Testing data integrity and validation...', 'info');
  
  try {
    // Test 6: Invalid role values are rejected
    try {
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: await bcrypt.hash('testpassword123', 12),
          name: 'Invalid Role Test',
          role: 'INVALID_ROLE', // This should fail
          emailVerified: new Date()
        }
      });
      
      addTestResult(
        'Data Integrity: Invalid role rejection',
        false,
        'Invalid role was accepted',
        null,
        true // Critical security issue
      );
    } catch (error) {
      addTestResult(
        'Data Integrity: Invalid role rejection',
        true,
        'Invalid role properly rejected'
      );
    }
    
    // Test 7: Default role assignment
    try {
      const userWithDefaultRole = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: await bcrypt.hash('testpassword123', 12),
          name: 'Default Role Test',
          // role field omitted - should default to USER
          emailVerified: new Date()
        }
      });
      
      addTestResult(
        'Data Integrity: Default role assignment',
        userWithDefaultRole.role === 'USER',
        `Default role: ${userWithDefaultRole.role}`
      );
      
      // Cleanup
      await prisma.user.delete({ where: { id: userWithDefaultRole.id } });
      
    } catch (error) {
      addTestResult(
        'Data Integrity: Default role test',
        false,
        'Failed to test default role assignment',
        error
      );
    }
    
    // Test 8: Role enum validation
    const validRoles = Object.values(UserRole);
    addTestResult(
      'Data Integrity: Role enum contains expected values',
      validRoles.includes('USER') && validRoles.includes('ADMIN') && validRoles.includes('SUPER_ADMIN'),
      `Available roles: ${validRoles.join(', ')}`
    );
    
  } catch (error) {
    addTestResult(
      'Data Integrity: Test execution',
      false,
      'Failed to execute data integrity tests',
      error
    );
  }
}

async function testPasswordSecurity() {
  log('Testing password security...', 'info');
  
  try {
    // Test 9: Password hashing
    const plainPassword = 'testpassword123';
    const hashedPassword = await bcrypt.hash(plainPassword, 12);
    
    addTestResult(
      'Password Security: Password is hashed',
      hashedPassword !== plainPassword && hashedPassword.length > 50,
      `Hash length: ${hashedPassword.length} characters`
    );
    
    // Test 10: Password verification
    const isValidPassword = await bcrypt.compare(plainPassword, hashedPassword);
    const isInvalidPassword = await bcrypt.compare('wrongpassword', hashedPassword);
    
    addTestResult(
      'Password Security: Password verification works',
      isValidPassword && !isInvalidPassword,
      `Valid: ${isValidPassword}, Invalid: ${isInvalidPassword}`
    );
    
  } catch (error) {
    addTestResult(
      'Password Security: Test execution',
      false,
      'Failed to execute password security tests',
      error
    );
  }
}

async function cleanup(testUsers) {
  log('Cleaning up security test data...', 'info');
  
  try {
    if (testUsers && testUsers.length > 0) {
      const userIds = testUsers.map(user => user.id);
      await prisma.user.deleteMany({
        where: { id: { in: userIds } }
      });
      log(`Cleaned up ${userIds.length} test users`, 'success');
    }
    
    // Clean up any additional test users
    await prisma.user.deleteMany({
      where: {
        email: { contains: 'testerat-sec' }
      }
    });
    
  } catch (error) {
    log(`Security test cleanup failed: ${error.message}`, 'error');
  }
}

function generateSecurityReport() {
  const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  
  console.log('\n' + '='.repeat(70));
  console.log('🐭 TESTERAT - SIMPLIFIED ADMIN SECURITY TEST REPORT');
  console.log('='.repeat(70));
  console.log(`📊 Total Tests: ${testResults.total}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`🚨 Critical Failures: ${testResults.critical}`);
  console.log(`📈 Pass Rate: ${passRate}%`);
  console.log('='.repeat(70));
  
  if (testResults.critical > 0) {
    console.log('\n🚨 CRITICAL SECURITY ISSUES:');
    testResults.errors.filter(e => e.critical).forEach(error => {
      console.log(`  • ${error.test}: ${error.error || 'Security vulnerability detected'}`);
    });
  }
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.filter(e => !e.critical).forEach(error => {
      console.log(`  • ${error.test}: ${error.error || 'Test failed'}`);
    });
  }
  
  // Security assessment
  if (testResults.critical === 0 && passRate >= 95) {
    console.log('\n🛡️ SECURITY ASSESSMENT: EXCELLENT - Production Ready');
  } else if (testResults.critical === 0 && passRate >= 85) {
    console.log('\n⚠️ SECURITY ASSESSMENT: GOOD - Minor issues to address');
  } else if (testResults.critical > 0) {
    console.log('\n🚨 SECURITY ASSESSMENT: CRITICAL ISSUES - DO NOT DEPLOY');
  } else {
    console.log('\n❌ SECURITY ASSESSMENT: POOR - Significant security concerns');
  }
  
  return testResults.critical === 0 && passRate >= 90;
}

// Main test execution
async function runSimpleSecurityTests() {
  console.log('🐭 TESTERAT - Starting Simplified Admin Security Tests...\n');
  
  let testUsers = [];
  
  try {
    testUsers = await testDatabaseRoleSecurity();
    await testSQLInjectionProtection();
    await testDataIntegrityAndValidation();
    await testPasswordSecurity();
    
  } catch (error) {
    log(`Critical security test failure: ${error.message}`, 'critical');
  } finally {
    await cleanup(testUsers);
    await prisma.$disconnect();
  }
  
  const success = generateSecurityReport();
  
  if (success) {
    console.log('\n🎉 TESTERAT SECURITY VERDICT: ADMIN SYSTEM IS SECURE!');
    process.exit(0);
  } else {
    console.log('\n💥 TESTERAT SECURITY VERDICT: SECURITY ISSUES DETECTED!');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runSimpleSecurityTests().catch(error => {
    console.error('💥 Testerat security tests crashed:', error);
    process.exit(1);
  });
}

module.exports = { runSimpleSecurityTests, testResults };
