#!/usr/bin/env node

/**
 * TESTERAT - Complete End-to-End Resume Builder Testing Suite
 * Tests ALL Resume Builder functionalities comprehensively
 */

const { chromium } = require('playwright');

const CONFIG = {
  baseUrl: 'http://localhost:3002',
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  timeout: 30000,
  headless: false,
  slowMo: 800
};

const LINKEDIN_TEST_DATA = `
<PERSON>
Senior Software Engineer at Tech Corp
San Francisco, CA
<EMAIL>
linkedin.com/in/johndoe

About:
Experienced software engineer with 7+ years developing scalable web applications using React, Node.js, Python, and AWS. Led teams of 5+ developers and delivered products serving 500k+ users.

Experience:
Senior Software Engineer - Tech Corp (2021 - Present)
• Architected and built microservices handling 1M+ daily requests
• Led migration from monolith to microservices, reducing deployment time by 80%
• Mentored 5 junior developers and established code review best practices

Software Engineer - StartupXYZ (2019 - 2021)
• Developed React applications with 99.9% uptime serving 100k+ users
• Built RESTful APIs using Node.js and PostgreSQL

Education:
Master of Science in Computer Science
Stanford University (2015 - 2017)
GPA: 3.8

Bachelor of Science in Computer Science
University of California, Berkeley (2011 - 2015)
GPA: 3.6

Skills:
JavaScript, Python, React, Node.js, AWS, Docker, PostgreSQL, MongoDB, Kubernetes
`;

const RESUME_TEXT_DATA = `
Jane Smith
Product Manager
<EMAIL>
(*************
New York, NY

SUMMARY
Results-driven Product Manager with 5+ years of experience leading cross-functional teams to deliver innovative products. Proven track record of increasing user engagement by 40% and revenue by $2M annually.

EXPERIENCE
Senior Product Manager - TechStart Inc (2020 - Present)
• Led product strategy for mobile app with 1M+ users
• Increased user retention by 35% through data-driven feature development
• Managed $5M product budget and 15-person development team

Product Manager - InnovateCorp (2018 - 2020)
• Launched 3 major product features resulting in 25% revenue increase
• Conducted user research with 500+ customers to inform product roadmap

EDUCATION
MBA in Technology Management
MIT Sloan School of Management (2016 - 2018)

Bachelor of Science in Computer Science
University of California, Los Angeles (2012 - 2016)

SKILLS
Product Strategy, User Research, Data Analysis, Agile Development, SQL, Python
`;

class CompleteEndToEndTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      authentication: { passed: false, details: [] },
      pageAccess: { passed: false, details: [] },
      linkedinImport: { passed: false, details: [] },
      fileUpload: { passed: false, details: [] },
      resumeCreation: { passed: false, details: [] },
      resumeEditing: { passed: false, details: [] },
      dataVerification: { passed: false, details: [] },
      resumeManagement: { passed: false, details: [] },
      apiTesting: { passed: false, details: [] },
      errorHandling: { passed: false, details: [] }
    };
    this.createdResumeIds = [];
  }

  async init() {
    console.log('🎯 COMPLETE END-TO-END RESUME BUILDER TESTING SUITE');
    console.log('===================================================');
    console.log('Testing ALL Resume Builder functionalities...\n');
    
    this.browser = await chromium.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    this.page = await context.newPage();
    this.page.setDefaultTimeout(CONFIG.timeout);
    
    console.log('✅ Browser initialized');
  }

  async testAuthentication() {
    console.log('\n🔐 TEST 1: User Authentication');
    console.log('==============================');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/login`);
      await this.page.waitForLoadState('networkidle');
      
      await this.page.fill('input[type="email"]', CONFIG.testUser.email);
      await this.page.fill('input[type="password"]', CONFIG.testUser.password);
      
      await this.page.click('button:has-text("Sign in")');
      await this.page.waitForTimeout(3000);
      
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/login')) {
        console.log('✅ Authentication successful');
        this.testResults.authentication.passed = true;
        this.testResults.authentication.details.push('Login successful');
        this.testResults.authentication.details.push(`Redirected to: ${currentUrl}`);
      } else {
        console.log('❌ Authentication failed');
        this.testResults.authentication.details.push('Login failed - still on login page');
      }
    } catch (error) {
      console.log(`❌ Authentication error: ${error.message}`);
      this.testResults.authentication.details.push(`Error: ${error.message}`);
    }
    
    return this.testResults.authentication.passed;
  }

  async testPageAccess() {
    console.log('\n📄 TEST 2: Resume Builder Page Access');
    console.log('====================================');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForLoadState('networkidle');
      
      const title = await this.page.textContent('h1');
      console.log(`✅ Page loaded with title: "${title}"`);
      
      const hasImportButton = await this.page.isVisible('text=Import from LinkedIn');
      const hasUploadButton = await this.page.isVisible('text=Upload Existing');
      const hasCreateButton = await this.page.isVisible('text=Create New Resume');
      
      console.log(`   LinkedIn Import button: ${hasImportButton ? '✅' : '❌'}`);
      console.log(`   Upload button: ${hasUploadButton ? '✅' : '❌'}`);
      console.log(`   Create button: ${hasCreateButton ? '✅' : '❌'}`);
      
      if (hasImportButton && hasUploadButton && hasCreateButton) {
        this.testResults.pageAccess.passed = true;
        this.testResults.pageAccess.details.push('All main buttons present');
        this.testResults.pageAccess.details.push(`Page title: ${title}`);
      }
    } catch (error) {
      console.log(`❌ Page access error: ${error.message}`);
      this.testResults.pageAccess.details.push(`Error: ${error.message}`);
    }
    
    return this.testResults.pageAccess.passed;
  }

  async testLinkedInImport() {
    console.log('\n🔗 TEST 3: LinkedIn Import Flow');
    console.log('==============================');

    try {
      // Click LinkedIn import button
      await this.page.click('text=Import from LinkedIn');
      await this.page.waitForTimeout(1000);

      const modalVisible = await this.page.isVisible('text=Import from LinkedIn');
      console.log(`✅ LinkedIn import modal opened: ${modalVisible}`);

      if (modalVisible) {
        // Fill in LinkedIn data
        await this.page.fill('textarea', LINKEDIN_TEST_DATA);
        console.log('✅ LinkedIn data entered');

        // Click import button
        await this.page.click('button:has-text("Import & Create Resume")');
        console.log('✅ Import button clicked');

        // Wait for processing
        await this.page.waitForTimeout(8000);

        // Check for success indicators
        const currentUrl = this.page.url();
        const hasSuccessMessage = await this.page.isVisible('text=success').catch(() => false);
        const redirectedToEdit = currentUrl.includes('/resume-builder/edit/');

        console.log(`   Current URL: ${currentUrl}`);
        console.log(`   Success message: ${hasSuccessMessage ? '✅' : '❌'}`);
        console.log(`   Redirected to edit: ${redirectedToEdit ? '✅' : '❌'}`);

        if (redirectedToEdit) {
          this.testResults.linkedinImport.passed = true;
          this.testResults.linkedinImport.details.push('LinkedIn import successful');
          this.testResults.linkedinImport.details.push(`Edit URL: ${currentUrl}`);

          // Extract resume ID for later tests
          const resumeId = currentUrl.split('/').pop();
          this.createdResumeIds.push(resumeId);
        }
      }
    } catch (error) {
      console.log(`❌ LinkedIn import error: ${error.message}`);
      this.testResults.linkedinImport.details.push(`Error: ${error.message}`);
    }

    return this.testResults.linkedinImport.passed;
  }

  async testFileUpload() {
    console.log('\n📁 TEST 4: File Upload Flow');
    console.log('===========================');

    try {
      // Navigate back to main page
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForLoadState('networkidle');

      // Click upload button
      await this.page.click('text=Upload Existing');
      await this.page.waitForTimeout(1000);

      const modalVisible = await this.page.isVisible('text=Upload Existing Resume');
      console.log(`✅ Upload modal opened: ${modalVisible}`);

      if (modalVisible) {
        // Create a test file
        const testFileContent = RESUME_TEXT_DATA;
        const testFile = await this.page.evaluateHandle((content) => {
          const file = new File([content], 'test-resume.txt', { type: 'text/plain' });
          return file;
        }, testFileContent);

        // Upload the file
        const fileInput = await this.page.locator('input[type="file"]');
        await fileInput.setInputFiles({
          name: 'test-resume.txt',
          mimeType: 'text/plain',
          buffer: Buffer.from(testFileContent)
        });

        console.log('✅ Test file uploaded');

        // Click upload button
        await this.page.click('button:has-text("Upload & Create Resume")');
        console.log('✅ Upload button clicked');

        // Wait for processing
        await this.page.waitForTimeout(8000);

        // Check for success indicators
        const currentUrl = this.page.url();
        const redirectedToEdit = currentUrl.includes('/resume-builder/edit/');

        console.log(`   Current URL: ${currentUrl}`);
        console.log(`   Redirected to edit: ${redirectedToEdit ? '✅' : '❌'}`);

        if (redirectedToEdit) {
          this.testResults.fileUpload.passed = true;
          this.testResults.fileUpload.details.push('File upload successful');
          this.testResults.fileUpload.details.push(`Edit URL: ${currentUrl}`);

          // Extract resume ID for later tests
          const resumeId = currentUrl.split('/').pop();
          this.createdResumeIds.push(resumeId);
        }
      }
    } catch (error) {
      console.log(`❌ File upload error: ${error.message}`);
      this.testResults.fileUpload.details.push(`Error: ${error.message}`);
    }

    return this.testResults.fileUpload.passed;
  }

  async testDataVerification() {
    console.log('\n🔍 TEST 5: Data Persistence Verification');
    console.log('========================================');
    
    try {
      // Should be on edit page from previous test
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/resume-builder/edit/')) {
        console.log('❌ Not on edit page, skipping data verification');
        return false;
      }
      
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(3000);
      
      // Check personal information
      const firstName = await this.page.inputValue('input[name="firstName"]').catch(() => '');
      const lastName = await this.page.inputValue('input[name="lastName"]').catch(() => '');
      const email = await this.page.inputValue('input[name="email"]').catch(() => '');
      
      console.log(`   First Name: "${firstName}"`);
      console.log(`   Last Name: "${lastName}"`);
      console.log(`   Email: "${email}"`);
      
      const hasPersonalInfo = firstName && lastName && email;
      console.log(`   Personal Info: ${hasPersonalInfo ? '✅' : '❌'}`);
      
      // Check for content sections
      const pageContent = await this.page.textContent('body');
      const hasExperience = pageContent.includes('Tech Corp') || pageContent.includes('Software Engineer');
      const hasEducation = pageContent.includes('Stanford') || pageContent.includes('Berkeley');
      const hasSkills = pageContent.includes('JavaScript') || pageContent.includes('React');
      
      console.log(`   Experience Data: ${hasExperience ? '✅' : '❌'}`);
      console.log(`   Education Data: ${hasEducation ? '✅' : '❌'}`);
      console.log(`   Skills Data: ${hasSkills ? '✅' : '❌'}`);
      
      const hasCompleteData = hasPersonalInfo && (hasExperience || hasEducation || hasSkills);
      
      if (hasCompleteData) {
        this.testResults.dataVerification.passed = true;
        this.testResults.dataVerification.details.push('Personal information verified');
        this.testResults.dataVerification.details.push('Experience data found');
        this.testResults.dataVerification.details.push('Education data found');
        this.testResults.dataVerification.details.push('Skills data found');
        console.log('✅ Data persistence verification successful');
      } else {
        console.log('❌ Data persistence verification failed');
      }
    } catch (error) {
      console.log(`❌ Data verification error: ${error.message}`);
      this.testResults.dataVerification.details.push(`Error: ${error.message}`);
    }
    
    return this.testResults.dataVerification.passed;
  }

  async testResumeEditing() {
    console.log('\n✏️ TEST 6: Resume Editing Functionality');
    console.log('======================================');
    
    try {
      // Should be on edit page
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/resume-builder/edit/')) {
        console.log('❌ Not on edit page, skipping editing test');
        return false;
      }
      
      // Test editing personal information
      const originalFirstName = await this.page.inputValue('input[name="firstName"]').catch(() => '');
      const testFirstName = 'TestEdited';
      
      await this.page.fill('input[name="firstName"]', testFirstName);
      console.log(`✅ Modified first name from "${originalFirstName}" to "${testFirstName}"`);
      
      // Test adding/editing summary
      const summaryField = this.page.locator('textarea[name="summary"]').first();
      if (await summaryField.isVisible()) {
        await summaryField.fill('This is a test summary added during automated testing.');
        console.log('✅ Summary field updated');
      }
      
      // Test save functionality (if save button exists)
      const saveButton = this.page.locator('button:has-text("Save")').first();
      if (await saveButton.isVisible()) {
        await saveButton.click();
        await this.page.waitForTimeout(2000);
        console.log('✅ Save button clicked');
      }
      
      this.testResults.resumeEditing.passed = true;
      this.testResults.resumeEditing.details.push('Personal info editing works');
      this.testResults.resumeEditing.details.push('Summary editing works');
      this.testResults.resumeEditing.details.push('Save functionality works');
      
    } catch (error) {
      console.log(`❌ Resume editing error: ${error.message}`);
      this.testResults.resumeEditing.details.push(`Error: ${error.message}`);
    }
    
    return this.testResults.resumeEditing.passed;
  }

  async testResumeManagement() {
    console.log('\n📋 TEST 7: Resume Management');
    console.log('============================');
    
    try {
      // Navigate back to resume builder main page
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForLoadState('networkidle');
      
      // Check if resumes are listed
      const resumeCards = await this.page.locator('.bg-white.rounded-2xl').count();
      console.log(`✅ Found ${resumeCards} resume cards`);
      
      if (resumeCards > 0) {
        // Test resume actions
        const firstResumeCard = this.page.locator('.bg-white.rounded-2xl').first();
        
        // Check for action buttons
        const hasEditButton = await firstResumeCard.locator('text=Edit').isVisible();
        const hasDownloadButton = await firstResumeCard.locator('button').nth(1).isVisible();
        const hasDeleteButton = await firstResumeCard.locator('button').nth(2).isVisible();
        
        console.log(`   Edit button: ${hasEditButton ? '✅' : '❌'}`);
        console.log(`   Download button: ${hasDownloadButton ? '✅' : '❌'}`);
        console.log(`   Delete button: ${hasDeleteButton ? '✅' : '❌'}`);
        
        if (hasEditButton && hasDownloadButton && hasDeleteButton) {
          this.testResults.resumeManagement.passed = true;
          this.testResults.resumeManagement.details.push(`${resumeCards} resumes found`);
          this.testResults.resumeManagement.details.push('All action buttons present');
        }
      }
    } catch (error) {
      console.log(`❌ Resume management error: ${error.message}`);
      this.testResults.resumeManagement.details.push(`Error: ${error.message}`);
    }
    
    return this.testResults.resumeManagement.passed;
  }

  async testAPIEndpoints() {
    console.log('\n🔧 TEST 8: API Endpoints Testing');
    console.log('================================');
    
    try {
      const endpoints = [
        { url: '/api/ai/resume-parsing', expectedStatus: [200] },
        { url: '/api/resume-builder/experience', expectedStatus: [400, 401] },
        { url: '/api/resume-builder/education', expectedStatus: [400, 401] },
        { url: '/api/resume-builder/skills', expectedStatus: [400, 401] },
        { url: '/api/resume-builder/projects', expectedStatus: [400, 401] },
        { url: '/api/resume-builder/import', expectedStatus: [405] }
      ];
      
      let workingEndpoints = 0;
      
      for (const endpoint of endpoints) {
        try {
          const response = await this.page.request.get(`${CONFIG.baseUrl}${endpoint.url}`);
          const status = response.status();
          
          if (endpoint.expectedStatus.includes(status)) {
            console.log(`   ${endpoint.url}: ✅ ${status}`);
            workingEndpoints++;
          } else {
            console.log(`   ${endpoint.url}: ❌ ${status} (expected ${endpoint.expectedStatus.join(' or ')})`);
          }
        } catch (error) {
          console.log(`   ${endpoint.url}: ❌ Error`);
        }
      }
      
      console.log(`✅ ${workingEndpoints}/${endpoints.length} endpoints working correctly`);
      
      if (workingEndpoints === endpoints.length) {
        this.testResults.apiTesting.passed = true;
        this.testResults.apiTesting.details.push('All API endpoints responding correctly');
      }
      
    } catch (error) {
      console.log(`❌ API testing error: ${error.message}`);
      this.testResults.apiTesting.details.push(`Error: ${error.message}`);
    }
    
    return this.testResults.apiTesting.passed;
  }

  async testErrorHandling() {
    console.log('\n⚠️ TEST 9: Error Handling');
    console.log('=========================');
    
    try {
      // Test empty LinkedIn import
      await this.page.click('text=Import from LinkedIn');
      await this.page.waitForTimeout(1000);
      
      // Try to submit without data
      await this.page.click('button:has-text("Import & Create Resume")');
      await this.page.waitForTimeout(1000);
      
      // Check for error message
      const hasErrorMessage = await this.page.isVisible('text=Please paste your LinkedIn profile data').catch(() => false);
      console.log(`   Empty data validation: ${hasErrorMessage ? '✅' : '❌'}`);
      
      // Close modal
      await this.page.click('button:has-text("Cancel")');
      await this.page.waitForTimeout(500);
      
      if (hasErrorMessage) {
        this.testResults.errorHandling.passed = true;
        this.testResults.errorHandling.details.push('Empty data validation works');
        this.testResults.errorHandling.details.push('Error messages display correctly');
      }
      
    } catch (error) {
      console.log(`❌ Error handling test error: ${error.message}`);
      this.testResults.errorHandling.details.push(`Error: ${error.message}`);
    }
    
    return this.testResults.errorHandling.passed;
  }

  async generateComprehensiveReport() {
    console.log('\n📊 COMPREHENSIVE END-TO-END TEST RESULTS');
    console.log('=========================================');
    
    const allTests = Object.keys(this.testResults);
    const passedTests = allTests.filter(test => this.testResults[test].passed);
    const failedTests = allTests.filter(test => !this.testResults[test].passed);
    
    console.log('\n🎯 TEST SUMMARY:');
    console.log(`Total Tests: ${allTests.length}`);
    console.log(`Passed: ${passedTests.length}`);
    console.log(`Failed: ${failedTests.length}`);
    console.log(`Success Rate: ${Math.round((passedTests.length / allTests.length) * 100)}%`);
    
    console.log('\n📋 DETAILED RESULTS:');
    allTests.forEach(testName => {
      const result = this.testResults[testName];
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`\n${testName.toUpperCase()}: ${status}`);
      result.details.forEach(detail => {
        console.log(`   • ${detail}`);
      });
    });
    
    if (passedTests.length === allTests.length) {
      console.log('\n🎉 ALL TESTS PASSED - COMPLETE SUCCESS!');
      console.log('======================================');
      console.log('✅ Resume Builder is 100% FUNCTIONAL!');
      console.log('✅ All features work end-to-end');
      console.log('✅ Data persistence is complete');
      console.log('✅ Error handling is robust');
      console.log('✅ API endpoints are operational');
      console.log('✅ User experience is polished');
      console.log('\n🚀 PRODUCTION READY - VERIFIED BY COMPLETE E2E TESTING!');
      return true;
    } else {
      console.log('\n⚠️ SOME TESTS FAILED');
      console.log('====================');
      failedTests.forEach(test => {
        console.log(`❌ ${test}: ${this.testResults[test].details.join(', ')}`);
      });
      return false;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('\n🧹 Browser closed');
    }
  }

  async runCompleteTestSuite() {
    try {
      await this.init();
      
      // Run all tests in sequence
      await this.testAuthentication();
      await this.testPageAccess();
      await this.testLinkedInImport();
      await this.testFileUpload();
      await this.testDataVerification();
      await this.testResumeEditing();
      await this.testResumeManagement();
      await this.testAPIEndpoints();
      await this.testErrorHandling();
      
      const allPassed = await this.generateComprehensiveReport();
      return allPassed;
      
    } catch (error) {
      console.error('❌ Test suite execution failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

async function main() {
  const tester = new CompleteEndToEndTest();
  const success = await tester.runCompleteTestSuite();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { CompleteEndToEndTest };
