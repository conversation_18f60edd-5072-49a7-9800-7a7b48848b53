#!/usr/bin/env node

/**
 * TESTERAT - Automated Resume Builder Testing
 * Tests complete functionality with automatic authentication
 */

const { chromium } = require('playwright');
const fetch = require('node-fetch');

const CONFIG = {
  baseUrl: 'http://localhost:3001',
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  timeout: 30000,
  headless: false,
  slowMo: 200
};

const LINKEDIN_TEST_DATA = `
<PERSON>
Senior Software Engineer at Tech Corp
San Francisco, CA
<EMAIL>
linkedin.com/in/johndoe

About:
Experienced software engineer with 7+ years developing scalable web applications using React, Node.js, Python, and AWS. Led teams of 5+ developers and delivered products serving 500k+ users.

Experience:
Senior Software Engineer - Tech Corp (2021 - Present)
• Architected and built microservices handling 1M+ daily requests
• Led migration from monolith to microservices, reducing deployment time by 80%
• Mentored 5 junior developers and established code review best practices

Software Engineer - StartupXYZ (2019 - 2021)
• Developed React applications with 99.9% uptime serving 100k+ users
• Built RESTful APIs using Node.js and PostgreSQL

Education:
Master of Science in Computer Science
Stanford University (2015 - 2017)
GPA: 3.8

Bachelor of Science in Computer Science
University of California, Berkeley (2011 - 2015)
GPA: 3.6

Skills:
Programming Languages: JavaScript, Python, Java, TypeScript, Go
Frontend: React, Vue.js, Angular, HTML5, CSS3, Sass
Backend: Node.js, Express, Django, Flask, Spring Boot
Databases: PostgreSQL, MongoDB, Redis, MySQL
Cloud: AWS, Google Cloud, Azure, Docker, Kubernetes
`;

class AutomatedResumeTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.sessionCookie = null;
    this.results = {
      authentication: false,
      pageAccess: false,
      linkedinImport: false,
      dataVerification: false,
      apiTesting: false
    };
  }

  async init() {
    console.log('🚀 Initializing Automated Resume Builder Test...');

    this.browser = await chromium.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo
    });

    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1280, height: 720 });
    this.page.setDefaultTimeout(CONFIG.timeout);

    console.log('✅ Browser initialized');
  }

  async authenticateViaAPI() {
    console.log('\n🔐 Testing API Authentication...');
    
    try {
      // First, get the login page to extract any CSRF tokens
      await this.page.goto(`${CONFIG.baseUrl}/login`);
      await this.page.waitForLoadState('networkidle');
      
      // Get all cookies from the page
      const cookies = await this.page.context().cookies();
      
      // Try to login via form submission
      await this.page.fill('input[name="email"], input[type="email"]', CONFIG.testUser.email);
      await this.page.fill('input[name="password"], input[type="password"]', CONFIG.testUser.password);
      
      // Submit the form and wait for navigation
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle' }),
        this.page.click('button[type="submit"], input[type="submit"]')
      ]);
      
      // Check if we're redirected away from login page
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/login')) {
        console.log('✅ Authentication successful via form submission');
        console.log(`   Redirected to: ${currentUrl}`);
        this.results.authentication = true;
        return true;
      } else {
        console.log('❌ Form authentication failed');
        return false;
      }
    } catch (error) {
      console.log(`❌ Authentication error: ${error.message}`);
      return false;
    }
  }

  async testAPIEndpoints() {
    console.log('\n🔧 Testing API Endpoints...');
    
    try {
      // Get cookies from the authenticated session
      const cookies = await this.page.context().cookies();
      const cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
      
      // Test the resume parsing API
      const response = await fetch(`${CONFIG.baseUrl}/api/ai/resume-parsing`, {
        method: 'GET',
        headers: {
          'Cookie': cookieString,
          'User-Agent': 'Mozilla/5.0 (compatible; TestBot/1.0)'
        }
      });
      
      console.log(`   Resume Parsing API: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ API accessible with authentication');
        console.log(`   Supported formats: ${data.supportedFormats?.join(', ')}`);
        this.results.apiTesting = true;
        return true;
      } else {
        console.log('❌ API not accessible');
        return false;
      }
    } catch (error) {
      console.log(`❌ API testing error: ${error.message}`);
      return false;
    }
  }

  async testResumeBuilderPage() {
    console.log('\n📄 Testing Resume Builder Page...');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForSelector('h1, [data-testid="page-title"]', { timeout: 10000 });
      
      const title = await this.page.textContent('h1, [data-testid="page-title"]');
      console.log(`✅ Page loaded with title: "${title}"`);
      
      // Wait for the page to fully load
      await this.page.waitForTimeout(2000);
      
      // Check for key elements using multiple selectors
      const importButton = await this.page.locator('text=Import from LinkedIn').first().isVisible().catch(() => false) ||
                          await this.page.locator('button:has-text("Import")').first().isVisible().catch(() => false) ||
                          await this.page.locator('[data-testid="linkedin-import"]').isVisible().catch(() => false);
      
      const uploadButton = await this.page.locator('text=Upload Existing').first().isVisible().catch(() => false) ||
                          await this.page.locator('button:has-text("Upload")').first().isVisible().catch(() => false) ||
                          await this.page.locator('[data-testid="resume-upload"]').isVisible().catch(() => false);
      
      console.log(`   LinkedIn Import button visible: ${importButton ? '✅' : '❌'}`);
      console.log(`   Upload button visible: ${uploadButton ? '✅' : '❌'}`);
      
      this.results.pageAccess = importButton || uploadButton; // At least one should be visible
      return this.results.pageAccess;
    } catch (error) {
      console.log(`❌ Page access error: ${error.message}`);
      return false;
    }
  }

  async testLinkedInImportFlow() {
    console.log('\n🔗 Testing LinkedIn Import Flow...');
    
    try {
      // Look for LinkedIn import button with various approaches
      let importButton = null;
      
      // Try different selectors
      const selectors = [
        'text=Import from LinkedIn',
        'button:has-text("Import")',
        'button:has-text("LinkedIn")',
        '[data-testid="linkedin-import"]',
        'button[aria-label*="LinkedIn"]'
      ];
      
      for (const selector of selectors) {
        try {
          importButton = await this.page.locator(selector).first();
          if (await importButton.isVisible()) {
            console.log(`✅ Found import button with selector: ${selector}`);
            break;
          }
        } catch (e) {
          // Continue to next selector
        }
      }
      
      if (!importButton || !(await importButton.isVisible())) {
        console.log('❌ LinkedIn Import button not found');
        return false;
      }
      
      // Click the import button
      await importButton.click();
      await this.page.waitForTimeout(1000);
      
      // Look for modal or form
      const textarea = await this.page.locator('textarea').first();
      if (!(await textarea.isVisible())) {
        console.log('❌ LinkedIn import form not opened');
        return false;
      }
      
      console.log('✅ LinkedIn import form opened');
      
      // Fill in the LinkedIn data
      await textarea.fill(LINKEDIN_TEST_DATA);
      console.log('✅ LinkedIn data entered');
      
      // Look for submit button
      const submitSelectors = [
        'button:has-text("Import")',
        'button:has-text("Submit")',
        'button[type="submit"]',
        'button:has-text("Create")'
      ];
      
      let submitButton = null;
      for (const selector of submitSelectors) {
        try {
          submitButton = await this.page.locator(selector).first();
          if (await submitButton.isVisible()) {
            console.log(`✅ Found submit button with selector: ${selector}`);
            break;
          }
        } catch (e) {
          // Continue
        }
      }
      
      if (!submitButton || !(await submitButton.isVisible())) {
        console.log('❌ Submit button not found');
        return false;
      }
      
      // Submit the form and wait for navigation
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle', timeout: 30000 }),
        submitButton.click()
      ]);
      
      // Check if redirected to edit page
      const currentUrl = this.page.url();
      if (currentUrl.includes('/resume-builder/edit/')) {
        console.log('✅ Successfully redirected to edit page');
        console.log(`   Edit URL: ${currentUrl}`);
        this.results.linkedinImport = true;
        return true;
      } else {
        console.log(`❌ Not redirected to edit page. Current URL: ${currentUrl}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ LinkedIn import error: ${error.message}`);
      return false;
    }
  }

  async testDataPersistence() {
    console.log('\n🔍 Testing Data Persistence...');
    
    try {
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/resume-builder/edit/')) {
        console.log('❌ Not on edit page, skipping data verification');
        return false;
      }
      
      // Wait for page to load
      await this.page.waitForTimeout(3000);
      
      // Check personal information
      const firstName = await this.page.inputValue('input[name="firstName"]').catch(() => '');
      const lastName = await this.page.inputValue('input[name="lastName"]').catch(() => '');
      const email = await this.page.inputValue('input[name="email"]').catch(() => '');
      
      console.log(`   First Name: "${firstName}"`);
      console.log(`   Last Name: "${lastName}"`);
      console.log(`   Email: "${email}"`);
      
      const hasPersonalInfo = firstName && lastName && email;
      console.log(`   Personal Info: ${hasPersonalInfo ? '✅' : '❌'}`);
      
      // Check for content sections
      const pageContent = await this.page.textContent('body');
      const hasExperience = pageContent.includes('Tech Corp') || pageContent.includes('Software Engineer');
      const hasEducation = pageContent.includes('Stanford') || pageContent.includes('Berkeley');
      const hasSkills = pageContent.includes('JavaScript') || pageContent.includes('React');
      
      console.log(`   Experience Data: ${hasExperience ? '✅' : '❌'}`);
      console.log(`   Education Data: ${hasEducation ? '✅' : '❌'}`);
      console.log(`   Skills Data: ${hasSkills ? '✅' : '❌'}`);
      
      const hasCompleteData = hasPersonalInfo && (hasExperience || hasEducation || hasSkills);
      this.results.dataVerification = hasCompleteData;
      
      if (hasCompleteData) {
        console.log('✅ Data persistence verification successful');
      } else {
        console.log('❌ Data persistence verification failed');
      }
      
      return hasCompleteData;
    } catch (error) {
      console.log(`❌ Data verification error: ${error.message}`);
      return false;
    }
  }

  async generateReport() {
    console.log('\n📊 AUTOMATED TEST RESULTS');
    console.log('==========================');
    
    const results = this.results;
    const passCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`Authentication: ${results.authentication ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`API Testing: ${results.apiTesting ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Page Access: ${results.pageAccess ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`LinkedIn Import: ${results.linkedinImport ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Data Verification: ${results.dataVerification ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);
    
    if (passCount === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('✅ Resume Builder is 100% FUNCTIONAL!');
      console.log('✅ Authentication works automatically');
      console.log('✅ LinkedIn Import works end-to-end');
      console.log('✅ Data persistence is complete');
      console.log('✅ APIs are properly secured');
      console.log('\n🚀 PRODUCTION READY!');
    } else if (passCount >= 3) {
      console.log('\n✅ CORE FUNCTIONALITY WORKS!');
      console.log('⚠️  Some advanced features may need attention');
    } else {
      console.log('\n❌ Critical functionality issues found');
    }
    
    return passCount === totalTests;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('\n🧹 Browser closed');
    }
  }

  async runFullTest() {
    try {
      await this.init();
      
      const authSuccess = await this.authenticateViaAPI();
      if (!authSuccess) {
        console.log('❌ Cannot proceed without authentication');
        return false;
      }
      
      await this.testAPIEndpoints();
      await this.testResumeBuilderPage();
      await this.testLinkedInImportFlow();
      await this.testDataPersistence();
      
      const allPassed = await this.generateReport();
      return allPassed;
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

async function main() {
  console.log('🎯 AUTOMATED RESUME BUILDER TESTERAT');
  console.log('====================================');
  
  const tester = new AutomatedResumeTest();
  const success = await tester.runFullTest();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { AutomatedResumeTest };
