#!/usr/bin/env node

/**
 * TESTERAT - Final Resume Builder Validation
 * Comprehensive validation of all Resume Builder functionality
 */

const { chromium } = require('playwright');

const CONFIG = {
  baseUrl: 'http://localhost:3002',
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  timeout: 30000,
  headless: false,
  slowMo: 1000
};

const LINKEDIN_TEST_DATA = `
<PERSON>
Senior Product Manager at TechCorp
Seattle, WA
<EMAIL>
linkedin.com/in/sarah<PERSON><PERSON>son

About:
Experienced Product Manager with 6+ years leading cross-functional teams to deliver innovative products. Proven track record of increasing user engagement by 45% and revenue by $3M annually.

Experience:
Senior Product Manager - TechCorp (2020 - Present)
• Led product strategy for mobile app with 2M+ users
• Increased user retention by 40% through data-driven feature development
• Managed $8M product budget and 20-person development team

Product Manager - StartupABC (2018 - 2020)
• Launched 4 major product features resulting in 30% revenue increase
• Conducted user research with 800+ customers to inform product roadmap

Education:
MBA in Technology Management
Stanford Graduate School of Business (2016 - 2018)

Bachelor of Science in Computer Science
University of Washington (2012 - 2016)

Skills:
Product Strategy, User Research, Data Analysis, Agile Development, SQL, Python, Figma
`;

class FinalValidationTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      authentication: false,
      pageLoad: false,
      linkedinImport: false,
      dataCreation: false,
      resumeList: false,
      editPageAccess: false,
      overallSuccess: false
    };
  }

  async init() {
    console.log('🎯 FINAL RESUME BUILDER VALIDATION');
    console.log('==================================');
    
    this.browser = await chromium.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    this.page = await context.newPage();
    this.page.setDefaultTimeout(CONFIG.timeout);
    
    console.log('✅ Browser initialized\n');
  }

  async testAuthentication() {
    console.log('🔐 Step 1: Authentication Test');
    console.log('==============================');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/login`);
      await this.page.waitForLoadState('networkidle');
      
      await this.page.fill('input[type="email"]', CONFIG.testUser.email);
      await this.page.fill('input[type="password"]', CONFIG.testUser.password);
      
      await this.page.click('button:has-text("Sign in")');
      await this.page.waitForTimeout(3000);
      
      const currentUrl = this.page.url();
      this.results.authentication = !currentUrl.includes('/login');
      
      console.log(`✅ Authentication: ${this.results.authentication ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   Current URL: ${currentUrl}\n`);
      
      return this.results.authentication;
    } catch (error) {
      console.log(`❌ Authentication failed: ${error.message}\n`);
      return false;
    }
  }

  async testPageLoad() {
    console.log('📄 Step 2: Resume Builder Page Load');
    console.log('===================================');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForLoadState('networkidle');
      
      const title = await this.page.textContent('h1');
      const hasImportButton = await this.page.isVisible('text=Import from LinkedIn');
      
      this.results.pageLoad = title.includes('Resume Builder') && hasImportButton;
      
      console.log(`✅ Page Load: ${this.results.pageLoad ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   Title: ${title}`);
      console.log(`   Import Button: ${hasImportButton ? 'Found' : 'Missing'}\n`);
      
      return this.results.pageLoad;
    } catch (error) {
      console.log(`❌ Page load failed: ${error.message}\n`);
      return false;
    }
  }

  async testLinkedInImportComplete() {
    console.log('🔗 Step 3: Complete LinkedIn Import Flow');
    console.log('========================================');
    
    try {
      // Get initial resume count
      const initialResumeCount = await this.page.locator('.bg-white.rounded-2xl').count();
      console.log(`   Initial resume count: ${initialResumeCount}`);
      
      // Open LinkedIn import modal
      await this.page.click('text=Import from LinkedIn');
      await this.page.waitForTimeout(1000);
      
      // Fill in data
      await this.page.fill('textarea', LINKEDIN_TEST_DATA);
      await this.page.waitForTimeout(500);
      
      // Click import
      await this.page.click('button:has-text("Import & Create Resume")');
      console.log('   Import button clicked, waiting for completion...');
      
      // Wait for import to complete (up to 30 seconds)
      let importCompleted = false;
      let redirected = false;
      
      for (let i = 0; i < 30; i++) {
        await this.page.waitForTimeout(1000);
        
        const currentUrl = this.page.url();
        
        // Check if redirected to edit page
        if (currentUrl.includes('/resume-builder/edit/')) {
          redirected = true;
          console.log(`   ✅ Redirected to edit page: ${currentUrl}`);
          break;
        }
        
        // Check if modal closed and back on main page
        const modalVisible = await this.page.isVisible('h2:has-text("Import from LinkedIn")').catch(() => false);
        if (!modalVisible && currentUrl.includes('/resume-builder') && !currentUrl.includes('/edit/')) {
          // Check if resume count increased
          const newResumeCount = await this.page.locator('.bg-white.rounded-2xl').count();
          if (newResumeCount > initialResumeCount) {
            importCompleted = true;
            console.log(`   ✅ Resume created! Count increased from ${initialResumeCount} to ${newResumeCount}`);
            break;
          }
        }
        
        console.log(`   ${i + 1}s: Waiting... (URL: ${currentUrl})`);
      }
      
      this.results.linkedinImport = importCompleted || redirected;
      this.results.dataCreation = importCompleted || redirected;
      
      console.log(`✅ LinkedIn Import: ${this.results.linkedinImport ? 'SUCCESS' : 'FAILED'}`);
      console.log(`✅ Data Creation: ${this.results.dataCreation ? 'SUCCESS' : 'FAILED'}\n`);
      
      return this.results.linkedinImport;
    } catch (error) {
      console.log(`❌ LinkedIn import failed: ${error.message}\n`);
      return false;
    }
  }

  async testResumeList() {
    console.log('📋 Step 4: Resume List Verification');
    console.log('===================================');
    
    try {
      // Navigate back to main page if not already there
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/resume-builder') || currentUrl.includes('/edit/')) {
        await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
        await this.page.waitForLoadState('networkidle');
      }
      
      const resumeCount = await this.page.locator('.bg-white.rounded-2xl').count();
      const hasEditButtons = await this.page.locator('text=Edit').count() > 0;
      
      this.results.resumeList = resumeCount > 0 && hasEditButtons;
      
      console.log(`✅ Resume List: ${this.results.resumeList ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   Resume count: ${resumeCount}`);
      console.log(`   Edit buttons: ${hasEditButtons ? 'Present' : 'Missing'}\n`);
      
      return this.results.resumeList;
    } catch (error) {
      console.log(`❌ Resume list verification failed: ${error.message}\n`);
      return false;
    }
  }

  async testEditPageAccess() {
    console.log('✏️ Step 5: Edit Page Access');
    console.log('===========================');
    
    try {
      // Click the first edit button
      const editButton = this.page.locator('text=Edit').first();
      if (await editButton.isVisible()) {
        await editButton.click();
        await this.page.waitForTimeout(3000);
        
        const currentUrl = this.page.url();
        this.results.editPageAccess = currentUrl.includes('/resume-builder/edit/');
        
        console.log(`✅ Edit Page Access: ${this.results.editPageAccess ? 'SUCCESS' : 'FAILED'}`);
        console.log(`   Edit URL: ${currentUrl}\n`);
      } else {
        console.log(`❌ Edit Page Access: FAILED - No edit button found\n`);
      }
      
      return this.results.editPageAccess;
    } catch (error) {
      console.log(`❌ Edit page access failed: ${error.message}\n`);
      return false;
    }
  }

  async generateFinalReport() {
    console.log('📊 FINAL VALIDATION REPORT');
    console.log('==========================');
    
    const testResults = [
      { name: 'Authentication', passed: this.results.authentication },
      { name: 'Page Load', passed: this.results.pageLoad },
      { name: 'LinkedIn Import', passed: this.results.linkedinImport },
      { name: 'Data Creation', passed: this.results.dataCreation },
      { name: 'Resume List', passed: this.results.resumeList },
      { name: 'Edit Page Access', passed: this.results.editPageAccess }
    ];
    
    const passedTests = testResults.filter(test => test.passed).length;
    const totalTests = testResults.length;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log(`\n🎯 SUMMARY:`);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success Rate: ${successRate}%`);
    
    console.log(`\n📋 DETAILED RESULTS:`);
    testResults.forEach(test => {
      const status = test.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${test.name}: ${status}`);
    });
    
    this.results.overallSuccess = passedTests === totalTests;
    
    if (this.results.overallSuccess) {
      console.log('\n🎉 COMPLETE SUCCESS!');
      console.log('===================');
      console.log('✅ Resume Builder is 100% FUNCTIONAL!');
      console.log('✅ All core features work end-to-end');
      console.log('✅ LinkedIn import creates resumes successfully');
      console.log('✅ Data persistence is complete');
      console.log('✅ User interface is fully operational');
      console.log('\n🚀 PRODUCTION READY - VERIFIED BY COMPREHENSIVE E2E TESTING!');
    } else if (successRate >= 80) {
      console.log('\n✅ MOSTLY FUNCTIONAL!');
      console.log('====================');
      console.log('✅ Core functionality works');
      console.log('⚠️  Minor issues detected but system is usable');
    } else {
      console.log('\n❌ SIGNIFICANT ISSUES');
      console.log('====================');
      console.log('❌ Multiple core features failing');
    }
    
    return this.results.overallSuccess;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('\n🧹 Browser closed');
    }
  }

  async runCompleteValidation() {
    try {
      await this.init();
      
      await this.testAuthentication();
      await this.testPageLoad();
      await this.testLinkedInImportComplete();
      await this.testResumeList();
      await this.testEditPageAccess();
      
      const success = await this.generateFinalReport();
      return success;
      
    } catch (error) {
      console.error('❌ Validation failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

async function main() {
  const validator = new FinalValidationTest();
  const success = await validator.runCompleteValidation();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { FinalValidationTest };
