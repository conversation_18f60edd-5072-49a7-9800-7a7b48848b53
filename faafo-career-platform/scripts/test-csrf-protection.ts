#!/usr/bin/env tsx

/**
 * CSRF Protection Testing Script
 * Tests the CSRF protection implementation
 */

import { NextRequest } from 'next/server';
import { SimpleSecurity } from '../src/lib/simple-security';
import { 
  withCSRFProtection, 
  generateCSRFTokenForClient, 
  validateCSRFTokenManual,
  CSRF_CONFIGS 
} from '../src/lib/csrf-middleware';
import { disconnectRedis } from '../src/lib/redis';

// Mock NextRequest for testing
function createMockRequest(
  method: string = 'POST',
  csrfToken?: string,
  sessionId?: string
): NextRequest {
  const url = 'http://localhost:3000/api/test';
  const request = new NextRequest(url, { method });
  
  // Mock headers
  const headers = new Map();
  if (csrfToken) {
    headers.set('x-csrf-token', csrfToken);
  }
  if (sessionId) {
    headers.set('x-session-id', sessionId);
  }
  headers.set('user-agent', 'test-agent');
  headers.set('x-forwarded-for', '127.0.0.1');
  
  Object.defineProperty(request, 'headers', {
    value: headers,
    writable: false,
  });
  
  return request;
}

async function testCSRFTokenGeneration(): Promise<void> {
  console.log('\n🧪 Testing CSRF Token Generation...');
  
  try {
    // Test basic token generation
    const token1 = SimpleSecurity.generateCSRFToken();
    console.log(`✅ Basic token generated: ${token1.substring(0, 20)}...`);
    
    // Test session-bound token generation
    const sessionId = 'test-session-123';
    const token2 = SimpleSecurity.generateCSRFToken(sessionId);
    console.log(`✅ Session-bound token generated: ${token2.substring(0, 20)}...`);
    
    // Test client token generation
    const clientToken = await generateCSRFTokenForClient(sessionId);
    console.log(`✅ Client token generated: ${clientToken.token.substring(0, 20)}...`);
    console.log(`✅ Token expires at: ${new Date(clientToken.expiresAt).toISOString()}`);
    
  } catch (error) {
    console.log(`❌ Token generation failed: ${error}`);
  }
}

async function testCSRFTokenValidation(): Promise<void> {
  console.log('\n🧪 Testing CSRF Token Validation...');
  
  try {
    const sessionId = 'test-session-456';
    
    // Generate and store a token
    const token = SimpleSecurity.generateCSRFToken(sessionId);
    await SimpleSecurity.storeCSRFToken(token, sessionId);
    
    // Test valid token
    const validation1 = await SimpleSecurity.validateCSRFToken(token, sessionId);
    console.log(`✅ Valid token validation: ${validation1.isValid}`);
    
    // Test token reuse (should fail)
    const validation2 = await SimpleSecurity.validateCSRFToken(token, sessionId);
    console.log(`✅ Token reuse validation: ${validation2.isValid} (should be false)`);
    console.log(`   Reason: ${validation2.reason}`);
    
    // Test invalid token
    const validation3 = await SimpleSecurity.validateCSRFToken('invalid-token', sessionId);
    console.log(`✅ Invalid token validation: ${validation3.isValid} (should be false)`);
    console.log(`   Reason: ${validation3.reason}`);
    
    // Test session mismatch
    const token2 = SimpleSecurity.generateCSRFToken('different-session');
    await SimpleSecurity.storeCSRFToken(token2, 'different-session');
    const validation4 = await SimpleSecurity.validateCSRFToken(token2, sessionId);
    console.log(`✅ Session mismatch validation: ${validation4.isValid} (should be false)`);
    console.log(`   Reason: ${validation4.reason}`);
    
  } catch (error) {
    console.log(`❌ Token validation failed: ${error}`);
  }
}

async function testCSRFMiddleware(): Promise<void> {
  console.log('\n🧪 Testing CSRF Middleware...');
  
  try {
    // Create a test handler
    const testHandler = async (req: NextRequest) => {
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    };
    
    // Wrap with CSRF protection
    const protectedHandler = withCSRFProtection(testHandler, CSRF_CONFIGS.HIGH_SECURITY);
    
    // Test 1: GET request (should pass - no CSRF required)
    const getRequest = createMockRequest('GET');
    const getResponse = await protectedHandler(getRequest);
    console.log(`✅ GET request: ${getResponse.status} (should be 200)`);
    
    // Test 2: POST without CSRF token (should fail)
    const postRequest = createMockRequest('POST');
    const postResponse = await protectedHandler(postRequest);
    console.log(`✅ POST without token: ${postResponse.status} (should be 403)`);
    
    // Test 3: POST with invalid CSRF token (should fail)
    const postWithInvalidToken = createMockRequest('POST', 'invalid-token');
    const invalidResponse = await protectedHandler(postWithInvalidToken);
    console.log(`✅ POST with invalid token: ${invalidResponse.status} (should be 403)`);
    
    // Test 4: POST with valid CSRF token (should pass)
    const sessionId = 'test-session-789';
    const validToken = SimpleSecurity.generateCSRFToken(sessionId);
    await SimpleSecurity.storeCSRFToken(validToken, sessionId);
    
    const postWithValidToken = createMockRequest('POST', validToken, sessionId);
    const validResponse = await protectedHandler(postWithValidToken);
    console.log(`✅ POST with valid token: ${validResponse.status} (should be 200)`);
    
  } catch (error) {
    console.log(`❌ Middleware testing failed: ${error}`);
  }
}

async function testCSRFConfigurations(): Promise<void> {
  console.log('\n🧪 Testing CSRF Configurations...');
  
  try {
    // Test different security levels
    const configs = Object.keys(CSRF_CONFIGS);
    console.log(`✅ Available CSRF configs: ${configs.join(', ')}`);
    
    // Test each configuration
    for (const configName of configs) {
      const config = CSRF_CONFIGS[configName as keyof typeof CSRF_CONFIGS];
      console.log(`✅ ${configName}: enabled=${config.enabled}, logViolations=${config.logViolations}`);
    }
    
  } catch (error) {
    console.log(`❌ Configuration testing failed: ${error}`);
  }
}

async function testCSRFTokenExpiration(): Promise<void> {
  console.log('\n🧪 Testing CSRF Token Expiration...');
  
  try {
    // Create a token with short expiration for testing
    const sessionId = 'test-session-expiry';
    const token = SimpleSecurity.generateCSRFToken(sessionId);
    
    // Manually create an expired token by modifying the timestamp
    const parts = token.split('-');
    const expiredTimestamp = (Date.now() - 2 * 60 * 60 * 1000).toString(36); // 2 hours ago
    const expiredToken = `${parts[0]}-${expiredTimestamp}-${parts[2] || ''}`;
    
    // Store the expired token
    await SimpleSecurity.storeCSRFToken(expiredToken, sessionId);
    
    // Test validation of expired token
    const validation = await SimpleSecurity.validateCSRFToken(expiredToken, sessionId);
    console.log(`✅ Expired token validation: ${validation.isValid} (should be false)`);
    console.log(`   Reason: ${validation.reason}`);
    
  } catch (error) {
    console.log(`❌ Token expiration testing failed: ${error}`);
  }
}

async function testCSRFWithDifferentMethods(): Promise<void> {
  console.log('\n🧪 Testing CSRF with Different HTTP Methods...');
  
  const testHandler = async (req: NextRequest) => {
    return new Response(JSON.stringify({ method: req.method, success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  };
  
  const protectedHandler = withCSRFProtection(testHandler, CSRF_CONFIGS.MEDIUM_SECURITY);
  
  const methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'];
  
  for (const method of methods) {
    try {
      const request = createMockRequest(method);
      const response = await protectedHandler(request);
      
      const shouldRequireCSRF = ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method);
      const expectedStatus = shouldRequireCSRF ? 403 : 200;
      
      console.log(`✅ ${method}: ${response.status} (expected: ${expectedStatus})`);
      
    } catch (error) {
      console.log(`❌ ${method}: Error - ${error}`);
    }
  }
}

async function runAllTests(): Promise<void> {
  console.log('🚀 Starting CSRF Protection Tests\n');
  
  try {
    await testCSRFTokenGeneration();
    await testCSRFTokenValidation();
    await testCSRFMiddleware();
    await testCSRFConfigurations();
    await testCSRFTokenExpiration();
    await testCSRFWithDifferentMethods();
    
    console.log('\n✅ All CSRF tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ CSRF tests failed:', error);
  } finally {
    // Clean up Redis connection
    await disconnectRedis();
    console.log('\n🔌 Redis connection closed');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

export {
  runAllTests,
  testCSRFTokenGeneration,
  testCSRFTokenValidation,
  testCSRFMiddleware,
};
