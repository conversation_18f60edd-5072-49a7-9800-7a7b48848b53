#!/usr/bin/env tsx

/**
 * CSRF Protection Audit Script
 * Analyzes all API routes to identify CSRF protection status
 */

import fs from 'fs';
import path from 'path';

interface RouteAnalysis {
  file: string;
  methods: string[];
  hasCSRFProtection: boolean;
  csrfMethod: string;
  requiresCSRF: boolean;
  securityLevel: 'HIGH' | 'MEDIUM' | 'LOW';
  issues: string[];
}

class CSRFAuditor {
  private routes: RouteAnalysis[] = [];
  private apiDir = path.join(__dirname, '../src/app/api');

  async auditAllRoutes(): Promise<void> {
    console.log('🔍 Starting CSRF Protection Audit...\n');
    
    const routeFiles = this.findAllRouteFiles();
    
    for (const file of routeFiles) {
      const analysis = await this.analyzeRoute(file);
      this.routes.push(analysis);
    }
    
    this.generateReport();
  }

  private findAllRouteFiles(): string[] {
    const files: string[] = [];
    
    const scanDirectory = (dir: string): void => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scanDirectory(fullPath);
        } else if (item === 'route.ts') {
          files.push(fullPath);
        }
      }
    };
    
    scanDirectory(this.apiDir);
    return files;
  }

  private async analyzeRoute(filePath: string): Promise<RouteAnalysis> {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(this.apiDir, filePath);
    
    const analysis: RouteAnalysis = {
      file: relativePath,
      methods: this.extractMethods(content),
      hasCSRFProtection: false,
      csrfMethod: 'none',
      requiresCSRF: false,
      securityLevel: 'LOW',
      issues: []
    };

    // Check for state-changing methods
    const stateChangingMethods = analysis.methods.filter(m => 
      ['POST', 'PUT', 'DELETE', 'PATCH'].includes(m)
    );
    
    if (stateChangingMethods.length > 0) {
      analysis.requiresCSRF = true;
      analysis.securityLevel = this.determineSecurityLevel(relativePath);
    }

    // Check for CSRF protection patterns
    analysis.hasCSRFProtection = this.checkCSRFProtection(content);
    analysis.csrfMethod = this.identifyCSRFMethod(content);

    // Special check for routes that have CSRF protection but audit doesn't detect properly
    if (!analysis.hasCSRFProtection && this.hasAnyCSRFProtection(content)) {
      analysis.hasCSRFProtection = true;
      analysis.csrfMethod = 'detected-but-unidentified';
    }

    // Identify issues
    if (analysis.requiresCSRF && !analysis.hasCSRFProtection) {
      analysis.issues.push('Missing CSRF protection for state-changing operations');
    }

    if (analysis.hasCSRFProtection && !analysis.requiresCSRF) {
      analysis.issues.push('CSRF protection on GET-only endpoint (unnecessary)');
    }

    return analysis;
  }

  private extractMethods(content: string): string[] {
    const methods: string[] = [];
    const methodRegex = /export\s+async\s+function\s+(GET|POST|PUT|DELETE|PATCH|OPTIONS|HEAD)/g;
    let match;
    
    while ((match = methodRegex.exec(content)) !== null) {
      methods.push(match[1]);
    }
    
    return methods;
  }

  private checkCSRFProtection(content: string): boolean {
    const csrfPatterns = [
      /withCSRFProtection/,
      /x-csrf-token/i,
      /csrf-token/i,
      /validateCSRFToken/,
      /requireCSRF.*true/,
      /SecurityMiddleware\.protect/,
      /withSimpleSecurity.*requireCSRF/,
      /CSRF_CONFIGS/,
      /withSecurityProtection/,
    ];

    return csrfPatterns.some(pattern => pattern.test(content));
  }

  private hasAnyCSRFProtection(content: string): boolean {
    // More comprehensive check for any form of CSRF protection
    const patterns = [
      /withCSRFProtection\s*\(/,
      /withSecurityProtection\s*\(/,
      /withSimpleSecurity\s*\([^)]*requireCSRF/,
      /CSRF_CONFIGS\.(HIGH|MEDIUM|LOW)_SECURITY/,
      /csrf.*protection/i,
      /csrf.*middleware/i,
    ];

    return patterns.some(pattern => pattern.test(content));
  }

  private identifyCSRFMethod(content: string): string {
    if (content.includes('withCSRFProtection')) return 'withCSRFProtection';
    if (content.includes('withSecurityProtection')) return 'withSecurityProtection';
    if (content.includes('SecurityMiddleware.protect')) return 'SecurityMiddleware';
    if (content.includes('withSimpleSecurity')) return 'withSimpleSecurity';
    if (content.includes('CSRF_CONFIGS')) return 'CSRF_CONFIGS';
    if (content.includes('x-csrf-token')) return 'manual-header-check';
    if (content.includes('validateCSRFToken')) return 'manual-validation';
    return 'none';
  }

  private determineSecurityLevel(routePath: string): 'HIGH' | 'MEDIUM' | 'LOW' {
    // High security routes (critical operations)
    if (routePath.includes('admin/') ||
        routePath.includes('auth/') ||
        routePath.includes('profile/') ||
        routePath.includes('payment/') ||
        routePath.includes('errors/') ||
        routePath.includes('freedom-fund/')) {
      return 'HIGH';
    }

    // Medium security routes (user data operations)
    if (routePath.includes('forum/') ||
        routePath.includes('assessment/') ||
        routePath.includes('goals/') ||
        routePath.includes('achievements/') ||
        routePath.includes('learning-progress/') ||
        routePath.includes('progress-tracker/') ||
        routePath.includes('resource-ratings/') ||
        routePath.includes('career-paths/') ||
        routePath.includes('learning-paths/')) {
      return 'MEDIUM';
    }

    return 'LOW';
  }

  private generateReport(): void {
    console.log('📊 CSRF Protection Audit Report');
    console.log('='.repeat(50));
    
    const totalRoutes = this.routes.length;
    const routesWithStateChanging = this.routes.filter(r => r.requiresCSRF).length;
    const protectedRoutes = this.routes.filter(r => r.hasCSRFProtection && r.requiresCSRF).length;
    const unprotectedRoutes = this.routes.filter(r => r.requiresCSRF && !r.hasCSRFProtection).length;
    
    console.log(`\n📈 Summary:`);
    console.log(`Total API routes: ${totalRoutes}`);
    console.log(`Routes with state-changing methods: ${routesWithStateChanging}`);
    console.log(`Protected routes: ${protectedRoutes}`);
    console.log(`Unprotected routes: ${unprotectedRoutes}`);
    console.log(`Protection coverage: ${routesWithStateChanging > 0 ? Math.round((protectedRoutes / routesWithStateChanging) * 100) : 0}%`);

    // Group by security level
    const highSecurity = this.routes.filter(r => r.securityLevel === 'HIGH');
    const mediumSecurity = this.routes.filter(r => r.securityLevel === 'MEDIUM');
    const lowSecurity = this.routes.filter(r => r.securityLevel === 'LOW');

    console.log(`\n🔒 Security Level Breakdown:`);
    console.log(`High security routes: ${highSecurity.length}`);
    console.log(`Medium security routes: ${mediumSecurity.length}`);
    console.log(`Low security routes: ${lowSecurity.length}`);

    // Show unprotected routes by priority
    console.log(`\n❌ Unprotected Routes (Requires Immediate Attention):`);
    const unprotected = this.routes.filter(r => r.requiresCSRF && !r.hasCSRFProtection);
    
    if (unprotected.length === 0) {
      console.log('✅ All state-changing routes are protected!');
    } else {
      unprotected
        .sort((a, b) => {
          const priority = { HIGH: 3, MEDIUM: 2, LOW: 1 };
          return priority[b.securityLevel] - priority[a.securityLevel];
        })
        .forEach(route => {
          console.log(`  🚨 ${route.securityLevel}: ${route.file}`);
          console.log(`     Methods: ${route.methods.join(', ')}`);
          console.log(`     Issues: ${route.issues.join(', ')}`);
          console.log('');
        });
    }

    // Show protected routes
    console.log(`\n✅ Protected Routes:`);
    const csrfProtectedRoutes = this.routes.filter(r => r.hasCSRFProtection && r.requiresCSRF);
    csrfProtectedRoutes.forEach(route => {
      console.log(`  ✅ ${route.file} (${route.csrfMethod})`);
    });

    // Show CSRF methods used
    console.log(`\n🛡️  CSRF Protection Methods in Use:`);
    const methods = [...new Set(this.routes.filter(r => r.hasCSRFProtection).map(r => r.csrfMethod))];
    methods.forEach(method => {
      const count = this.routes.filter(r => r.csrfMethod === method).length;
      console.log(`  ${method}: ${count} routes`);
    });

    // Recommendations
    console.log(`\n💡 Recommendations:`);
    if (unprotectedRoutes > 0) {
      console.log(`  1. Add CSRF protection to ${unprotectedRoutes} unprotected routes`);
      console.log(`  2. Prioritize HIGH security routes first`);
      console.log(`  3. Use consistent CSRF protection method across all routes`);
    }
    
    if (methods.length > 2) {
      console.log(`  4. Consider standardizing on one CSRF protection method`);
    }
    
    console.log(`  5. Test CSRF protection after implementation`);
    console.log(`  6. Add CSRF token rotation for enhanced security`);
  }
}

// Run audit if script is executed directly
if (require.main === module) {
  const auditor = new CSRFAuditor();
  auditor.auditAllRoutes().catch(console.error);
}

export { CSRFAuditor };
