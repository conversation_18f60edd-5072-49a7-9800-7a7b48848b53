const fs = require('fs');
const path = require('path');

// Read the current schema
const schemaPath = path.join(__dirname, '../prisma/schema.prisma');
let schema = fs.readFileSync(schemaPath, 'utf8');

// Add UserRole enum before the existing enums
const userRoleEnum = `
enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}
`;

// Find the first enum and insert UserRole before it
const firstEnumIndex = schema.indexOf('enum ExperienceLevel');
if (firstEnumIndex !== -1) {
  schema = schema.slice(0, firstEnumIndex) + userRoleEnum + '\n' + schema.slice(firstEnumIndex);
}

// Add role field to User model after lockedUntil
const userModelStart = schema.indexOf('model User {');
const lockedUntilLine = schema.indexOf('lockedUntil          DateTime?');
const nextLineIndex = schema.indexOf('\n', lockedUntilLine) + 1;

// Insert the role field
const roleField = '  role                 UserRole                   @default(USER)\n';
schema = schema.slice(0, nextLineIndex) + roleField + schema.slice(nextLineIndex);

// Write the updated schema
fs.writeFileSync(schemaPath, schema);

console.log('✅ Schema updated successfully!');
console.log('Added:');
console.log('- UserRole enum (USER, ADMIN, SUPER_ADMIN)');
console.log('- role field to User model with default USER');
