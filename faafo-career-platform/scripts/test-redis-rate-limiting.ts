#!/usr/bin/env tsx

/**
 * Test script for Redis-based rate limiting implementation
 * Tests both Redis and memory fallback functionality
 */

import { NextRequest } from 'next/server';
import { RateLimiter, rateLimiters, withRateLimit } from '../src/lib/rate-limit';
import { rateLimit, rateLimitConfigs } from '../src/lib/rateLimit';
import { getRedisStatus, getRedisClient, disconnectRedis } from '../src/lib/redis';
import { SimpleSecurity } from '../src/lib/simple-security';

// Test configuration
const TEST_CONFIG = {
  requests: 5,
  windowMs: 10000, // 10 seconds for faster testing
};

// Mock NextRequest for testing
function createMockRequest(ip: string = '127.0.0.1'): NextRequest {
  const url = 'http://localhost:3000/api/test';
  const request = new NextRequest(url);
  
  // Mock headers
  Object.defineProperty(request, 'headers', {
    value: new Map([
      ['x-forwarded-for', ip],
      ['user-agent', 'test-agent'],
    ]),
    writable: false,
  });
  
  return request;
}

async function testRedisConnection(): Promise<boolean> {
  console.log('\n🔍 Testing Redis Connection...');
  
  const status = getRedisStatus();
  console.log(`Redis Status: ${status.status}`);
  console.log(`Connected: ${status.connected}`);
  
  if (status.error) {
    console.log(`Error: ${status.error}`);
  }
  
  try {
    const client = await getRedisClient();
    if (client) {
      await client.ping();
      console.log('✅ Redis connection successful');
      return true;
    } else {
      console.log('⚠️  Redis not available, will test memory fallback');
      return false;
    }
  } catch (error) {
    console.log('❌ Redis connection failed:', error);
    return false;
  }
}

async function testRateLimiterClass(): Promise<void> {
  console.log('\n🧪 Testing RateLimiter Class...');
  
  const rateLimiter = new RateLimiter(TEST_CONFIG);
  const request = createMockRequest('192.168.1.100');
  
  console.log('Testing rate limiting with 5 requests...');
  
  for (let i = 1; i <= 7; i++) {
    const result = await rateLimiter.check(request);
    console.log(`Request ${i}: allowed=${result.allowed}, remaining=${result.remaining}, limit=${result.limit}`);
    
    if (i === 5) {
      console.log('✅ Rate limit should be reached at request 5');
    }
    if (i > 5 && !result.allowed) {
      console.log('✅ Rate limiting working correctly');
    }
  }
}

async function testRateLimitFunction(): Promise<void> {
  console.log('\n🧪 Testing rateLimit Function...');
  
  const rateLimitMiddleware = rateLimit({
    windowMs: TEST_CONFIG.windowMs,
    maxRequests: TEST_CONFIG.requests,
    message: 'Test rate limit exceeded',
  });
  
  const request = createMockRequest('192.168.1.101');
  
  console.log('Testing rate limiting middleware...');
  
  for (let i = 1; i <= 7; i++) {
    const result = await rateLimitMiddleware(request);
    
    if (result) {
      console.log(`Request ${i}: Rate limited (${result.status})`);
    } else {
      console.log(`Request ${i}: Allowed`);
    }
  }
}

async function testSimpleSecurityRateLimit(): Promise<void> {
  console.log('\n🧪 Testing SimpleSecurity Rate Limiting...');
  
  const identifier = 'test-user-192.168.1.102';
  
  console.log('Testing SimpleSecurity rate limiting...');
  
  for (let i = 1; i <= 7; i++) {
    const allowed = await SimpleSecurity.checkRateLimitAsync(
      identifier,
      TEST_CONFIG.requests,
      TEST_CONFIG.windowMs
    );
    
    console.log(`Request ${i}: allowed=${allowed}`);
  }
}

async function testWithRateLimitMiddleware(): Promise<void> {
  console.log('\n🧪 Testing withRateLimit Middleware...');
  
  const rateLimiter = new RateLimiter(TEST_CONFIG);
  const middleware = withRateLimit(rateLimiter);
  const request = createMockRequest('192.168.1.103');
  
  console.log('Testing middleware wrapper...');
  
  for (let i = 1; i <= 3; i++) {
    const result = await middleware(request);
    console.log(`Request ${i}:`, {
      allowed: result.allowed,
      remaining: result.remaining,
      backend: result.headers['X-RateLimit-Backend'],
    });
  }
}

async function testPredefinedRateLimiters(): Promise<void> {
  console.log('\n🧪 Testing Predefined Rate Limiters...');
  
  const request = createMockRequest('192.168.1.104');
  
  // Test auth rate limiter
  console.log('Testing auth rate limiter...');
  const authResult = await rateLimiters.auth.check(request);
  console.log('Auth rate limiter:', authResult);
  
  // Test API rate limiter
  console.log('Testing API rate limiter...');
  const apiResult = await rateLimiters.api.check(request);
  console.log('API rate limiter:', apiResult);
}

async function testConcurrentRequests(): Promise<void> {
  console.log('\n🧪 Testing Concurrent Requests...');
  
  const rateLimiter = new RateLimiter({
    requests: 3,
    windowMs: 5000,
  });
  
  const requests = Array.from({ length: 5 }, (_, i) => 
    createMockRequest(`192.168.1.${200 + i}`)
  );
  
  console.log('Testing 5 concurrent requests from different IPs...');
  
  const promises = requests.map(async (request, index) => {
    const result = await rateLimiter.check(request);
    return { index: index + 1, ...result };
  });
  
  const results = await Promise.all(promises);
  results.forEach(result => {
    console.log(`Concurrent request ${result.index}: allowed=${result.allowed}, remaining=${result.remaining}`);
  });
}

async function testRedisKeyExpiration(): Promise<void> {
  console.log('\n🧪 Testing Redis Key Expiration...');
  
  const client = await getRedisClient();
  if (!client) {
    console.log('⚠️  Redis not available, skipping expiration test');
    return;
  }
  
  const testKey = 'test_rate_limit:expiration_test';
  
  // Set a key with short expiration
  await client.hmset(testKey, 'count', '1', 'resetTime', (Date.now() + 2000).toString());
  await client.pexpire(testKey, 2000);
  
  console.log('Set test key with 2-second expiration');
  
  // Check if key exists
  const exists1 = await client.exists(testKey);
  console.log(`Key exists immediately: ${exists1 === 1}`);
  
  // Wait 3 seconds
  console.log('Waiting 3 seconds for expiration...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Check if key still exists
  const exists2 = await client.exists(testKey);
  console.log(`Key exists after expiration: ${exists2 === 1}`);
  
  if (exists2 === 0) {
    console.log('✅ Redis key expiration working correctly');
  } else {
    console.log('❌ Redis key expiration not working');
  }
}

async function runAllTests(): Promise<void> {
  console.log('🚀 Starting Redis Rate Limiting Tests\n');
  
  try {
    // Test Redis connection
    const redisAvailable = await testRedisConnection();
    
    // Run all tests
    await testRateLimiterClass();
    await testRateLimitFunction();
    await testSimpleSecurityRateLimit();
    await testWithRateLimitMiddleware();
    await testPredefinedRateLimiters();
    await testConcurrentRequests();
    
    if (redisAvailable) {
      await testRedisKeyExpiration();
    }
    
    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  } finally {
    // Clean up Redis connection
    await disconnectRedis();
    console.log('\n🔌 Redis connection closed');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

export {
  runAllTests,
  testRedisConnection,
  testRateLimiterClass,
  testRateLimitFunction,
  testSimpleSecurityRateLimit,
};
