#!/usr/bin/env node

/**
 * TESTERAT - Complete Browser Automation for Resume Builder
 * Tests all functionality with full browser automation
 */

const { chromium } = require('playwright');

const CONFIG = {
  baseUrl: 'http://localhost:3003',
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  timeout: 30000,
  headless: false, // Set to true for CI/CD
  slowMo: 300
};

const LINKEDIN_TEST_DATA = `
<PERSON>
Senior Software Engineer at Tech Corp
San Francisco, CA
<EMAIL>
linkedin.com/in/johndoe

About:
Experienced software engineer with 7+ years developing scalable web applications using React, Node.js, Python, and AWS. Led teams of 5+ developers and delivered products serving 500k+ users.

Experience:
Senior Software Engineer - Tech Corp (2021 - Present)
• Architected and built microservices handling 1M+ daily requests
• Led migration from monolith to microservices, reducing deployment time by 80%
• Mentored 5 junior developers and established code review best practices

Software Engineer - StartupXYZ (2019 - 2021)
• Developed React applications with 99.9% uptime serving 100k+ users
• Built RESTful APIs using Node.js and PostgreSQL

Education:
Master of Science in Computer Science
Stanford University (2015 - 2017)
GPA: 3.8

Bachelor of Science in Computer Science
University of California, Berkeley (2011 - 2015)
GPA: 3.6

Skills:
Programming Languages: JavaScript, Python, Java, TypeScript, Go
Frontend: React, Vue.js, Angular, HTML5, CSS3, Sass
Backend: Node.js, Express, Django, Flask, Spring Boot
Databases: PostgreSQL, MongoDB, Redis, MySQL
Cloud: AWS, Google Cloud, Azure, Docker, Kubernetes
`;

class BrowserAutomationTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      serverHealth: false,
      authentication: false,
      pageAccess: false,
      linkedinImport: false,
      dataVerification: false,
      endToEndFlow: false
    };
  }

  async init() {
    console.log('🚀 Initializing Browser Automation Test...');
    
    this.browser = await chromium.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 },
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    });
    
    this.page = await context.newPage();
    this.page.setDefaultTimeout(CONFIG.timeout);
    
    console.log('✅ Browser initialized');
  }

  async testServerHealth() {
    console.log('\n🏥 Testing Server Health...');
    
    try {
      // Test if server is responding
      const response = await this.page.request.get(`${CONFIG.baseUrl}/api/ai/resume-parsing`);
      console.log(`   API Health Status: ${response.status()}`);
      
      if (response.ok()) {
        const data = await response.json();
        console.log('✅ Server is healthy');
        console.log(`   Supported formats: ${data.supportedFormats?.join(', ')}`);
        this.results.serverHealth = true;
        return true;
      } else {
        console.log('❌ Server health check failed');
        return false;
      }
    } catch (error) {
      console.log(`❌ Server health error: ${error.message}`);
      return false;
    }
  }

  async authenticateUser() {
    console.log('\n🔐 Testing User Authentication...');
    
    try {
      // Navigate to login page
      await this.page.goto(`${CONFIG.baseUrl}/login`);
      await this.page.waitForLoadState('networkidle');
      
      console.log('✅ Login page loaded');
      
      // Fill login form
      await this.page.fill('input[name="email"], input[type="email"]', CONFIG.testUser.email);
      await this.page.fill('input[name="password"], input[type="password"]', CONFIG.testUser.password);
      
      console.log('✅ Login credentials entered');
      
      // Submit form and wait for navigation
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle' }),
        this.page.click('button[type="submit"], input[type="submit"]')
      ]);
      
      // Verify authentication success
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/login')) {
        console.log('✅ Authentication successful');
        console.log(`   Redirected to: ${currentUrl}`);
        this.results.authentication = true;
        return true;
      } else {
        console.log('❌ Authentication failed - still on login page');
        return false;
      }
    } catch (error) {
      console.log(`❌ Authentication error: ${error.message}`);
      return false;
    }
  }

  async testResumeBuilderAccess() {
    console.log('\n📄 Testing Resume Builder Access...');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForLoadState('networkidle');
      
      // Wait for page content to load
      await this.page.waitForSelector('h1, [data-testid="page-title"]', { timeout: 10000 });
      
      const title = await this.page.textContent('h1, [data-testid="page-title"]');
      console.log(`✅ Page loaded with title: "${title}"`);
      
      // Check for key elements
      const importButtonVisible = await this.page.isVisible('text=Import from LinkedIn').catch(() => false) ||
                                 await this.page.isVisible('button:has-text("Import")').catch(() => false);
      
      const uploadButtonVisible = await this.page.isVisible('text=Upload Existing').catch(() => false) ||
                                  await this.page.isVisible('button:has-text("Upload")').catch(() => false);
      
      console.log(`   LinkedIn Import button: ${importButtonVisible ? '✅' : '❌'}`);
      console.log(`   Upload button: ${uploadButtonVisible ? '✅' : '❌'}`);
      
      this.results.pageAccess = importButtonVisible || uploadButtonVisible;
      return this.results.pageAccess;
    } catch (error) {
      console.log(`❌ Page access error: ${error.message}`);
      return false;
    }
  }

  async testLinkedInImportFlow() {
    console.log('\n🔗 Testing LinkedIn Import Flow...');
    
    try {
      // Find and click LinkedIn import button
      const importSelectors = [
        'text=Import from LinkedIn',
        'button:has-text("Import")',
        'button:has-text("LinkedIn")',
        '[data-testid="linkedin-import"]'
      ];
      
      let clicked = false;
      for (const selector of importSelectors) {
        try {
          if (await this.page.isVisible(selector)) {
            await this.page.click(selector);
            clicked = true;
            console.log(`✅ Clicked import button: ${selector}`);
            break;
          }
        } catch (e) {
          // Continue to next selector
        }
      }
      
      if (!clicked) {
        console.log('❌ Could not find LinkedIn import button');
        return false;
      }
      
      // Wait for modal/form to appear
      await this.page.waitForTimeout(1000);
      
      // Find and fill textarea
      const textareaVisible = await this.page.isVisible('textarea');
      if (!textareaVisible) {
        console.log('❌ LinkedIn import form not opened');
        return false;
      }
      
      console.log('✅ LinkedIn import form opened');
      
      await this.page.fill('textarea', LINKEDIN_TEST_DATA);
      console.log('✅ LinkedIn data entered');
      
      // Find and click submit button
      const submitSelectors = [
        'button:has-text("Import")',
        'button:has-text("Submit")',
        'button[type="submit"]',
        'button:has-text("Create")'
      ];
      
      let submitted = false;
      for (const selector of submitSelectors) {
        try {
          if (await this.page.isVisible(selector)) {
            await Promise.all([
              this.page.waitForNavigation({ waitUntil: 'networkidle', timeout: 30000 }),
              this.page.click(selector)
            ]);
            submitted = true;
            console.log(`✅ Submitted with button: ${selector}`);
            break;
          }
        } catch (e) {
          // Continue to next selector
        }
      }
      
      if (!submitted) {
        console.log('❌ Could not submit LinkedIn import');
        return false;
      }
      
      // Verify redirect to edit page
      const currentUrl = this.page.url();
      if (currentUrl.includes('/resume-builder/edit/')) {
        console.log('✅ Successfully redirected to edit page');
        console.log(`   Edit URL: ${currentUrl}`);
        this.results.linkedinImport = true;
        return true;
      } else {
        console.log(`❌ Not redirected to edit page. Current URL: ${currentUrl}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ LinkedIn import error: ${error.message}`);
      return false;
    }
  }

  async testDataPersistence() {
    console.log('\n🔍 Testing Data Persistence...');
    
    try {
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/resume-builder/edit/')) {
        console.log('❌ Not on edit page, skipping data verification');
        return false;
      }
      
      // Wait for page to fully load
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(3000);
      
      // Check personal information fields
      const firstName = await this.page.inputValue('input[name="firstName"]').catch(() => '');
      const lastName = await this.page.inputValue('input[name="lastName"]').catch(() => '');
      const email = await this.page.inputValue('input[name="email"]').catch(() => '');
      
      console.log(`   First Name: "${firstName}"`);
      console.log(`   Last Name: "${lastName}"`);
      console.log(`   Email: "${email}"`);
      
      const hasPersonalInfo = firstName && lastName && email;
      console.log(`   Personal Info: ${hasPersonalInfo ? '✅' : '❌'}`);
      
      // Check for content in the page
      const pageContent = await this.page.textContent('body');
      const hasExperience = pageContent.includes('Tech Corp') || pageContent.includes('Software Engineer');
      const hasEducation = pageContent.includes('Stanford') || pageContent.includes('Berkeley');
      const hasSkills = pageContent.includes('JavaScript') || pageContent.includes('React');
      
      console.log(`   Experience Data: ${hasExperience ? '✅' : '❌'}`);
      console.log(`   Education Data: ${hasEducation ? '✅' : '❌'}`);
      console.log(`   Skills Data: ${hasSkills ? '✅' : '❌'}`);
      
      const hasCompleteData = hasPersonalInfo && (hasExperience || hasEducation || hasSkills);
      this.results.dataVerification = hasCompleteData;
      
      if (hasCompleteData) {
        console.log('✅ Data persistence verification successful');
        this.results.endToEndFlow = true;
      } else {
        console.log('❌ Data persistence verification failed');
      }
      
      return hasCompleteData;
    } catch (error) {
      console.log(`❌ Data verification error: ${error.message}`);
      return false;
    }
  }

  async generateReport() {
    console.log('\n📊 BROWSER AUTOMATION TEST RESULTS');
    console.log('===================================');
    
    const results = this.results;
    const passCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`Server Health: ${results.serverHealth ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Authentication: ${results.authentication ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Page Access: ${results.pageAccess ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`LinkedIn Import: ${results.linkedinImport ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Data Verification: ${results.dataVerification ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`End-to-End Flow: ${results.endToEndFlow ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);
    
    if (passCount === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('✅ Resume Builder is 100% FUNCTIONAL!');
      console.log('✅ Complete end-to-end flow works');
      console.log('✅ Data persistence is verified');
      console.log('✅ Authentication works properly');
      console.log('✅ All APIs are operational');
      console.log('\n🚀 PRODUCTION READY - VERIFIED BY AUTOMATION!');
    } else if (passCount >= 4) {
      console.log('\n✅ CORE FUNCTIONALITY VERIFIED!');
      console.log('⚠️  Minor issues detected but main flow works');
    } else {
      console.log('\n❌ Critical functionality issues detected');
    }
    
    return passCount === totalTests;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('\n🧹 Browser closed');
    }
  }

  async runFullTest() {
    try {
      await this.init();
      
      const serverHealthy = await this.testServerHealth();
      if (!serverHealthy) {
        console.log('❌ Server not healthy, cannot proceed');
        return false;
      }
      
      const authenticated = await this.authenticateUser();
      if (!authenticated) {
        console.log('❌ Authentication failed, cannot proceed');
        return false;
      }
      
      await this.testResumeBuilderAccess();
      await this.testLinkedInImportFlow();
      await this.testDataPersistence();
      
      const allPassed = await this.generateReport();
      return allPassed;
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

async function main() {
  console.log('🎯 RESUME BUILDER - COMPLETE BROWSER AUTOMATION TEST');
  console.log('===================================================');
  
  const tester = new BrowserAutomationTest();
  const success = await tester.runFullTest();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { BrowserAutomationTest };
