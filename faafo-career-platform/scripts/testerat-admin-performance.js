#!/usr/bin/env node

/**
 * 🐭 TESTERAT - Admin Performance Testing Suite
 * 
 * Performance testing for admin system implementation
 * Tests response times, concurrent access, memory usage, and scalability
 */

const { PrismaClient, UserRole } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Test configuration
const TEST_CONFIG = {
  performanceThresholds: {
    adminCheck: 100, // ms
    roleRetrieval: 50, // ms
    databaseQuery: 200, // ms
    bulkOperations: 1000 // ms
  },
  loadTest: {
    concurrentUsers: 10,
    operationsPerUser: 5,
    maxAcceptableTime: 500 // ms
  },
  stressTest: {
    userCount: 100,
    adminCount: 10,
    iterations: 50
  }
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
  details: [],
  performanceMetrics: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '🔍',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    perf: '⚡'
  }[type];
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function addTestResult(testName, passed, details = '', error = null, metrics = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`${testName}: PASSED ${details}`, 'success');
  } else {
    testResults.failed++;
    log(`${testName}: FAILED ${details}`, 'error');
    if (error) {
      testResults.errors.push({ test: testName, error: error.message });
    }
  }
  
  testResults.details.push({
    test: testName,
    passed,
    details,
    metrics,
    timestamp: new Date().toISOString()
  });
  
  if (metrics) {
    testResults.performanceMetrics.push({
      test: testName,
      ...metrics
    });
  }
}

// Performance measurement utilities
function measureTime(fn) {
  return async (...args) => {
    const start = process.hrtime.bigint();
    const result = await fn(...args);
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // Convert to milliseconds
    return { result, duration };
  };
}

function measureMemory() {
  const usage = process.memoryUsage();
  return {
    rss: Math.round(usage.rss / 1024 / 1024), // MB
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
    external: Math.round(usage.external / 1024 / 1024) // MB
  };
}

// Performance test functions
async function testAdminCheckPerformance() {
  log('Testing admin check performance...', 'perf');
  
  const testUsers = [];
  
  try {
    // Create test users
    for (let i = 0; i < 10; i++) {
      const user = await prisma.user.create({
        data: {
          email: `testerat-perf-${i}@example.com`,
          password: await bcrypt.hash('testpassword123', 12),
          name: `Performance Test User ${i}`,
          role: i % 3 === 0 ? UserRole.ADMIN : UserRole.USER,
          emailVerified: new Date()
        }
      });
      testUsers.push(user);
    }
    
    const { isUserAdmin } = require('../src/lib/auth.tsx');
    const measuredIsUserAdmin = measureTime(isUserAdmin);
    
    // Test 1: Single admin check performance
    const { result: isAdmin, duration } = await measuredIsUserAdmin(testUsers[0].id);
    
    addTestResult(
      'Performance: Single admin check',
      duration < TEST_CONFIG.performanceThresholds.adminCheck,
      `${duration.toFixed(2)}ms (threshold: ${TEST_CONFIG.performanceThresholds.adminCheck}ms)`,
      null,
      { operation: 'admin_check', duration, threshold: TEST_CONFIG.performanceThresholds.adminCheck }
    );
    
    // Test 2: Bulk admin checks performance
    const startTime = Date.now();
    const results = await Promise.all(
      testUsers.map(user => isUserAdmin(user.id))
    );
    const bulkDuration = Date.now() - startTime;
    
    addTestResult(
      'Performance: Bulk admin checks',
      bulkDuration < TEST_CONFIG.performanceThresholds.bulkOperations,
      `${bulkDuration}ms for ${testUsers.length} checks (${(bulkDuration/testUsers.length).toFixed(2)}ms avg)`,
      null,
      { operation: 'bulk_admin_check', duration: bulkDuration, count: testUsers.length }
    );
    
    // Test 3: Concurrent admin checks
    const concurrentStart = Date.now();
    const concurrentPromises = [];
    
    for (let i = 0; i < TEST_CONFIG.loadTest.concurrentUsers; i++) {
      const userIndex = i % testUsers.length;
      concurrentPromises.push(isUserAdmin(testUsers[userIndex].id));
    }
    
    await Promise.all(concurrentPromises);
    const concurrentDuration = Date.now() - concurrentStart;
    
    addTestResult(
      'Performance: Concurrent admin checks',
      concurrentDuration < TEST_CONFIG.loadTest.maxAcceptableTime,
      `${concurrentDuration}ms for ${TEST_CONFIG.loadTest.concurrentUsers} concurrent checks`,
      null,
      { operation: 'concurrent_admin_check', duration: concurrentDuration, concurrency: TEST_CONFIG.loadTest.concurrentUsers }
    );
    
  } catch (error) {
    addTestResult(
      'Performance: Admin check setup',
      false,
      'Failed to create test users for performance testing',
      error
    );
  }
  
  return testUsers;
}

async function testDatabasePerformance() {
  log('Testing database performance...', 'perf');
  
  try {
    // Test 4: User role query performance
    const startMemory = measureMemory();
    
    const { getUserRole } = require('../src/lib/auth.tsx');
    const measuredGetUserRole = measureTime(getUserRole);
    
    // Create a test user for role queries
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('testpassword123', 12),
        name: 'DB Performance Test User',
        role: UserRole.ADMIN,
        emailVerified: new Date()
      }
    });
    
    const { result: role, duration } = await measuredGetUserRole(testUser.id);
    
    addTestResult(
      'Performance: User role query',
      duration < TEST_CONFIG.performanceThresholds.roleRetrieval,
      `${duration.toFixed(2)}ms (threshold: ${TEST_CONFIG.performanceThresholds.roleRetrieval}ms)`,
      null,
      { operation: 'role_query', duration, threshold: TEST_CONFIG.performanceThresholds.roleRetrieval }
    );
    
    // Test 5: Database connection performance
    const connectionStart = Date.now();
    await prisma.user.count();
    const connectionDuration = Date.now() - connectionStart;
    
    addTestResult(
      'Performance: Database connection',
      connectionDuration < TEST_CONFIG.performanceThresholds.databaseQuery,
      `${connectionDuration}ms for simple query`,
      null,
      { operation: 'db_connection', duration: connectionDuration }
    );
    
    // Test 6: Memory usage during operations
    const endMemory = measureMemory();
    const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed;
    
    addTestResult(
      'Performance: Memory usage',
      memoryIncrease < 50, // Less than 50MB increase
      `Memory increase: ${memoryIncrease}MB (Start: ${startMemory.heapUsed}MB, End: ${endMemory.heapUsed}MB)`,
      null,
      { operation: 'memory_usage', startMemory, endMemory, increase: memoryIncrease }
    );
    
    // Cleanup
    await prisma.user.delete({ where: { id: testUser.id } });
    
  } catch (error) {
    addTestResult(
      'Performance: Database operations',
      false,
      'Failed to test database performance',
      error
    );
  }
}

async function testStressLoad() {
  log('Testing stress load performance...', 'perf');
  
  const createdUsers = [];
  
  try {
    // Test 7: Create many users quickly
    const userCreationStart = Date.now();
    
    const userPromises = [];
    for (let i = 0; i < TEST_CONFIG.stressTest.userCount; i++) {
      const isAdmin = i < TEST_CONFIG.stressTest.adminCount;
      userPromises.push(
        prisma.user.create({
          data: {
            email: `testerat-stress-${i}@example.com`,
            password: await bcrypt.hash('testpassword123', 12),
            name: `Stress Test User ${i}`,
            role: isAdmin ? UserRole.ADMIN : UserRole.USER,
            emailVerified: new Date()
          }
        })
      );
    }
    
    const users = await Promise.all(userPromises);
    createdUsers.push(...users);
    const userCreationDuration = Date.now() - userCreationStart;
    
    addTestResult(
      'Stress Test: User creation',
      userCreationDuration < 10000, // 10 seconds for 100 users
      `Created ${users.length} users in ${userCreationDuration}ms`,
      null,
      { operation: 'bulk_user_creation', duration: userCreationDuration, count: users.length }
    );
    
    // Test 8: Stress test admin checks
    const { isUserAdmin } = require('../src/lib/auth.tsx');
    
    const stressTestStart = Date.now();
    const stressPromises = [];
    
    for (let iteration = 0; iteration < TEST_CONFIG.stressTest.iterations; iteration++) {
      for (const user of users) {
        stressPromises.push(isUserAdmin(user.id));
      }
    }
    
    const stressResults = await Promise.all(stressPromises);
    const stressTestDuration = Date.now() - stressTestStart;
    
    const totalOperations = TEST_CONFIG.stressTest.iterations * users.length;
    const avgOperationTime = stressTestDuration / totalOperations;
    
    addTestResult(
      'Stress Test: Admin check operations',
      avgOperationTime < 10, // Less than 10ms per operation
      `${totalOperations} operations in ${stressTestDuration}ms (${avgOperationTime.toFixed(2)}ms avg)`,
      null,
      { 
        operation: 'stress_admin_checks', 
        duration: stressTestDuration, 
        totalOperations, 
        avgOperationTime 
      }
    );
    
    // Test 9: Verify stress test accuracy
    const adminCount = stressResults.filter(result => result === true).length / TEST_CONFIG.stressTest.iterations;
    const expectedAdminCount = TEST_CONFIG.stressTest.adminCount;
    
    addTestResult(
      'Stress Test: Result accuracy',
      Math.abs(adminCount - expectedAdminCount) < 1,
      `Found ${adminCount} admins per iteration, expected ${expectedAdminCount}`,
      null,
      { operation: 'stress_accuracy', found: adminCount, expected: expectedAdminCount }
    );
    
  } catch (error) {
    addTestResult(
      'Stress Test: Execution',
      false,
      'Failed to execute stress test',
      error
    );
  }
  
  return createdUsers;
}

async function testConcurrencyPerformance() {
  log('Testing concurrency performance...', 'perf');
  
  try {
    // Test 10: Concurrent admin operations
    const { isUserAdmin, requireAdmin } = require('../src/lib/auth.tsx');
    
    // Create a test admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('testpassword123', 12),
        name: 'Concurrency Test Admin',
        role: UserRole.ADMIN,
        emailVerified: new Date()
      }
    });
    
    const concurrencyStart = Date.now();
    const concurrentOperations = [];
    
    // Simulate multiple concurrent admin operations
    for (let i = 0; i < 20; i++) {
      concurrentOperations.push(isUserAdmin(adminUser.id));
      
      // Add some requireAdmin calls
      const mockSession = {
        user: { id: adminUser.id, email: adminUser.email }
      };
      concurrentOperations.push(requireAdmin(mockSession));
    }
    
    await Promise.all(concurrentOperations);
    const concurrencyDuration = Date.now() - concurrencyStart;
    
    addTestResult(
      'Concurrency: Mixed admin operations',
      concurrencyDuration < 1000, // Less than 1 second for 40 operations
      `${concurrentOperations.length} concurrent operations in ${concurrencyDuration}ms`,
      null,
      { operation: 'concurrent_mixed', duration: concurrencyDuration, count: concurrentOperations.length }
    );
    
    // Cleanup
    await prisma.user.delete({ where: { id: adminUser.id } });
    
  } catch (error) {
    addTestResult(
      'Concurrency: Test execution',
      false,
      'Failed to test concurrency performance',
      error
    );
  }
}

async function cleanup(testUsers, stressUsers) {
  log('Cleaning up performance test data...', 'info');
  
  try {
    // Clean up regular test users
    if (testUsers && testUsers.length > 0) {
      const userIds = testUsers.map(user => user.id);
      await prisma.user.deleteMany({
        where: { id: { in: userIds } }
      });
      log(`Cleaned up ${userIds.length} test users`, 'success');
    }
    
    // Clean up stress test users
    if (stressUsers && stressUsers.length > 0) {
      const stressUserIds = stressUsers.map(user => user.id);
      await prisma.user.deleteMany({
        where: { id: { in: stressUserIds } }
      });
      log(`Cleaned up ${stressUserIds.length} stress test users`, 'success');
    }
    
    // Clean up any remaining test users
    await prisma.user.deleteMany({
      where: {
        email: {
          contains: 'testerat-'
        }
      }
    });
    
  } catch (error) {
    log(`Performance test cleanup failed: ${error.message}`, 'error');
  }
}

function generatePerformanceReport() {
  const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  
  console.log('\n' + '='.repeat(70));
  console.log('🐭 TESTERAT - ADMIN PERFORMANCE TEST REPORT');
  console.log('='.repeat(70));
  console.log(`📊 Total Tests: ${testResults.total}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Pass Rate: ${passRate}%`);
  console.log('='.repeat(70));
  
  // Performance metrics summary
  if (testResults.performanceMetrics.length > 0) {
    console.log('\n⚡ PERFORMANCE METRICS:');
    testResults.performanceMetrics.forEach(metric => {
      console.log(`  • ${metric.test}: ${metric.duration?.toFixed(2) || 'N/A'}ms`);
    });
  }
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach(error => {
      console.log(`  • ${error.test}: ${error.error}`);
    });
  }
  
  // Performance assessment
  const avgDuration = testResults.performanceMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / testResults.performanceMetrics.length;
  
  if (passRate >= 95 && avgDuration < 100) {
    console.log('\n🚀 PERFORMANCE ASSESSMENT: EXCELLENT - High Performance');
  } else if (passRate >= 85 && avgDuration < 200) {
    console.log('\n⚡ PERFORMANCE ASSESSMENT: GOOD - Acceptable Performance');
  } else {
    console.log('\n⚠️ PERFORMANCE ASSESSMENT: NEEDS OPTIMIZATION');
  }
  
  return passRate >= 90;
}

// Main test execution
async function runPerformanceTests() {
  console.log('🐭 TESTERAT - Starting Admin Performance Tests...\n');
  
  let testUsers = [];
  let stressUsers = [];
  
  try {
    testUsers = await testAdminCheckPerformance();
    await testDatabasePerformance();
    stressUsers = await testStressLoad();
    await testConcurrencyPerformance();
    
  } catch (error) {
    log(`Critical performance test failure: ${error.message}`, 'error');
  } finally {
    await cleanup(testUsers, stressUsers);
    await prisma.$disconnect();
  }
  
  const success = generatePerformanceReport();
  
  if (success) {
    console.log('\n🎉 TESTERAT PERFORMANCE VERDICT: ADMIN SYSTEM IS FAST!');
    process.exit(0);
  } else {
    console.log('\n💥 TESTERAT PERFORMANCE VERDICT: PERFORMANCE ISSUES DETECTED!');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runPerformanceTests().catch(error => {
    console.error('💥 Testerat performance tests crashed:', error);
    process.exit(1);
  });
}

module.exports = { runPerformanceTests, testResults };
