#!/usr/bin/env node

/**
 * 🐭 TESTERAT - Admin API Testing Suite
 * 
 * Comprehensive API testing for admin endpoints
 * Tests all admin-protected routes, data validation, and API security
 */

const { PrismaClient, UserRole } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Test configuration
const TEST_CONFIG = {
  adminEndpoints: [
    {
      path: '/api/admin/database',
      methods: ['GET', 'POST', 'PUT'],
      description: 'Database administration endpoint'
    },
    {
      path: '/api/learning-paths',
      methods: ['POST'],
      description: 'Learning path creation endpoint'
    },
    {
      path: '/api/learning-paths/test-id',
      methods: ['PUT', 'DELETE'],
      description: 'Learning path modification endpoint'
    },
    {
      path: '/api/auth/check-admin',
      methods: ['GET'],
      description: 'Admin status check endpoint'
    }
  ],
  testData: {
    learningPath: {
      title: 'Testerat Learning Path',
      description: 'A test learning path created by testerat',
      difficulty: 'BEGINNER',
      estimatedDuration: '4 weeks',
      isActive: true
    },
    databaseAction: {
      action: 'analyze',
      options: { includeRecommendations: true }
    }
  }
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
  details: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '🔍',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type];
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function addTestResult(testName, passed, details = '', error = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`${testName}: PASSED ${details}`, 'success');
  } else {
    testResults.failed++;
    log(`${testName}: FAILED ${details}`, 'error');
    if (error) {
      testResults.errors.push({ test: testName, error: error.message });
    }
  }
  
  testResults.details.push({
    test: testName,
    passed,
    details,
    timestamp: new Date().toISOString()
  });
}

// Mock session creation for testing
function createMockSession(userId, email, isAdmin = false) {
  return {
    user: {
      id: userId,
      email: email,
      name: 'Test User'
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
  };
}

// API test functions
async function testAdminUtilityFunctions() {
  log('Testing admin utility functions with real data...', 'info');
  
  const testUsers = [];
  
  try {
    // Create test users
    const regularUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('testpassword123', 12),
        name: 'API Test User',
        role: UserRole.USER,
        emailVerified: new Date()
      }
    });
    testUsers.push(regularUser);
    
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: await bcrypt.hash('testpassword123', 12),
        name: 'API Test Admin',
        role: UserRole.ADMIN,
        emailVerified: new Date()
      }
    });
    testUsers.push(adminUser);
    
    // Import admin functions
    const { isUserAdmin, getUserRole, requireAdmin } = require('../src/lib/auth.tsx');
    
    // Test 1: isUserAdmin with regular user
    const regularUserIsAdmin = await isUserAdmin(regularUser.id);
    addTestResult(
      'API Utils: isUserAdmin(regular user)',
      !regularUserIsAdmin,
      `Result: ${regularUserIsAdmin}`
    );
    
    // Test 2: isUserAdmin with admin user
    const adminUserIsAdmin = await isUserAdmin(adminUser.id);
    addTestResult(
      'API Utils: isUserAdmin(admin user)',
      adminUserIsAdmin,
      `Result: ${adminUserIsAdmin}`
    );
    
    // Test 3: getUserRole function
    const regularUserRole = await getUserRole(regularUser.id);
    const adminUserRole = await getUserRole(adminUser.id);
    
    addTestResult(
      'API Utils: getUserRole accuracy',
      regularUserRole === 'USER' && adminUserRole === 'ADMIN',
      `Regular: ${regularUserRole}, Admin: ${adminUserRole}`
    );
    
    // Test 4: requireAdmin with regular user session
    const regularSession = createMockSession(regularUser.id, regularUser.email);
    try {
      await requireAdmin(regularSession);
      addTestResult(
        'API Utils: requireAdmin blocks regular user',
        false,
        'Regular user was granted admin access'
      );
    } catch (error) {
      addTestResult(
        'API Utils: requireAdmin blocks regular user',
        true,
        'Regular user properly blocked'
      );
    }
    
    // Test 5: requireAdmin with admin user session
    const adminSession = createMockSession(adminUser.id, adminUser.email, true);
    try {
      await requireAdmin(adminSession);
      addTestResult(
        'API Utils: requireAdmin allows admin user',
        true,
        'Admin user properly granted access'
      );
    } catch (error) {
      addTestResult(
        'API Utils: requireAdmin allows admin user',
        false,
        'Admin user was blocked',
        error
      );
    }
    
  } catch (error) {
    addTestResult(
      'API Utils: Test setup',
      false,
      'Failed to create test users',
      error
    );
  }
  
  return testUsers;
}

async function testAdminMiddleware() {
  log('Testing admin middleware functions...', 'info');
  
  try {
    // Import middleware functions
    const { 
      checkAdminStatus, 
      requireAdminAccess, 
      canPerformAdminAction 
    } = require('../src/middleware/admin.ts');
    
    // Test 6: checkAdminStatus with null session
    const nullSessionStatus = await checkAdminStatus(null);
    addTestResult(
      'Middleware: checkAdminStatus(null)',
      !nullSessionStatus.isAdmin && nullSessionStatus.error,
      `Result: ${JSON.stringify(nullSessionStatus)}`
    );
    
    // Test 7: requireAdminAccess with null session
    try {
      await requireAdminAccess(null);
      addTestResult(
        'Middleware: requireAdminAccess blocks null session',
        false,
        'Null session was granted access'
      );
    } catch (error) {
      addTestResult(
        'Middleware: requireAdminAccess blocks null session',
        true,
        'Null session properly blocked'
      );
    }
    
    // Test 8: canPerformAdminAction with null session
    const canPerform = await canPerformAdminAction(null, 'test-action');
    addTestResult(
      'Middleware: canPerformAdminAction(null)',
      !canPerform,
      `Result: ${canPerform}`
    );
    
  } catch (error) {
    addTestResult(
      'Middleware: Function imports',
      false,
      'Failed to import middleware functions',
      error
    );
  }
}

async function testDatabaseAdminAPI() {
  log('Testing database admin API functionality...', 'info');
  
  try {
    // Import database optimization service
    const { dbOptimization } = require('../src/lib/services/databaseOptimization');
    
    // Test 9: Database stats retrieval
    try {
      const stats = await dbOptimization.getDatabaseStats();
      addTestResult(
        'DB Admin API: getDatabaseStats',
        stats && typeof stats === 'object',
        `Stats keys: ${Object.keys(stats || {}).join(', ')}`
      );
    } catch (error) {
      addTestResult(
        'DB Admin API: getDatabaseStats',
        false,
        'Failed to get database stats',
        error
      );
    }
    
    // Test 10: Table sizes analysis
    try {
      const tableSizes = await dbOptimization.getTableSizes();
      addTestResult(
        'DB Admin API: getTableSizes',
        Array.isArray(tableSizes),
        `Found ${tableSizes?.length || 0} tables`
      );
    } catch (error) {
      addTestResult(
        'DB Admin API: getTableSizes',
        false,
        'Failed to get table sizes',
        error
      );
    }
    
    // Test 11: Performance recommendations
    try {
      const recommendations = await dbOptimization.getPerformanceRecommendations();
      addTestResult(
        'DB Admin API: getPerformanceRecommendations',
        Array.isArray(recommendations),
        `Found ${recommendations?.length || 0} recommendations`
      );
    } catch (error) {
      addTestResult(
        'DB Admin API: getPerformanceRecommendations',
        false,
        'Failed to get performance recommendations',
        error
      );
    }
    
    // Test 12: Recent metrics
    try {
      const metrics = dbOptimization.getRecentMetrics(10);
      addTestResult(
        'DB Admin API: getRecentMetrics',
        Array.isArray(metrics),
        `Found ${metrics?.length || 0} metrics`
      );
    } catch (error) {
      addTestResult(
        'DB Admin API: getRecentMetrics',
        false,
        'Failed to get recent metrics',
        error
      );
    }
    
  } catch (error) {
    addTestResult(
      'DB Admin API: Service import',
      false,
      'Failed to import database optimization service',
      error
    );
  }
}

async function testLearningPathAdminAPI() {
  log('Testing learning path admin API functionality...', 'info');
  
  try {
    // Test 13: Create learning path (simulate admin action)
    const testLearningPath = await prisma.learningPath.create({
      data: {
        title: TEST_CONFIG.testData.learningPath.title,
        description: TEST_CONFIG.testData.learningPath.description,
        difficulty: TEST_CONFIG.testData.learningPath.difficulty,
        estimatedDuration: TEST_CONFIG.testData.learningPath.estimatedDuration,
        isActive: TEST_CONFIG.testData.learningPath.isActive,
        slug: 'testerat-learning-path-' + Date.now()
      }
    });
    
    addTestResult(
      'Learning Path API: Create learning path',
      testLearningPath && testLearningPath.id,
      `Created path: ${testLearningPath.title}`
    );
    
    // Test 14: Update learning path
    const updatedPath = await prisma.learningPath.update({
      where: { id: testLearningPath.id },
      data: { 
        title: 'Updated Testerat Learning Path',
        description: 'Updated description'
      }
    });
    
    addTestResult(
      'Learning Path API: Update learning path',
      updatedPath.title === 'Updated Testerat Learning Path',
      `Updated title: ${updatedPath.title}`
    );
    
    // Test 15: Delete learning path
    await prisma.learningPath.delete({
      where: { id: testLearningPath.id }
    });
    
    const deletedPath = await prisma.learningPath.findUnique({
      where: { id: testLearningPath.id }
    });
    
    addTestResult(
      'Learning Path API: Delete learning path',
      deletedPath === null,
      'Learning path successfully deleted'
    );
    
  } catch (error) {
    addTestResult(
      'Learning Path API: CRUD operations',
      false,
      'Failed to test learning path operations',
      error
    );
  }
}

async function testAdminStatusAPI() {
  log('Testing admin status check API...', 'info');
  
  try {
    // Test 16: Admin status check for different user types
    const users = await prisma.user.findMany({
      where: {
        email: {
          contains: 'testerat-api'
        }
      },
      select: { id: true, email: true, role: true }
    });
    
    for (const user of users) {
      const expectedIsAdmin = user.role === 'ADMIN' || user.role === 'SUPER_ADMIN';
      
      // Simulate the check-admin API logic
      const isAdmin = user.role === 'ADMIN' || user.role === 'SUPER_ADMIN';
      
      addTestResult(
        `Admin Status API: ${user.role} user check`,
        isAdmin === expectedIsAdmin,
        `User: ${user.email}, IsAdmin: ${isAdmin}, Expected: ${expectedIsAdmin}`
      );
    }
    
  } catch (error) {
    addTestResult(
      'Admin Status API: Status checks',
      false,
      'Failed to test admin status checks',
      error
    );
  }
}

async function testErrorHandling() {
  log('Testing API error handling...', 'info');
  
  try {
    // Test 17: Invalid user ID handling
    const { isUserAdmin } = require('../src/lib/auth.tsx');
    
    const invalidUserResult = await isUserAdmin('invalid-user-id');
    addTestResult(
      'Error Handling: Invalid user ID',
      !invalidUserResult,
      `Result for invalid ID: ${invalidUserResult}`
    );
    
    // Test 18: Null user ID handling
    const nullUserResult = await isUserAdmin(null);
    addTestResult(
      'Error Handling: Null user ID',
      !nullUserResult,
      `Result for null ID: ${nullUserResult}`
    );
    
    // Test 19: Database connection error simulation
    // This test checks if functions handle database errors gracefully
    try {
      // Temporarily disconnect to simulate error
      await prisma.$disconnect();
      
      const disconnectedResult = await isUserAdmin('test-id');
      addTestResult(
        'Error Handling: Database disconnection',
        !disconnectedResult,
        'Function handled database error gracefully'
      );
      
      // Reconnect
      await prisma.$connect();
      
    } catch (error) {
      addTestResult(
        'Error Handling: Database disconnection',
        true,
        'Function properly threw error on database issue'
      );
      
      // Ensure reconnection
      await prisma.$connect();
    }
    
  } catch (error) {
    addTestResult(
      'Error Handling: Test execution',
      false,
      'Failed to test error handling',
      error
    );
  }
}

async function cleanup(testUsers) {
  log('Cleaning up API test data...', 'info');
  
  try {
    if (testUsers && testUsers.length > 0) {
      const userIds = testUsers.map(user => user.id);
      await prisma.user.deleteMany({
        where: {
          id: {
            in: userIds
          }
        }
      });
      
      log(`Cleaned up ${userIds.length} test users`, 'success');
    }
    
    // Clean up any test learning paths
    await prisma.learningPath.deleteMany({
      where: {
        title: {
          contains: 'Testerat'
        }
      }
    });
    
  } catch (error) {
    log(`API test cleanup failed: ${error.message}`, 'error');
  }
}

function generateAPIReport() {
  const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  
  console.log('\n' + '='.repeat(60));
  console.log('🐭 TESTERAT - ADMIN API TEST REPORT');
  console.log('='.repeat(60));
  console.log(`📊 Total Tests: ${testResults.total}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Pass Rate: ${passRate}%`);
  console.log('='.repeat(60));
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach(error => {
      console.log(`  • ${error.test}: ${error.error}`);
    });
  }
  
  return passRate >= 90;
}

// Main test execution
async function runAPITests() {
  console.log('🐭 TESTERAT - Starting Admin API Tests...\n');
  
  let testUsers = [];
  
  try {
    testUsers = await testAdminUtilityFunctions();
    await testAdminMiddleware();
    await testDatabaseAdminAPI();
    await testLearningPathAdminAPI();
    await testAdminStatusAPI();
    await testErrorHandling();
    
  } catch (error) {
    log(`Critical API test failure: ${error.message}`, 'error');
  } finally {
    await cleanup(testUsers);
    await prisma.$disconnect();
  }
  
  const success = generateAPIReport();
  
  if (success) {
    console.log('\n🎉 TESTERAT API VERDICT: ADMIN APIs ARE WORKING PERFECTLY!');
    process.exit(0);
  } else {
    console.log('\n💥 TESTERAT API VERDICT: API ISSUES DETECTED!');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runAPITests().catch(error => {
    console.error('💥 Testerat API tests crashed:', error);
    process.exit(1);
  });
}

module.exports = { runAPITests, testResults };
