#!/usr/bin/env tsx

/**
 * 🗄️ ADMIN DATABASE INTEGRATION TESTS
 * 
 * Tests the admin system's database operations, role assignments,
 * and data integrity with real database connections.
 */

import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { isUserAdmin, getUserRole, requireAdmin } from '../src/lib/auth';

const prisma = new PrismaClient();

interface TestResult {
  name: string;
  passed: boolean;
  details: string;
  duration: number;
  error?: string;
}

class AdminDatabaseTester {
  private results: TestResult[] = [];
  private testUsers: any[] = [];

  private async runTest(
    name: string,
    testFn: () => Promise<void>
  ): Promise<void> {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: true,
        details: 'Test completed successfully',
        duration
      });
      console.log(`✅ ${name} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        name,
        passed: false,
        details: 'Test failed',
        duration,
        error: error instanceof Error ? error.message : String(error)
      });
      console.log(`❌ ${name} (${duration}ms): ${error}`);
    }
  }

  async testUserRoleCreation(): Promise<void> {
    await this.runTest('User Role Creation', async () => {
      // Test creating users with different roles
      const roles = [UserRole.USER, UserRole.ADMIN, UserRole.SUPER_ADMIN];
      
      for (const role of roles) {
        const user = await prisma.user.create({
          data: {
            email: `test-${role.toLowerCase()}@example.com`,
            password: await bcrypt.hash('testpassword', 12),
            name: `Test ${role}`,
            role: role,
            emailVerified: new Date()
          }
        });
        
        this.testUsers.push(user);
        
        if (user.role !== role) {
          throw new Error(`Role assignment failed: expected ${role}, got ${user.role}`);
        }
      }
    });
  }

  async testRoleValidation(): Promise<void> {
    await this.runTest('Role Validation Functions', async () => {
      for (const user of this.testUsers) {
        // Test isUserAdmin function
        const isAdmin = await isUserAdmin(user.id);
        const shouldBeAdmin = user.role === UserRole.ADMIN || user.role === UserRole.SUPER_ADMIN;
        
        if (isAdmin !== shouldBeAdmin) {
          throw new Error(`isUserAdmin failed for ${user.email}: got ${isAdmin}, expected ${shouldBeAdmin}`);
        }
        
        // Test getUserRole function
        const userRole = await getUserRole(user.id);
        if (userRole !== user.role) {
          throw new Error(`getUserRole failed for ${user.email}: got ${userRole}, expected ${user.role}`);
        }
      }
    });
  }

  async testRequireAdminFunction(): Promise<void> {
    await this.runTest('RequireAdmin Function', async () => {
      for (const user of this.testUsers) {
        const mockSession = {
          user: { id: user.id, email: user.email }
        };
        
        const shouldPass = user.role === UserRole.ADMIN || user.role === UserRole.SUPER_ADMIN;
        
        try {
          await requireAdmin(mockSession as any);
          if (!shouldPass) {
            throw new Error(`requireAdmin should have failed for ${user.email} (${user.role})`);
          }
        } catch (error) {
          if (shouldPass) {
            throw new Error(`requireAdmin should have passed for ${user.email} (${user.role})`);
          }
        }
      }
    });
  }

  async testRoleTransitions(): Promise<void> {
    await this.runTest('Role Transitions', async () => {
      // Create a regular user
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: await bcrypt.hash('testpassword', 12),
          name: 'Transition Test User',
          role: UserRole.USER,
          emailVerified: new Date()
        }
      });
      
      this.testUsers.push(user);
      
      // Test USER -> ADMIN transition
      const adminUser = await prisma.user.update({
        where: { id: user.id },
        data: { role: UserRole.ADMIN }
      });
      
      if (adminUser.role !== UserRole.ADMIN) {
        throw new Error('Failed to promote user to admin');
      }
      
      const isAdminAfterPromotion = await isUserAdmin(adminUser.id);
      if (!isAdminAfterPromotion) {
        throw new Error('isUserAdmin returned false after promotion to admin');
      }
      
      // Test ADMIN -> SUPER_ADMIN transition
      const superAdminUser = await prisma.user.update({
        where: { id: user.id },
        data: { role: UserRole.SUPER_ADMIN }
      });
      
      if (superAdminUser.role !== UserRole.SUPER_ADMIN) {
        throw new Error('Failed to promote admin to super admin');
      }
      
      const isSuperAdmin = await isUserAdmin(superAdminUser.id);
      if (!isSuperAdmin) {
        throw new Error('isUserAdmin returned false for super admin');
      }
      
      // Test SUPER_ADMIN -> USER demotion
      const demotedUser = await prisma.user.update({
        where: { id: user.id },
        data: { role: UserRole.USER }
      });
      
      if (demotedUser.role !== UserRole.USER) {
        throw new Error('Failed to demote super admin to user');
      }
      
      const isAdminAfterDemotion = await isUserAdmin(demotedUser.id);
      if (isAdminAfterDemotion) {
        throw new Error('isUserAdmin returned true after demotion to user');
      }
    });
  }

  async testConcurrentRoleOperations(): Promise<void> {
    await this.runTest('Concurrent Role Operations', async () => {
      // Create multiple users simultaneously
      const userPromises = Array.from({ length: 5 }, (_, i) =>
        prisma.user.create({
          data: {
            email: `concurrent-${i}@example.com`,
            password: await bcrypt.hash('testpassword', 12),
            name: `Concurrent User ${i}`,
            role: i % 2 === 0 ? UserRole.USER : UserRole.ADMIN,
            emailVerified: new Date()
          }
        })
      );
      
      const users = await Promise.all(userPromises);
      this.testUsers.push(...users);
      
      // Test concurrent admin checks
      const adminCheckPromises = users.map(user => isUserAdmin(user.id));
      const adminResults = await Promise.all(adminCheckPromises);
      
      // Verify results
      for (let i = 0; i < users.length; i++) {
        const expectedAdmin = users[i].role === UserRole.ADMIN;
        if (adminResults[i] !== expectedAdmin) {
          throw new Error(`Concurrent admin check failed for user ${i}`);
        }
      }
    });
  }

  async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling', async () => {
      // Test with non-existent user ID
      const nonExistentId = 'non-existent-id';
      
      const isAdminResult = await isUserAdmin(nonExistentId);
      if (isAdminResult !== false) {
        throw new Error('isUserAdmin should return false for non-existent user');
      }
      
      const roleResult = await getUserRole(nonExistentId);
      if (roleResult !== null) {
        throw new Error('getUserRole should return null for non-existent user');
      }
      
      // Test requireAdmin with invalid session
      try {
        await requireAdmin(null);
        throw new Error('requireAdmin should throw error for null session');
      } catch (error) {
        if (!(error instanceof Error) || !error.message.includes('Authentication required')) {
          throw new Error('requireAdmin should throw authentication error');
        }
      }
      
      // Test requireAdmin with session without user ID
      try {
        await requireAdmin({ user: {} } as any);
        throw new Error('requireAdmin should throw error for session without user ID');
      } catch (error) {
        if (!(error instanceof Error) || !error.message.includes('Authentication required')) {
          throw new Error('requireAdmin should throw authentication error');
        }
      }
    });
  }

  async testDataIntegrity(): Promise<void> {
    await this.runTest('Data Integrity', async () => {
      // Test that role field has proper constraints
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: await bcrypt.hash('testpassword', 12),
          name: 'Integrity Test User',
          role: UserRole.USER,
          emailVerified: new Date()
        }
      });
      
      this.testUsers.push(user);
      
      // Verify default role is USER
      if (user.role !== UserRole.USER) {
        throw new Error(`Default role should be USER, got ${user.role}`);
      }
      
      // Test that role is required (try to create user without role - should use default)
      const userWithoutExplicitRole = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: await bcrypt.hash('testpassword', 12),
          name: 'No Role Test User',
          emailVerified: new Date()
          // No role specified - should default to USER
        }
      });
      
      this.testUsers.push(userWithoutExplicitRole);
      
      if (userWithoutExplicitRole.role !== UserRole.USER) {
        throw new Error(`Default role should be USER when not specified, got ${userWithoutExplicitRole.role}`);
      }
    });
  }

  async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up test data...');
    
    if (this.testUsers.length > 0) {
      const userIds = this.testUsers.map(user => user.id);
      await prisma.user.deleteMany({
        where: { id: { in: userIds } }
      });
      console.log(`✅ Cleaned up ${userIds.length} test users`);
    }
  }

  generateReport(): void {
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = total - passed;
    const passRate = ((passed / total) * 100).toFixed(1);
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    console.log('\n' + '='.repeat(60));
    console.log('🗄️ ADMIN DATABASE INTEGRATION TEST REPORT');
    console.log('='.repeat(60));
    console.log(`📊 Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Pass Rate: ${passRate}%`);
    console.log(`⏱️ Total Duration: ${totalDuration}ms`);
    console.log('='.repeat(60));
    
    if (failed > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results
        .filter(r => !r.passed)
        .forEach(result => {
          console.log(`  • ${result.name}: ${result.error}`);
        });
    }
    
    // Save detailed report
    const fs = require('fs');
    const path = require('path');
    const reportPath = path.join(process.cwd(), 'docs/deployment/ADMIN_DATABASE_TEST_REPORT.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      summary: { total, passed, failed, passRate, totalDuration },
      results: this.results,
      timestamp: new Date().toISOString()
    }, null, 2));
    
    console.log(`\n📄 Detailed report saved: ${reportPath}`);
  }

  async runAllTests(): Promise<boolean> {
    console.log('🗄️ Starting Admin Database Integration Tests...\n');
    
    try {
      await this.testUserRoleCreation();
      await this.testRoleValidation();
      await this.testRequireAdminFunction();
      await this.testRoleTransitions();
      await this.testConcurrentRoleOperations();
      await this.testErrorHandling();
      await this.testDataIntegrity();
      
    } catch (error) {
      console.error('💥 Critical test failure:', error);
    } finally {
      await this.cleanup();
      await prisma.$disconnect();
    }
    
    this.generateReport();
    
    const passRate = (this.results.filter(r => r.passed).length / this.results.length) * 100;
    return passRate >= 90;
  }
}

// Run tests if called directly
async function main() {
  const tester = new AdminDatabaseTester();
  const success = await tester.runAllTests();
  
  if (success) {
    console.log('\n🎉 DATABASE INTEGRATION TESTS PASSED!');
    process.exit(0);
  } else {
    console.log('\n💥 DATABASE INTEGRATION TESTS FAILED!');
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('💥 Test runner crashed:', error);
    process.exit(1);
  });
}

export { AdminDatabaseTester };
