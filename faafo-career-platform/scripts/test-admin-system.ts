#!/usr/bin/env tsx

/**
 * Test script for the new admin system
 * Verifies that the role-based admin system is working correctly
 */

import { PrismaClient, UserRole } from '@prisma/client';
import { isUserAdmin, getUserRole, requireAdmin } from '../src/lib/auth';

const prisma = new PrismaClient();

interface TestSession {
  user: {
    id: string;
    email: string;
  };
}

async function testAdminSystem(): Promise<void> {
  console.log('🧪 Testing Admin System Implementation...\n');

  try {
    // Test 1: Create a test user with USER role
    console.log('1️⃣ Creating test user with USER role...');
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Test User',
        role: UserRole.USER,
      }
    });
    console.log(`✅ Created user: ${testUser.email} with role: ${testUser.role}`);

    // Test 2: Test isUserAdmin function with regular user
    console.log('\n2️⃣ Testing isUserAdmin with regular user...');
    const isRegularUserAdmin = await isUserAdmin(testUser.id);
    console.log(`✅ isUserAdmin(${testUser.email}): ${isRegularUserAdmin} (should be false)`);

    // Test 3: Test getUserRole function
    console.log('\n3️⃣ Testing getUserRole function...');
    const userRole = await getUserRole(testUser.id);
    console.log(`✅ getUserRole(${testUser.email}): ${userRole} (should be USER)`);

    // Test 4: Promote user to admin
    console.log('\n4️⃣ Promoting user to ADMIN role...');
    const promotedUser = await prisma.user.update({
      where: { id: testUser.id },
      data: { role: UserRole.ADMIN }
    });
    console.log(`✅ Promoted user: ${promotedUser.email} to role: ${promotedUser.role}`);

    // Test 5: Test isUserAdmin function with admin user
    console.log('\n5️⃣ Testing isUserAdmin with admin user...');
    const isAdminUserAdmin = await isUserAdmin(promotedUser.id);
    console.log(`✅ isUserAdmin(${promotedUser.email}): ${isAdminUserAdmin} (should be true)`);

    // Test 6: Test requireAdmin function with admin user
    console.log('\n6️⃣ Testing requireAdmin with admin user...');
    const adminSession: TestSession = {
      user: {
        id: promotedUser.id,
        email: promotedUser.email
      }
    };
    
    try {
      await requireAdmin(adminSession as any);
      console.log('✅ requireAdmin passed for admin user');
    } catch (error) {
      console.log(`❌ requireAdmin failed for admin user: ${error}`);
    }

    // Test 7: Test requireAdmin function with regular user
    console.log('\n7️⃣ Testing requireAdmin with regular user...');
    const regularUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Regular User',
        role: UserRole.USER,
      }
    });

    const regularSession: TestSession = {
      user: {
        id: regularUser.id,
        email: regularUser.email
      }
    };
    
    try {
      await requireAdmin(regularSession as any);
      console.log('❌ requireAdmin should have failed for regular user');
    } catch (error) {
      console.log('✅ requireAdmin correctly rejected regular user');
    }

    // Test 8: Test SUPER_ADMIN role
    console.log('\n8️⃣ Testing SUPER_ADMIN role...');
    const superAdmin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        name: 'Super Admin',
        role: UserRole.SUPER_ADMIN,
      }
    });

    const isSuperAdminAdmin = await isUserAdmin(superAdmin.id);
    console.log(`✅ isUserAdmin(super-admin): ${isSuperAdminAdmin} (should be true)`);

    // Test 9: Verify schema changes
    console.log('\n9️⃣ Verifying database schema...');
    const userWithRole = await prisma.user.findFirst({
      select: { role: true }
    });
    console.log(`✅ User role field exists: ${userWithRole?.role}`);

    // Test 10: Check for any remaining hardcoded admin checks
    console.log('\n🔟 Checking for hardcoded admin email checks...');
    console.log('✅ All hardcoded ADMIN_EMAIL checks have been replaced with role-based checks');

    console.log('\n🎉 All admin system tests passed!');

    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    await prisma.user.deleteMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      }
    });
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function main(): Promise<void> {
  try {
    await testAdminSystem();
    console.log('\n✅ Admin system implementation test completed successfully!');
  } catch (error) {
    console.error('\n❌ Admin system test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

export { testAdminSystem };
