import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedResumeTemplates() {
  console.log('🎨 Seeding resume templates...');

  // Create resume templates
  const templates = [
    {
      id: 'modern-template-001',
      name: 'Modern Professional',
      category: 'modern',
      description: 'Clean, modern design perfect for tech and creative professionals',
      cssStyles: JSON.stringify({
        primaryColor: '#3B82F6',
        secondaryColor: '#1E40AF',
        fontFamily: 'Inter, sans-serif',
        layout: 'two-column'
      }),
      isActive: true,
      isPremium: false
    },
    {
      id: 'creative-template-001',
      name: 'Creative Designer',
      category: 'creative',
      description: 'Bold, creative layout ideal for designers and artists',
      cssStyles: JSON.stringify({
        primaryColor: '#8B5CF6',
        secondaryColor: '#7C3AED',
        fontFamily: 'Poppins, sans-serif',
        layout: 'creative-grid'
      }),
      isActive: true,
      isPremium: false
    },
    {
      id: 'professional-template-001',
      name: 'Corporate Professional',
      category: 'professional',
      description: 'Traditional, professional format for corporate environments',
      cssStyles: JSON.stringify({
        primaryColor: '#374151',
        secondaryColor: '#1F2937',
        fontFamily: 'Times New Roman, serif',
        layout: 'single-column'
      }),
      isActive: true,
      isPremium: false
    },
    {
      id: 'minimal-template-001',
      name: 'Minimal Clean',
      category: 'minimal',
      description: 'Simple, clean design that focuses on content',
      cssStyles: JSON.stringify({
        primaryColor: '#10B981',
        secondaryColor: '#059669',
        fontFamily: 'Helvetica, Arial, sans-serif',
        layout: 'minimal-single'
      }),
      isActive: true,
      isPremium: false
    }
  ];

  for (const template of templates) {
    const existingTemplate = await prisma.resumeTemplate.findUnique({
      where: { id: template.id }
    });

    if (!existingTemplate) {
      await prisma.resumeTemplate.create({
        data: template
      });
      console.log(`✅ Created template: ${template.name}`);
    } else {
      console.log(`⏭️  Template already exists: ${template.name}`);
    }
  }

  console.log('🎨 Resume templates seeding completed!');
}

async function main() {
  try {
    await seedResumeTemplates();
  } catch (error) {
    console.error('❌ Error seeding resume templates:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
