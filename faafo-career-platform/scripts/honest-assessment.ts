import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function honestAssessment() {
  console.log('🔍 HONEST RESUME BUILDER ASSESSMENT\n');
  console.log('=' .repeat(60));

  const results: { component: string; status: string; details: string }[] = [];

  try {
    // 1. Database Layer Assessment
    console.log('1️⃣ Testing Database Layer...');
    
    try {
      await prisma.$connect();
      
      // Check if resume templates exist
      const templates = await prisma.resumeTemplate.findMany();
      if (templates.length > 0) {
        results.push({ 
          component: 'Database - Templates', 
          status: '✅ WORKING', 
          details: `${templates.length} templates available` 
        });
      } else {
        results.push({ 
          component: 'Database - Templates', 
          status: '❌ BROKEN', 
          details: 'No templates found' 
        });
      }

      // Check if we can create/read/update/delete resumes
      const testUser = await prisma.user.findFirst();
      if (testUser) {
        // Test resume CRUD
        const testResume = await prisma.resume.create({
          data: {
            userId: testUser.id,
            title: 'Test Resume',
            templateId: templates[0]?.id || null,
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>'
          }
        });

        const fetchedResume = await prisma.resume.findUnique({
          where: { id: testResume.id }
        });

        await prisma.resume.delete({ where: { id: testResume.id } });

        if (fetchedResume) {
          results.push({ 
            component: 'Database - CRUD', 
            status: '✅ WORKING', 
            details: 'Create, Read, Delete operations successful' 
          });
        } else {
          results.push({ 
            component: 'Database - CRUD', 
            status: '❌ BROKEN', 
            details: 'CRUD operations failed' 
          });
        }
      } else {
        results.push({ 
          component: 'Database - Users', 
          status: '❌ BROKEN', 
          details: 'No users found in database' 
        });
      }

    } catch (error) {
      results.push({ 
        component: 'Database Connection', 
        status: '❌ BROKEN', 
        details: `Connection failed: ${error}` 
      });
    }

    // 2. Server Connectivity Assessment
    console.log('2️⃣ Testing Server Connectivity...');
    
    try {
      const response = await fetch('http://localhost:3002/resume-builder');
      if (response.status === 200) {
        results.push({ 
          component: 'Server - Main Page', 
          status: '✅ WORKING', 
          details: 'Resume builder page loads successfully' 
        });
      } else {
        results.push({ 
          component: 'Server - Main Page', 
          status: '❌ BROKEN', 
          details: `HTTP ${response.status}` 
        });
      }
    } catch (error) {
      results.push({ 
        component: 'Server - Main Page', 
        status: '❌ BROKEN', 
        details: 'Server not responding' 
      });
    }

    try {
      const response = await fetch('http://localhost:3002/resume-builder/create');
      if (response.status === 200) {
        results.push({ 
          component: 'Server - Create Page', 
          status: '✅ WORKING', 
          details: 'Create page loads successfully' 
        });
      } else {
        results.push({ 
          component: 'Server - Create Page', 
          status: '❌ BROKEN', 
          details: `HTTP ${response.status}` 
        });
      }
    } catch (error) {
      results.push({ 
        component: 'Server - Create Page', 
        status: '❌ BROKEN', 
        details: 'Create page not responding' 
      });
    }

    // 3. API Security Assessment
    console.log('3️⃣ Testing API Security...');
    
    try {
      const response = await fetch('http://localhost:3002/api/resume-builder');
      if (response.status === 401) {
        results.push({ 
          component: 'API - Security', 
          status: '✅ WORKING', 
          details: 'Properly rejects unauthenticated requests' 
        });
      } else {
        results.push({ 
          component: 'API - Security', 
          status: '⚠️ WARNING', 
          details: `Expected 401, got ${response.status}` 
        });
      }
    } catch (error) {
      results.push({ 
        component: 'API - Security', 
        status: '❌ BROKEN', 
        details: 'API not responding' 
      });
    }

    // 4. Authentication System Assessment
    console.log('4️⃣ Testing Authentication System...');
    
    try {
      const sessionResponse = await fetch('http://localhost:3002/api/auth/session');
      if (sessionResponse.status === 200) {
        const sessionData = await sessionResponse.json();
        if (sessionData.user) {
          results.push({ 
            component: 'Auth - Session', 
            status: '✅ WORKING', 
            details: 'User session active' 
          });
        } else {
          results.push({ 
            component: 'Auth - Session', 
            status: '⚠️ WARNING', 
            details: 'No active user session' 
          });
        }
      } else {
        results.push({ 
          component: 'Auth - Session', 
          status: '❌ BROKEN', 
          details: 'Session endpoint not working' 
        });
      }
    } catch (error) {
      results.push({ 
        component: 'Auth - Session', 
        status: '❌ BROKEN', 
        details: 'Authentication system not responding' 
      });
    }

    // Print Results
    console.log('\n' + '=' .repeat(60));
    console.log('📊 HONEST ASSESSMENT RESULTS');
    console.log('=' .repeat(60));

    let working = 0;
    let broken = 0;
    let warnings = 0;

    results.forEach(result => {
      console.log(`${result.status} ${result.component.padEnd(25)} ${result.details}`);
      if (result.status.includes('✅')) working++;
      else if (result.status.includes('❌')) broken++;
      else if (result.status.includes('⚠️')) warnings++;
    });

    console.log('=' .repeat(60));
    console.log(`📈 SUMMARY: ${working} Working | ${warnings} Warnings | ${broken} Broken`);

    // Honest Assessment
    console.log('\n🎯 HONEST VERDICT:');
    
    if (broken === 0 && warnings <= 1) {
      console.log('✅ RESUME BUILDER IS FULLY FUNCTIONAL!');
      console.log('   All core components are working correctly.');
      console.log('   Users can create, edit, and manage resumes.');
    } else if (broken <= 2 && working >= 4) {
      console.log('⚠️ RESUME BUILDER IS MOSTLY FUNCTIONAL');
      console.log('   Core functionality works but has some issues.');
      console.log('   Most users can use the basic features.');
    } else {
      console.log('❌ RESUME BUILDER HAS SIGNIFICANT ISSUES');
      console.log('   Multiple components are broken or not working.');
      console.log('   Needs fixes before users can use it effectively.');
    }

    console.log('\n🔧 WHAT ACTUALLY WORKS:');
    results.filter(r => r.status.includes('✅')).forEach(r => {
      console.log(`   ✅ ${r.component}: ${r.details}`);
    });

    if (warnings > 0) {
      console.log('\n⚠️ ISSUES THAT NEED ATTENTION:');
      results.filter(r => r.status.includes('⚠️')).forEach(r => {
        console.log(`   ⚠️ ${r.component}: ${r.details}`);
      });
    }

    if (broken > 0) {
      console.log('\n❌ BROKEN COMPONENTS:');
      results.filter(r => r.status.includes('❌')).forEach(r => {
        console.log(`   ❌ ${r.component}: ${r.details}`);
      });
    }

    console.log('\n🌐 TO TEST YOURSELF:');
    console.log('   1. Open: http://localhost:3002/resume-builder');
    console.log('   2. Try to create a new resume');
    console.log('   3. Check if you can save and edit resume data');
    console.log('   4. Verify all form fields work correctly');

  } catch (error) {
    console.error('❌ Assessment failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

honestAssessment();
