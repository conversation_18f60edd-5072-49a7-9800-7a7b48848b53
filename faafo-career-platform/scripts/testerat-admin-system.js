#!/usr/bin/env node

/**
 * 🐭 TESTERAT - Admin System Implementation Testing
 * 
 * Comprehensive testing suite for Task 1.1: Fix Admin Access Control System
 * Tests all aspects of the new role-based admin system
 */

const { PrismaClient, UserRole } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Test configuration
const TEST_CONFIG = {
  testUsers: [
    { email: '<EMAIL>', role: 'USER', name: 'Testerat User' },
    { email: '<EMAIL>', role: 'ADMIN', name: 'Testerat Admin' },
    { email: '<EMAIL>', role: 'SUPER_ADMIN', name: 'Testerat Super Admin' }
  ],
  apiEndpoints: [
    '/api/learning-paths',
    '/api/learning-paths/test-id',
    '/api/admin/database',
    '/api/auth/check-admin'
  ]
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
  details: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '🔍',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type];
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function addTestResult(testName, passed, details = '', error = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`${testName}: PASSED ${details}`, 'success');
  } else {
    testResults.failed++;
    log(`${testName}: FAILED ${details}`, 'error');
    if (error) {
      testResults.errors.push({ test: testName, error: error.message });
    }
  }
  
  testResults.details.push({
    test: testName,
    passed,
    details,
    timestamp: new Date().toISOString()
  });
}

// Test functions
async function testDatabaseSchema() {
  log('Testing database schema changes...', 'info');
  
  try {
    // Test 1: UserRole enum exists
    const userRoles = Object.values(UserRole);
    addTestResult(
      'Schema: UserRole enum',
      userRoles.includes('USER') && userRoles.includes('ADMIN') && userRoles.includes('SUPER_ADMIN'),
      `Found roles: ${userRoles.join(', ')}`
    );
    
    // Test 2: User model has role field
    const testUser = await prisma.user.findFirst({
      select: { role: true }
    });
    addTestResult(
      'Schema: User.role field',
      testUser !== null,
      'Role field accessible in User model'
    );
    
  } catch (error) {
    addTestResult('Schema: Database access', false, 'Failed to access database', error);
  }
}

async function testAdminUtilityFunctions() {
  log('Testing admin utility functions...', 'info');
  
  try {
    // Check if admin functions exist in auth file (file-based test)
    const fs = require('fs');
    const path = require('path');
    const authFilePath = path.join(process.cwd(), 'src/lib/auth.tsx');

    if (fs.existsSync(authFilePath)) {
      const authContent = fs.readFileSync(authFilePath, 'utf8');

      // Test 3: isUserAdmin function exists
      addTestResult(
        'Utils: isUserAdmin function',
        authContent.includes('export async function isUserAdmin'),
        'Function is defined and exported'
      );

      // Test 4: getUserRole function exists
      addTestResult(
        'Utils: getUserRole function',
        authContent.includes('export async function getUserRole'),
        'Function is defined and exported'
      );

      // Test 5: requireAdmin function exists
      addTestResult(
        'Utils: requireAdmin function',
        authContent.includes('export async function requireAdmin'),
        'Function is defined and exported'
      );
    } else {
      addTestResult('Utils: Auth file exists', false, 'Auth file not found');
    }

  } catch (error) {
    addTestResult('Utils: Function analysis', false, 'Failed to analyze admin functions', error);
  }
}

async function testUserRoleAssignment() {
  log('Testing user role assignment and validation...', 'info');
  
  const createdUsers = [];
  
  try {
    // Create test users with different roles
    for (const userData of TEST_CONFIG.testUsers) {
      const hashedPassword = await bcrypt.hash('testpassword123', 12);
      
      const user = await prisma.user.create({
        data: {
          email: userData.email,
          password: hashedPassword,
          name: userData.name,
          role: UserRole[userData.role],
          emailVerified: new Date()
        }
      });
      
      createdUsers.push(user);
      
      addTestResult(
        `Role Assignment: ${userData.role}`,
        user.role === userData.role,
        `User ${user.email} created with role ${user.role}`
      );
    }
    
    // Test role validation (simplified - just check roles were assigned)
    for (const user of createdUsers) {
      const expectedRole = UserRole[TEST_CONFIG.testUsers.find(u => u.email === user.email).role];

      addTestResult(
        `Role Validation: ${user.role}`,
        user.role === expectedRole,
        `User ${user.email} has role ${user.role}, expected ${expectedRole}`
      );
    }
    
  } catch (error) {
    addTestResult('Role Assignment: User creation', false, 'Failed to create test users', error);
  }
  
  return createdUsers;
}

async function testAPIRouteProtection(testUsers) {
  log('Testing API route protection...', 'info');
  
  try {
    // Test hardcoded admin email removal
    const filesToCheck = [
      'src/app/api/learning-paths/route.ts',
      'src/app/api/learning-paths/[id]/route.ts',
      'src/app/api/admin/database/route.ts'
    ];
    
    for (const filePath of filesToCheck) {
      const fullPath = path.join(process.cwd(), filePath);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        const hasHardcodedCheck = content.includes('process.env.ADMIN_EMAIL');
        
        addTestResult(
          `API Protection: ${filePath}`,
          !hasHardcodedCheck,
          hasHardcodedCheck ? 'Still contains hardcoded admin check' : 'No hardcoded admin checks found'
        );
      } else {
        addTestResult(
          `API Protection: ${filePath}`,
          false,
          'File not found'
        );
      }
    }
    
  } catch (error) {
    addTestResult('API Protection: File analysis', false, 'Failed to analyze API files', error);
  }
}

async function testAdminMiddleware() {
  log('Testing admin middleware...', 'info');
  
  try {
    // Test middleware file exists
    const middlewarePath = path.join(process.cwd(), 'src/middleware/admin.ts');
    const middlewareExists = fs.existsSync(middlewarePath);
    
    addTestResult(
      'Middleware: File exists',
      middlewareExists,
      middlewareExists ? 'Admin middleware file found' : 'Admin middleware file missing'
    );
    
    if (middlewareExists) {
      const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
      
      // Check for key functions
      const hasWithAdminAuth = middlewareContent.includes('withAdminAuth');
      const hasCheckAdminStatus = middlewareContent.includes('checkAdminStatus');
      const hasRequireAdminAccess = middlewareContent.includes('requireAdminAccess');
      
      addTestResult(
        'Middleware: Core functions',
        hasWithAdminAuth && hasCheckAdminStatus && hasRequireAdminAccess,
        `withAdminAuth: ${hasWithAdminAuth}, checkAdminStatus: ${hasCheckAdminStatus}, requireAdminAccess: ${hasRequireAdminAccess}`
      );
    }
    
  } catch (error) {
    addTestResult('Middleware: Analysis', false, 'Failed to analyze middleware', error);
  }
}

async function testAdminScripts() {
  log('Testing admin management scripts...', 'info');
  
  try {
    // Test create-admin script exists
    const scriptPath = path.join(process.cwd(), 'scripts/create-admin.ts');
    const scriptExists = fs.existsSync(scriptPath);
    
    addTestResult(
      'Scripts: create-admin.ts exists',
      scriptExists,
      scriptExists ? 'Admin creation script found' : 'Admin creation script missing'
    );
    
    // Test package.json script entry
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const hasCreateAdminScript = packageJson.scripts && packageJson.scripts['create-admin'];
      
      addTestResult(
        'Scripts: npm script entry',
        !!hasCreateAdminScript,
        hasCreateAdminScript ? `Script: ${hasCreateAdminScript}` : 'No create-admin script in package.json'
      );
    }
    
  } catch (error) {
    addTestResult('Scripts: Analysis', false, 'Failed to analyze admin scripts', error);
  }
}

async function testSecurityImprovements() {
  log('Testing security improvements...', 'info');
  
  try {
    // Test that no hardcoded admin emails remain in codebase
    const searchDirs = ['src/app/api', 'src/lib', 'src/middleware'];
    let hardcodedChecksFound = 0;
    
    for (const dir of searchDirs) {
      const dirPath = path.join(process.cwd(), dir);
      if (fs.existsSync(dirPath)) {
        const files = getAllTsFiles(dirPath);
        
        for (const file of files) {
          const content = fs.readFileSync(file, 'utf8');
          if (content.includes('process.env.ADMIN_EMAIL')) {
            hardcodedChecksFound++;
            log(`Found hardcoded check in: ${file}`, 'warning');
          }
        }
      }
    }
    
    addTestResult(
      'Security: No hardcoded admin checks',
      hardcodedChecksFound === 0,
      `Found ${hardcodedChecksFound} hardcoded admin email checks`
    );
    
  } catch (error) {
    addTestResult('Security: Codebase scan', false, 'Failed to scan codebase', error);
  }
}

function getAllTsFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getAllTsFiles(fullPath));
    } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

async function cleanup(testUsers) {
  log('Cleaning up test data...', 'info');
  
  try {
    if (testUsers && testUsers.length > 0) {
      const userIds = testUsers.map(user => user.id);
      await prisma.user.deleteMany({
        where: {
          id: {
            in: userIds
          }
        }
      });
      
      log(`Cleaned up ${userIds.length} test users`, 'success');
    }
  } catch (error) {
    log(`Cleanup failed: ${error.message}`, 'error');
  }
}

function generateReport() {
  const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  
  console.log('\n' + '='.repeat(60));
  console.log('🐭 TESTERAT - ADMIN SYSTEM TEST REPORT');
  console.log('='.repeat(60));
  console.log(`📊 Total Tests: ${testResults.total}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Pass Rate: ${passRate}%`);
  console.log('='.repeat(60));
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach(error => {
      console.log(`  • ${error.test}: ${error.error}`);
    });
  }
  
  // Save detailed report
  const reportPath = path.join(process.cwd(), 'docs/deployment/TESTERAT_ADMIN_REPORT.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  console.log(`\n📄 Detailed report saved: ${reportPath}`);
  
  return passRate >= 90;
}

// Main test execution
async function runAllTests() {
  console.log('🐭 TESTERAT - Starting Admin System Tests...\n');
  
  let testUsers = [];
  
  try {
    await testDatabaseSchema();
    await testAdminUtilityFunctions();
    testUsers = await testUserRoleAssignment();
    await testAPIRouteProtection(testUsers);
    await testAdminMiddleware();
    await testAdminScripts();
    await testSecurityImprovements();
    
  } catch (error) {
    log(`Critical test failure: ${error.message}`, 'error');
  } finally {
    await cleanup(testUsers);
    await prisma.$disconnect();
  }
  
  const success = generateReport();
  
  if (success) {
    console.log('\n🎉 TESTERAT VERDICT: ADMIN SYSTEM IMPLEMENTATION SUCCESSFUL!');
    process.exit(0);
  } else {
    console.log('\n💥 TESTERAT VERDICT: ADMIN SYSTEM NEEDS FIXES!');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('💥 Testerat crashed:', error);
    process.exit(1);
  });
}

module.exports = { runAllTests, testResults };
