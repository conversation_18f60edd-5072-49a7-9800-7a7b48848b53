#!/usr/bin/env node

/**
 * TESTERAT - Robust End-to-End Resume Builder Testing
 * Comprehensive testing with proper UI state handling
 */

const { chromium } = require('playwright');

const CONFIG = {
  baseUrl: 'http://localhost:3002',
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  timeout: 30000,
  headless: false,
  slowMo: 1000
};

const LINKEDIN_TEST_DATA = `
<PERSON>
Senior Software Engineer at Tech Corp
San Francisco, CA
<EMAIL>
linkedin.com/in/johndoe

About:
Experienced software engineer with 7+ years developing scalable web applications using React, Node.js, Python, and AWS. Led teams of 5+ developers and delivered products serving 500k+ users.

Experience:
Senior Software Engineer - Tech Corp (2021 - Present)
• Architected and built microservices handling 1M+ daily requests
• Led migration from monolith to microservices, reducing deployment time by 80%
• Mentored 5 junior developers and established code review best practices

Software Engineer - StartupXYZ (2019 - 2021)
• Developed React applications with 99.9% uptime serving 100k+ users
• Built RESTful APIs using Node.js and PostgreSQL

Education:
Master of Science in Computer Science
Stanford University (2015 - 2017)
GPA: 3.8

Skills:
JavaScript, Python, React, Node.js, AWS, Docker, PostgreSQL
`;

class RobustEndToEndTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
    this.createdResumeIds = [];
  }

  async init() {
    console.log('🎯 ROBUST END-TO-END RESUME BUILDER TESTING');
    console.log('===========================================');
    
    this.browser = await chromium.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    this.page = await context.newPage();
    this.page.setDefaultTimeout(CONFIG.timeout);
    
    console.log('✅ Browser initialized\n');
  }

  async addTestResult(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed,
      details: details || []
    });
    
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${testName}: ${status}`);
    if (details && details.length > 0) {
      details.forEach(detail => console.log(`   • ${detail}`));
    }
  }

  async testAuthentication() {
    console.log('🔐 TEST 1: Authentication');
    console.log('=========================');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/login`);
      await this.page.waitForLoadState('networkidle');
      
      await this.page.fill('input[type="email"]', CONFIG.testUser.email);
      await this.page.fill('input[type="password"]', CONFIG.testUser.password);
      
      await this.page.click('button:has-text("Sign in")');
      await this.page.waitForTimeout(3000);
      
      const currentUrl = this.page.url();
      const success = !currentUrl.includes('/login');
      
      await this.addTestResult('Authentication', success, [
        success ? 'Login successful' : 'Login failed',
        `Current URL: ${currentUrl}`
      ]);
      
      return success;
    } catch (error) {
      await this.addTestResult('Authentication', false, [`Error: ${error.message}`]);
      return false;
    }
  }

  async testPageAccess() {
    console.log('\n📄 TEST 2: Page Access');
    console.log('======================');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForLoadState('networkidle');
      
      const title = await this.page.textContent('h1');
      const hasImportButton = await this.page.isVisible('text=Import from LinkedIn');
      const hasUploadButton = await this.page.isVisible('text=Upload Existing');
      const hasCreateButton = await this.page.isVisible('text=Create New Resume');
      
      const success = hasImportButton && hasUploadButton && hasCreateButton;
      
      await this.addTestResult('Page Access', success, [
        `Page title: ${title}`,
        `LinkedIn Import: ${hasImportButton ? 'Found' : 'Missing'}`,
        `Upload Button: ${hasUploadButton ? 'Found' : 'Missing'}`,
        `Create Button: ${hasCreateButton ? 'Found' : 'Missing'}`
      ]);
      
      return success;
    } catch (error) {
      await this.addTestResult('Page Access', false, [`Error: ${error.message}`]);
      return false;
    }
  }

  async testLinkedInImportFlow() {
    console.log('\n🔗 TEST 3: LinkedIn Import');
    console.log('==========================');
    
    try {
      // Click LinkedIn import button
      await this.page.click('text=Import from LinkedIn');
      await this.page.waitForTimeout(1000);
      
      // Verify modal opened
      const modalVisible = await this.page.isVisible('h2:has-text("Import from LinkedIn")');
      if (!modalVisible) {
        await this.addTestResult('LinkedIn Import', false, ['Modal did not open']);
        return false;
      }
      
      // Fill in data
      await this.page.fill('textarea', LINKEDIN_TEST_DATA);
      await this.page.waitForTimeout(500);
      
      // Check if button is enabled
      const buttonEnabled = await this.page.isEnabled('button:has-text("Import & Create Resume")');
      if (!buttonEnabled) {
        await this.addTestResult('LinkedIn Import', false, ['Import button not enabled after entering data']);
        return false;
      }
      
      // Click import
      await this.page.click('button:has-text("Import & Create Resume")');
      
      // Wait for processing with multiple checks
      let redirected = false;
      let attempts = 0;
      const maxAttempts = 20;
      
      while (!redirected && attempts < maxAttempts) {
        await this.page.waitForTimeout(1000);
        const currentUrl = this.page.url();
        
        if (currentUrl.includes('/resume-builder/edit/')) {
          redirected = true;
          const resumeId = currentUrl.split('/').pop();
          this.createdResumeIds.push(resumeId);
          break;
        }
        
        // Check for success message
        const hasSuccess = await this.page.isVisible('text=success').catch(() => false) ||
                          await this.page.isVisible('text=created').catch(() => false) ||
                          await this.page.isVisible('text=imported').catch(() => false);
        
        if (hasSuccess) {
          console.log('   Success message detected, waiting for redirect...');
        }
        
        attempts++;
      }
      
      await this.addTestResult('LinkedIn Import', redirected, [
        redirected ? 'Import successful' : 'Import failed or timed out',
        redirected ? `Redirected to edit page` : `Still on: ${this.page.url()}`,
        `Processing time: ${attempts} seconds`
      ]);
      
      return redirected;
    } catch (error) {
      await this.addTestResult('LinkedIn Import', false, [`Error: ${error.message}`]);
      return false;
    }
  }

  async testDataPersistence() {
    console.log('\n🔍 TEST 4: Data Persistence');
    console.log('===========================');
    
    try {
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/resume-builder/edit/')) {
        await this.addTestResult('Data Persistence', false, ['Not on edit page']);
        return false;
      }
      
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(3000);
      
      // Check personal information
      const firstName = await this.page.inputValue('input[name="firstName"]').catch(() => '');
      const lastName = await this.page.inputValue('input[name="lastName"]').catch(() => '');
      const email = await this.page.inputValue('input[name="email"]').catch(() => '');
      
      // Check page content for imported data
      const pageContent = await this.page.textContent('body');
      const hasExperience = pageContent.includes('Tech Corp') || pageContent.includes('Software Engineer');
      const hasEducation = pageContent.includes('Stanford') || pageContent.includes('Computer Science');
      const hasSkills = pageContent.includes('JavaScript') || pageContent.includes('React');
      
      const hasPersonalInfo = firstName && lastName && email;
      const hasContentData = hasExperience || hasEducation || hasSkills;
      const success = hasPersonalInfo && hasContentData;
      
      await this.addTestResult('Data Persistence', success, [
        `Personal Info: ${hasPersonalInfo ? 'Complete' : 'Missing'}`,
        `  - First Name: "${firstName}"`,
        `  - Last Name: "${lastName}"`,
        `  - Email: "${email}"`,
        `Experience Data: ${hasExperience ? 'Found' : 'Missing'}`,
        `Education Data: ${hasEducation ? 'Found' : 'Missing'}`,
        `Skills Data: ${hasSkills ? 'Found' : 'Missing'}`
      ]);
      
      return success;
    } catch (error) {
      await this.addTestResult('Data Persistence', false, [`Error: ${error.message}`]);
      return false;
    }
  }

  async testResumeManagement() {
    console.log('\n📋 TEST 5: Resume Management');
    console.log('============================');
    
    try {
      // Navigate back to main page
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(2000);
      
      // Count resume cards
      const resumeCards = await this.page.locator('.bg-white.rounded-2xl').count();
      
      if (resumeCards > 0) {
        // Check first resume card for action buttons
        const firstCard = this.page.locator('.bg-white.rounded-2xl').first();
        const hasEditButton = await firstCard.locator('text=Edit').isVisible();
        const hasActionButtons = await firstCard.locator('button').count() >= 2;
        
        const success = hasEditButton && hasActionButtons;
        
        await this.addTestResult('Resume Management', success, [
          `Found ${resumeCards} resume(s)`,
          `Edit button: ${hasEditButton ? 'Present' : 'Missing'}`,
          `Action buttons: ${hasActionButtons ? 'Present' : 'Missing'}`
        ]);
        
        return success;
      } else {
        await this.addTestResult('Resume Management', false, ['No resumes found']);
        return false;
      }
    } catch (error) {
      await this.addTestResult('Resume Management', false, [`Error: ${error.message}`]);
      return false;
    }
  }

  async testAPIEndpoints() {
    console.log('\n🔧 TEST 6: API Endpoints');
    console.log('========================');
    
    try {
      const endpoints = [
        { url: '/api/ai/resume-parsing', expected: [200] },
        { url: '/api/resume-builder/import', expected: [405] }
      ];
      
      let workingCount = 0;
      const details = [];
      
      for (const endpoint of endpoints) {
        try {
          const response = await this.page.request.get(`${CONFIG.baseUrl}${endpoint.url}`);
          const status = response.status();
          
          if (endpoint.expected.includes(status)) {
            workingCount++;
            details.push(`${endpoint.url}: ✅ ${status}`);
          } else {
            details.push(`${endpoint.url}: ❌ ${status} (expected ${endpoint.expected.join('/')})`);
          }
        } catch (error) {
          details.push(`${endpoint.url}: ❌ Error`);
        }
      }
      
      const success = workingCount === endpoints.length;
      
      await this.addTestResult('API Endpoints', success, [
        `${workingCount}/${endpoints.length} endpoints working`,
        ...details
      ]);
      
      return success;
    } catch (error) {
      await this.addTestResult('API Endpoints', false, [`Error: ${error.message}`]);
      return false;
    }
  }

  async generateFinalReport() {
    console.log('\n📊 FINAL TEST RESULTS');
    console.log('=====================');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(test => test.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log(`\n🎯 SUMMARY:`);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${successRate}%`);
    
    console.log(`\n📋 DETAILED RESULTS:`);
    this.testResults.forEach(test => {
      const status = test.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`\n${test.name.toUpperCase()}: ${status}`);
      test.details.forEach(detail => {
        console.log(`   • ${detail}`);
      });
    });
    
    if (passedTests === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('===================');
      console.log('✅ Resume Builder is FULLY FUNCTIONAL!');
      console.log('✅ End-to-end flow works perfectly');
      console.log('✅ Data persistence is complete');
      console.log('✅ User experience is polished');
      console.log('\n🚀 PRODUCTION READY!');
      return true;
    } else if (successRate >= 80) {
      console.log('\n✅ MOSTLY FUNCTIONAL!');
      console.log('====================');
      console.log('✅ Core functionality works');
      console.log('⚠️  Some minor issues detected');
      return false;
    } else {
      console.log('\n❌ SIGNIFICANT ISSUES DETECTED');
      console.log('=============================');
      return false;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('\n🧹 Browser closed');
    }
  }

  async runCompleteTest() {
    try {
      await this.init();
      
      const authSuccess = await this.testAuthentication();
      if (!authSuccess) {
        console.log('\n❌ Authentication failed - cannot proceed');
        return false;
      }
      
      await this.testPageAccess();
      await this.testLinkedInImportFlow();
      await this.testDataPersistence();
      await this.testResumeManagement();
      await this.testAPIEndpoints();
      
      const success = await this.generateFinalReport();
      return success;
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

async function main() {
  const tester = new RobustEndToEndTest();
  const success = await tester.runCompleteTest();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { RobustEndToEndTest };
