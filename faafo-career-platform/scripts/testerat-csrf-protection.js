#!/usr/bin/env node

/**
 * 🐭 TESTERAT: CSRF Protection Implementation Validation
 * Comprehensive testing of CSRF protection system
 */

const fs = require('fs');
const path = require('path');

class CSRFProtectionTesterat {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.projectRoot = path.join(__dirname, '..');
  }

  test(name, testFn) {
    this.tests.push({ name, testFn });
  }

  async run() {
    console.log('🐭 TESTERAT: CSRF Protection Implementation Validation');
    console.log('=' .repeat(60));
    
    for (const { name, testFn } of this.tests) {
      try {
        await testFn();
        console.log(`✅ ${name}`);
        this.passed++;
      } catch (error) {
        console.log(`❌ ${name}: ${error.message}`);
        this.failed++;
      }
    }
    
    this.printSummary();
  }

  printSummary() {
    const total = this.passed + this.failed;
    const passRate = ((this.passed / total) * 100).toFixed(1);
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 TESTERAT VERDICT: CSRF PROTECTION IMPLEMENTATION');
    console.log(`📊 Total Tests: ${total}`);
    console.log(`✅ Passed: ${this.passed}`);
    console.log(`❌ Failed: ${this.failed}`);
    console.log(`📈 Pass Rate: ${passRate}%`);
    
    if (this.failed === 0) {
      console.log('🏆 PERFECT SCORE! CSRF protection implementation is excellent!');
    } else if (passRate >= 90) {
      console.log('🌟 EXCELLENT! CSRF protection implementation is very good!');
    } else if (passRate >= 80) {
      console.log('👍 GOOD! CSRF protection implementation needs minor improvements.');
    } else {
      console.log('⚠️  NEEDS WORK! CSRF protection implementation requires attention.');
    }
  }

  fileExists(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    return true;
  }

  fileContains(filePath, content) {
    const fullPath = path.join(this.projectRoot, filePath);
    const fileContent = fs.readFileSync(fullPath, 'utf8');
    if (!fileContent.includes(content)) {
      throw new Error(`File ${filePath} does not contain: ${content}`);
    }
    return true;
  }

  hasValidImport(filePath, importStatement) {
    const fullPath = path.join(this.projectRoot, filePath);
    const fileContent = fs.readFileSync(fullPath, 'utf8');
    const lines = fileContent.split('\n');
    const hasImport = lines.some(line => line.trim().includes(importStatement));
    if (!hasImport) {
      throw new Error(`File ${filePath} missing import: ${importStatement}`);
    }
    return true;
  }

  countProtectedRoutes() {
    const apiDir = path.join(this.projectRoot, 'src/app/api');
    let protectedCount = 0;
    let totalRoutes = 0;

    const scanDirectory = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scanDirectory(fullPath);
        } else if (item === 'route.ts') {
          totalRoutes++;
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // Check for CSRF protection
          if (content.includes('withCSRFProtection') || 
              content.includes('CSRF_CONFIGS') ||
              content.includes('withSecurityProtection')) {
            protectedCount++;
          }
        }
      }
    };

    scanDirectory(apiDir);
    return { protectedCount, totalRoutes };
  }
}

// Create test instance
const testerat = new CSRFProtectionTesterat();

// Test 1: CSRF middleware file exists
testerat.test('CSRF middleware file exists', () => {
  testerat.fileExists('src/lib/csrf-middleware.ts');
});

// Test 2: CSRF middleware has proper imports
testerat.test('CSRF middleware imports NextRequest/NextResponse', () => {
  testerat.hasValidImport('src/lib/csrf-middleware.ts', 'NextRequest');
  testerat.hasValidImport('src/lib/csrf-middleware.ts', 'NextResponse');
});

// Test 3: CSRF middleware has core functions
testerat.test('CSRF middleware has core protection functions', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'withCSRFProtection');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'withSecurityProtection');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'CSRF_CONFIGS');
});

// Test 4: SimpleSecurity has enhanced CSRF functions
testerat.test('SimpleSecurity has enhanced CSRF functions', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'generateCSRFToken');
  testerat.fileContains('src/lib/simple-security.ts', 'validateCSRFToken');
  testerat.fileContains('src/lib/simple-security.ts', 'storeCSRFToken');
});

// Test 5: CSRF token endpoint updated
testerat.test('CSRF token endpoint properly enhanced', () => {
  testerat.fileContains('src/app/api/csrf-token/route.ts', 'storeCSRFToken');
  testerat.fileContains('src/app/api/csrf-token/route.ts', 'expiresAt');
});

// Test 6: High-security routes protected
testerat.test('Admin database route has CSRF protection', () => {
  testerat.fileContains('src/app/api/admin/database/route.ts', 'withCSRFProtection');
  testerat.fileContains('src/app/api/admin/database/route.ts', 'CSRF_CONFIGS');
});

// Test 7: Auth routes protected
testerat.test('Auth reset-password route has CSRF protection', () => {
  testerat.fileContains('src/app/api/auth/reset-password/route.ts', 'withCSRFProtection');
  testerat.fileContains('src/app/api/auth/reset-password/route.ts', 'HIGH_SECURITY');
});

// Test 8: Profile route protected
testerat.test('Profile route has CSRF protection', () => {
  testerat.fileContains('src/app/api/profile/route.ts', 'withCSRFProtection');
  testerat.fileContains('src/app/api/profile/route.ts', 'HIGH_SECURITY');
});

// Test 9: CSRF configurations exist
testerat.test('CSRF security configurations defined', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'HIGH_SECURITY');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'MEDIUM_SECURITY');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'LOW_SECURITY');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'DISABLED');
});

// Test 10: CSRF token validation has proper checks
testerat.test('CSRF validation has comprehensive security checks', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'Token already used');
  testerat.fileContains('src/lib/simple-security.ts', 'Token expired');
  testerat.fileContains('src/lib/simple-security.ts', 'Token session mismatch');
});

// Test 11: Redis integration for CSRF tokens
testerat.test('CSRF tokens use Redis for storage', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'executeRedisCommand');
  testerat.fileContains('src/lib/simple-security.ts', 'csrf_token:');
  testerat.fileContains('src/lib/simple-security.ts', 'pexpire');
});

// Test 12: CSRF middleware has proper error handling
testerat.test('CSRF middleware has proper error handling', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'try {');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'catch (error)');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'status: 403');
});

// Test 13: CSRF violation logging
testerat.test('CSRF violations are properly logged', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'CSRF violation');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'console.warn');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'logViolations');
});

// Test 14: HTTP method filtering
testerat.test('CSRF protection filters HTTP methods correctly', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'GET');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'HEAD');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'OPTIONS');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'POST');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'PUT');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'PATCH');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'DELETE');
});

// Test 15: CSRF test script exists
testerat.test('CSRF test script exists and is comprehensive', () => {
  testerat.fileExists('scripts/test-csrf-protection.ts');
  testerat.fileContains('scripts/test-csrf-protection.ts', 'testCSRFTokenGeneration');
  testerat.fileContains('scripts/test-csrf-protection.ts', 'testCSRFTokenValidation');
  testerat.fileContains('scripts/test-csrf-protection.ts', 'testCSRFMiddleware');
});

// Test 16: Package.json has CSRF scripts
testerat.test('Package.json includes CSRF test scripts', () => {
  testerat.fileContains('package.json', 'test-csrf');
  testerat.fileContains('package.json', 'audit-csrf');
});

// Test 17: CSRF token has proper structure
testerat.test('CSRF token generation has proper structure', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'randomBytes');
  testerat.fileContains('src/lib/simple-security.ts', 'timestamp');
  testerat.fileContains('src/lib/simple-security.ts', 'sessionId');
});

// Test 18: CSRF protection prevents replay attacks
testerat.test('CSRF protection prevents replay attacks', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'markCSRFTokenAsUsed');
  testerat.fileContains('src/lib/simple-security.ts', 'used: boolean');
});

// Test 19: CSRF tokens have expiration
testerat.test('CSRF tokens have proper expiration handling', () => {
  testerat.fileContains('src/lib/simple-security.ts', '60 * 60 * 1000'); // 1 hour
  testerat.fileContains('src/lib/simple-security.ts', 'tokenAge');
  testerat.fileContains('src/lib/simple-security.ts', 'Token expired');
});

// Test 20: CSRF middleware supports different security levels
testerat.test('CSRF middleware supports configurable security levels', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'CSRFOptions');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'errorMessage');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'logViolations');
});

// Test 21: CSRF protection count
testerat.test('Significant number of routes have CSRF protection', () => {
  const { protectedCount, totalRoutes } = testerat.countProtectedRoutes();
  console.log(`   Protected routes: ${protectedCount}/${totalRoutes}`);
  
  if (protectedCount < 3) {
    throw new Error(`Only ${protectedCount} routes protected, need at least 3 high-priority routes`);
  }
  return true;
});

// Test 22: CSRF headers properly handled
testerat.test('CSRF implementation handles headers correctly', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'x-csrf-token');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'x-session-id');
});

// Test 23: CSRF utility functions exist
testerat.test('CSRF utility functions are available', () => {
  testerat.fileContains('src/lib/csrf-middleware.ts', 'generateCSRFTokenForClient');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'validateCSRFTokenManual');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'extractCSRFToken');
});

// Test 24: CSRF integration with existing security
testerat.test('CSRF integrates with existing security systems', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'withSimpleSecurity');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'SimpleSecurity');
});

// Test 25: CSRF implementation is production-ready
testerat.test('CSRF implementation has production-ready features', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'cleanupExpiredCSRFTokens');
  testerat.fileContains('src/lib/csrf-middleware.ts', 'Internal server error');
  testerat.fileContains('src/lib/simple-security.ts', 'falling back to memory');
});

// Run all tests
testerat.run().catch(console.error);
