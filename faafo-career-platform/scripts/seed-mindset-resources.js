const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const mindsetResources = [
  // Fear & Anxiety Management
  {
    title: 'How to Deal with Career Change Anxiety',
    description: 'Evidence-based strategies from Harvard Business Review for managing anxiety and fear during career transitions, including practical exercises and mindset shifts.',
    url: 'https://hbr.org/2022/09/how-to-deal-with-layoff-anxiety',
    type: 'ARTICLE',
    category: 'FEAR_ANXIETY_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'Harvard Business Review',
    duration: '8 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Overcoming Career Change Fear: A Psychology Today Guide',
    description: 'Psychological insights and practical techniques for managing fear, uncertainty, and anxiety when making major career transitions.',
    url: 'https://www.psychologytoday.com/us/blog/cutting-edge-leadership/202408/why-are-people-so-resistant-to-change',
    type: 'ARTICLE',
    category: 'FEAR_ANXIETY_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'Psychology Today',
    duration: '10 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Mindfulness for Career Transitions',
    description: 'Mayo Clinic-backed mindfulness exercises and meditation techniques specifically designed to reduce stress and anxiety during career changes.',
    url: 'https://www.mayoclinic.org/healthy-lifestyle/consumer-health/in-depth/mindfulness-exercises/art-20046356',
    type: 'ARTICLE',
    category: 'FEAR_ANXIETY_MANAGEMENT',
    skillLevel: 'BEGINNER',
    author: 'Mayo Clinic',
    duration: '6 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Financial Planning
  {
    title: 'Financial Planning for Career Transitions',
    description: 'Comprehensive guide covering emergency funds, budgeting during career changes, and financial strategies for smooth transitions.',
    url: 'https://www.ussfcu.org/media-center/senate-cents-a-financial-wellness-blog/blog-detail.html?cId=92216&title=preparing-for-a-career-change-financial-steps-to-take-before-your-next-move',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'USS Federal Credit Union',
    duration: '12 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Building Your Career Change Emergency Fund',
    description: 'Step-by-step guide to building financial security before making a career transition, including budgeting worksheets and savings strategies.',
    url: 'https://finred.usalearning.gov/Planning/EaseTransitions',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'BEGINNER',
    author: 'FINRED',
    duration: '15 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'How to Think Strategically About Career Transition Finances',
    description: 'Strategic financial planning approaches for career transitions, covering risk assessment, income planning, and investment considerations.',
    url: 'https://hbr.org/2023/09/how-to-think-strategically-about-a-career-transition',
    type: 'ARTICLE',
    category: 'FINANCIAL_LITERACY',
    skillLevel: 'INTERMEDIATE',
    author: 'Harvard Business Review',
    duration: '10 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Imposter Syndrome & Confidence Building
  {
    title: 'Overcoming Imposter Syndrome in Career Changes',
    description: 'Research-backed strategies for building confidence and overcoming self-doubt when transitioning to new career fields.',
    url: 'https://impostorsyndrome.com/articles/10-steps-overcome-impostor/',
    type: 'ARTICLE',
    category: 'IMPOSTER_SYNDROME',
    skillLevel: 'BEGINNER',
    author: 'Impostor Syndrome Institute',
    duration: '12 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Building Confidence During Career Transitions',
    description: 'Practical exercises and mindset shifts to build genuine confidence and overcome self-doubt when entering new professional fields.',
    url: 'https://www.linkedin.com/pulse/helpful-resources-overcoming-imposter-syndrome-rebecca-o-hare--dt2qe',
    type: 'ARTICLE',
    category: 'IMPOSTER_SYNDROME',
    skillLevel: 'BEGINNER',
    author: 'LinkedIn Learning',
    duration: '8 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Reese Witherspoon on Turning Impostor Syndrome into Confidence',
    description: 'TED Talk featuring practical insights on transforming self-doubt into confidence, especially relevant for career changers.',
    url: 'https://www.ted.com/pages/reese-witherspoon-on-impostor-syndrome-confidence-transcript',
    type: 'VIDEO',
    category: 'IMPOSTER_SYNDROME',
    skillLevel: 'BEGINNER',
    author: 'TED',
    duration: '15 min watch',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Motivation & Inspiration
  {
    title: 'How to Find Work You Love',
    description: 'Inspiring TED Talk by Scott Dinsmore on discovering meaningful work and overcoming fear to pursue your passion.',
    url: 'https://www.youtube.com/watch?v=vVsXO9brK7M',
    type: 'VIDEO',
    category: 'MOTIVATION_INSPIRATION',
    skillLevel: 'BEGINNER',
    author: 'TED',
    duration: '18 min watch',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'How to Know Your Life Purpose in 5 Minutes',
    description: 'Quick but powerful framework for discovering your life purpose and aligning your career with your values.',
    url: 'https://www.youtube.com/watch?v=vVsXO9brK7M',
    type: 'VIDEO',
    category: 'MOTIVATION_INSPIRATION',
    skillLevel: 'BEGINNER',
    author: 'Adam Leipzig',
    duration: '5 min watch',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'The Career Change Podcast',
    description: 'Weekly interviews with people who have successfully changed careers, sharing their stories, strategies, and lessons learned.',
    url: 'https://thecareerchangepodcast.com/',
    type: 'PODCAST',
    category: 'MOTIVATION_INSPIRATION',
    skillLevel: 'BEGINNER',
    author: 'Anna Lundberg',
    duration: 'Various episodes',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Strategic Planning
  {
    title: 'The Complete Guide to Career Pivoting',
    description: 'Comprehensive framework for planning and executing successful career pivots, including assessment tools and action plans.',
    url: 'https://www.linkedin.com/pulse/complete-guide-career-pivoting-jenny-foss/',
    type: 'ARTICLE',
    category: 'STRATEGIC_PLANNING',
    skillLevel: 'INTERMEDIATE',
    author: 'LinkedIn Learning',
    duration: '15 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Designing Your Life: Career Design Thinking',
    description: 'Stanford-developed methodology for applying design thinking principles to career and life decisions.',
    url: 'https://designingyour.life/',
    type: 'ARTICLE',
    category: 'STRATEGIC_PLANNING',
    skillLevel: 'INTERMEDIATE',
    author: 'Bill Burnett & Dave Evans',
    duration: '20 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Stop Worrying About Making the Right Career Decision',
    description: 'Harvard Business Review guide to making career decisions with confidence, even when facing uncertainty.',
    url: 'https://hbr.org/2013/11/stop-worrying-about-making-the-right-decision',
    type: 'ARTICLE',
    category: 'STRATEGIC_PLANNING',
    skillLevel: 'BEGINNER',
    author: 'Harvard Business Review',
    duration: '8 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Personal Development
  {
    title: 'The Art of Self-Compassion in Career Transitions',
    description: 'Research-based approach to being kind to yourself during the ups and downs of career change, reducing stress and increasing resilience.',
    url: 'https://self-compassion.org/the-three-elements-of-self-compassion-2/',
    type: 'ARTICLE',
    category: 'PERSONAL_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Dr. Kristin Neff',
    duration: '10 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },
  {
    title: 'Growth Mindset for Career Changers',
    description: 'How to develop a growth mindset that embraces challenges and sees failures as learning opportunities during career transitions.',
    url: 'https://www.mindsetworks.com/science/',
    type: 'ARTICLE',
    category: 'PERSONAL_DEVELOPMENT',
    skillLevel: 'BEGINNER',
    author: 'Mindset Works',
    duration: '12 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  },

  // Career Guidance
  {
    title: 'What Color Is Your Parachute? Career Assessment',
    description: 'Classic career assessment and guidance framework for discovering your ideal career path and making strategic career decisions.',
    url: 'https://www.parachutebook.com/',
    type: 'ARTICLE',
    category: 'CAREER_GUIDANCE',
    skillLevel: 'BEGINNER',
    author: 'Richard N. Bolles',
    duration: '30 min read',
    cost: 'FREEMIUM',
    format: 'SELF_PACED'
  },
  {
    title: 'Career Transition Roadmap',
    description: 'Step-by-step roadmap for navigating career transitions, including timeline planning, skill assessment, and networking strategies.',
    url: 'https://www.leedsbeckett.ac.uk/peopledevelopment/learning-resources/',
    type: 'ARTICLE',
    category: 'CAREER_GUIDANCE',
    skillLevel: 'INTERMEDIATE',
    author: 'Leeds Beckett University',
    duration: '25 min read',
    cost: 'FREE',
    format: 'SELF_PACED'
  }
];

async function seedMindsetResources() {
  console.log('Starting to seed mindset resources...');
  
  try {
    for (const resource of mindsetResources) {
      const existingResource = await prisma.learningResource.findFirst({
        where: { url: resource.url }
      });

      if (!existingResource) {
        await prisma.learningResource.create({
          data: resource
        });
        console.log(`✅ Created: ${resource.title}`);
      } else {
        console.log(`⏭️  Skipped (already exists): ${resource.title}`);
      }
    }
    
    console.log('\n🎉 Mindset resources seeding completed!');
    console.log(`📊 Total resources processed: ${mindsetResources.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding mindset resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedMindsetResources();
