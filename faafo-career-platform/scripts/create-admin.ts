#!/usr/bin/env tsx

/**
 * Admin User Management Script
 * 
 * This script allows you to:
 * - Create admin users
 * - Promote existing users to admin
 * - Demote admin users to regular users
 * - List all admin users
 * 
 * Usage:
 *   npm run create-admin <email>           # Promote user to admin
 *   npm run create-admin --list            # List all admins
 *   npm run create-admin --demote <email>  # Demote admin to user
 */

import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

interface AdminScriptOptions {
  email?: string;
  list?: boolean;
  demote?: boolean;
  create?: boolean;
  password?: string;
  name?: string;
}

async function createNewAdmin(email: string, password: string, name?: string): Promise<void> {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      console.log(`❌ User with email ${email} already exists. Use promote option instead.`);
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create new admin user
    const newAdmin = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name: name || email.split('@')[0],
        role: UserRole.ADMIN,
        emailVerified: new Date(), // Auto-verify admin accounts
      }
    });

    console.log(`✅ Created new admin user: ${newAdmin.email} (ID: ${newAdmin.id})`);
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    throw error;
  }
}

async function promoteToAdmin(email: string): Promise<void> {
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, role: true, name: true }
    });

    if (!user) {
      console.log(`❌ User with email ${email} not found.`);
      return;
    }

    if (user.role === UserRole.ADMIN || user.role === UserRole.SUPER_ADMIN) {
      console.log(`ℹ️  User ${email} is already an admin (${user.role}).`);
      return;
    }

    const updatedUser = await prisma.user.update({
      where: { email },
      data: { role: UserRole.ADMIN }
    });

    console.log(`✅ User ${email} has been promoted to admin.`);
    console.log(`   Name: ${updatedUser.name || 'Not set'}`);
    console.log(`   Role: ${updatedUser.role}`);
  } catch (error) {
    console.error('❌ Error promoting user to admin:', error);
    throw error;
  }
}

async function demoteFromAdmin(email: string): Promise<void> {
  try {
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, role: true, name: true }
    });

    if (!user) {
      console.log(`❌ User with email ${email} not found.`);
      return;
    }

    if (user.role === UserRole.USER) {
      console.log(`ℹ️  User ${email} is already a regular user.`);
      return;
    }

    const updatedUser = await prisma.user.update({
      where: { email },
      data: { role: UserRole.USER }
    });

    console.log(`✅ User ${email} has been demoted to regular user.`);
    console.log(`   Name: ${updatedUser.name || 'Not set'}`);
    console.log(`   Role: ${updatedUser.role}`);
  } catch (error) {
    console.error('❌ Error demoting user from admin:', error);
    throw error;
  }
}

async function listAdmins(): Promise<void> {
  try {
    const admins = await prisma.user.findMany({
      where: {
        role: {
          in: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
        }
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true,
        emailVerified: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    if (admins.length === 0) {
      console.log('📋 No admin users found.');
      return;
    }

    console.log(`📋 Found ${admins.length} admin user(s):\n`);
    
    admins.forEach((admin, index) => {
      console.log(`${index + 1}. ${admin.email}`);
      console.log(`   Name: ${admin.name || 'Not set'}`);
      console.log(`   Role: ${admin.role}`);
      console.log(`   ID: ${admin.id}`);
      console.log(`   Created: ${admin.createdAt.toISOString()}`);
      console.log(`   Email Verified: ${admin.emailVerified ? '✅' : '❌'}`);
      console.log('');
    });
  } catch (error) {
    console.error('❌ Error listing admin users:', error);
    throw error;
  }
}

async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔧 Admin User Management Script

Usage:
  npm run create-admin <email>                    # Promote existing user to admin
  npm run create-admin --create <email> <password> [name]  # Create new admin user
  npm run create-admin --list                     # List all admin users
  npm run create-admin --demote <email>           # Demote admin to regular user

Examples:
  npm run create-admin <EMAIL>
  npm run create-admin --create <EMAIL> mypassword123 "Admin User"
  npm run create-admin --list
  npm run create-admin --demote <EMAIL>
`);
    return;
  }

  try {
    if (args[0] === '--list') {
      await listAdmins();
    } else if (args[0] === '--demote' && args[1]) {
      await demoteFromAdmin(args[1]);
    } else if (args[0] === '--create' && args[1] && args[2]) {
      await createNewAdmin(args[1], args[2], args[3]);
    } else if (args[0] && !args[0].startsWith('--')) {
      await promoteToAdmin(args[0]);
    } else {
      console.log('❌ Invalid arguments. Use --help for usage information.');
    }
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

export { createNewAdmin, promoteToAdmin, demoteFromAdmin, listAdmins };
