#!/usr/bin/env node

/**
 * TESTERAT - Diagnostic Test for Resume Builder
 * Detailed debugging of the import flow
 */

const { chromium } = require('playwright');

const CONFIG = {
  baseUrl: 'http://localhost:3002',
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword'
  },
  timeout: 30000,
  headless: false,
  slowMo: 1500
};

const LINKEDIN_TEST_DATA = `
<PERSON>
Senior Software Engineer at Tech Corp
San Francisco, CA
<EMAIL>
linkedin.com/in/johndoe

About:
Experienced software engineer with 7+ years developing scalable web applications.

Experience:
Senior Software Engineer - Tech Corp (2021 - Present)
• Architected and built microservices handling 1M+ daily requests

Education:
Master of Science in Computer Science
Stanford University (2015 - 2017)

Skills:
JavaScript, Python, React, Node.js
`;

class DiagnosticTest {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async init() {
    console.log('🔍 DIAGNOSTIC TEST - Resume Builder Import Flow');
    console.log('===============================================');
    
    this.browser = await chromium.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    this.page = await context.newPage();
    this.page.setDefaultTimeout(CONFIG.timeout);
    
    // Listen to console messages
    this.page.on('console', msg => {
      console.log(`🖥️  CONSOLE: ${msg.text()}`);
    });
    
    // Listen to network requests
    this.page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log(`📡 REQUEST: ${request.method()} ${request.url()}`);
      }
    });
    
    // Listen to network responses
    this.page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`📨 RESPONSE: ${response.status()} ${response.url()}`);
      }
    });
    
    console.log('✅ Browser initialized with debugging\n');
  }

  async authenticate() {
    console.log('🔐 Step 1: Authentication');
    console.log('=========================');
    
    await this.page.goto(`${CONFIG.baseUrl}/login`);
    await this.page.waitForLoadState('networkidle');
    
    await this.page.fill('input[type="email"]', CONFIG.testUser.email);
    await this.page.fill('input[type="password"]', CONFIG.testUser.password);
    
    await this.page.click('button:has-text("Sign in")');
    await this.page.waitForTimeout(3000);
    
    const currentUrl = this.page.url();
    console.log(`✅ Authentication complete. URL: ${currentUrl}\n`);
  }

  async navigateToResumeBuilder() {
    console.log('📄 Step 2: Navigate to Resume Builder');
    console.log('=====================================');
    
    await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
    await this.page.waitForLoadState('networkidle');
    
    const title = await this.page.textContent('h1');
    console.log(`✅ Page loaded: ${title}\n`);
  }

  async testLinkedInImportDetailed() {
    console.log('🔗 Step 3: Detailed LinkedIn Import Test');
    console.log('========================================');
    
    // Click LinkedIn import button
    console.log('📝 Clicking LinkedIn Import button...');
    await this.page.click('text=Import from LinkedIn');
    await this.page.waitForTimeout(2000);
    
    // Check modal state
    const modalVisible = await this.page.isVisible('h2:has-text("Import from LinkedIn")');
    console.log(`✅ Modal opened: ${modalVisible}`);
    
    if (!modalVisible) {
      console.log('❌ Modal not found, stopping test');
      return;
    }
    
    // Fill textarea
    console.log('📝 Filling LinkedIn data...');
    await this.page.fill('textarea', LINKEDIN_TEST_DATA);
    await this.page.waitForTimeout(1000);
    
    // Check button state
    const buttonEnabled = await this.page.isEnabled('button:has-text("Import & Create Resume")');
    console.log(`✅ Import button enabled: ${buttonEnabled}`);
    
    if (!buttonEnabled) {
      console.log('❌ Import button not enabled, checking why...');
      
      // Check if textarea has content
      const textareaValue = await this.page.inputValue('textarea');
      console.log(`📝 Textarea content length: ${textareaValue.length}`);
      
      // Check for any error messages
      const errorVisible = await this.page.isVisible('.text-red-600, .text-red-800').catch(() => false);
      console.log(`⚠️  Error message visible: ${errorVisible}`);
      
      return;
    }
    
    // Click import button
    console.log('🚀 Clicking Import & Create Resume button...');
    await this.page.click('button:has-text("Import & Create Resume")');
    
    // Monitor the import process
    console.log('⏳ Monitoring import process...');
    
    for (let i = 0; i < 30; i++) {
      await this.page.waitForTimeout(1000);
      
      const currentUrl = this.page.url();
      console.log(`   ${i + 1}s: URL = ${currentUrl}`);
      
      // Check for progress messages
      const progressVisible = await this.page.isVisible('text=Parsing').catch(() => false);
      const successVisible = await this.page.isVisible('text=success').catch(() => false);
      const errorVisible = await this.page.isVisible('.text-red-600, .text-red-800').catch(() => false);
      
      if (progressVisible) console.log(`   📊 Progress indicator visible`);
      if (successVisible) console.log(`   ✅ Success message visible`);
      if (errorVisible) {
        console.log(`   ❌ Error message visible`);
        const errorText = await this.page.textContent('.text-red-600, .text-red-800').catch(() => 'Unknown error');
        console.log(`   Error: ${errorText}`);
      }
      
      // Check if redirected
      if (currentUrl.includes('/resume-builder/edit/')) {
        console.log(`✅ Successfully redirected to edit page!`);
        const resumeId = currentUrl.split('/').pop();
        console.log(`📄 Resume ID: ${resumeId}`);
        return true;
      }
      
      // Check if modal is still open
      const modalStillOpen = await this.page.isVisible('h2:has-text("Import from LinkedIn")').catch(() => false);
      if (!modalStillOpen && !currentUrl.includes('/resume-builder/edit/')) {
        console.log(`⚠️  Modal closed but not redirected. Current URL: ${currentUrl}`);
        break;
      }
    }
    
    console.log('❌ Import process timed out or failed');
    return false;
  }

  async checkNetworkRequests() {
    console.log('\n🌐 Step 4: Manual API Test');
    console.log('==========================');
    
    try {
      // Test the parsing API directly
      console.log('📡 Testing resume parsing API...');
      const response = await this.page.request.post(`${CONFIG.baseUrl}/api/ai/resume-parsing`, {
        data: {
          resumeText: LINKEDIN_TEST_DATA,
          action: 'parse_linkedin'
        }
      });
      
      console.log(`📨 Response status: ${response.status()}`);
      
      if (response.ok()) {
        const result = await response.json();
        console.log(`✅ API response successful`);
        console.log(`📊 Response data:`, JSON.stringify(result, null, 2));
      } else {
        const errorText = await response.text();
        console.log(`❌ API error: ${errorText}`);
      }
    } catch (error) {
      console.log(`❌ Network test error: ${error.message}`);
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('\n🧹 Browser closed');
    }
  }

  async runDiagnostic() {
    try {
      await this.init();
      await this.authenticate();
      await this.navigateToResumeBuilder();
      await this.testLinkedInImportDetailed();
      await this.checkNetworkRequests();
      
      console.log('\n🎯 DIAGNOSTIC COMPLETE');
      console.log('======================');
      console.log('Check the output above for detailed information about the import flow.');
      
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
    } finally {
      await this.cleanup();
    }
  }
}

async function main() {
  const tester = new DiagnosticTest();
  await tester.runDiagnostic();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { DiagnosticTest };
