#!/usr/bin/env node

/**
 * 🐭 TESTERAT - Simplified Admin Performance Testing
 * 
 * Performance testing focused on database operations and basic metrics
 */

const { PrismaClient, UserRole } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Test configuration
const TEST_CONFIG = {
  performanceThresholds: {
    userCreation: 1000, // ms
    roleQuery: 200, // ms
    bulkQuery: 500, // ms
    userUpdate: 300 // ms
  },
  testSizes: {
    small: 5,
    medium: 20,
    large: 50
  }
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: [],
  details: [],
  performanceMetrics: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '🔍',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    perf: '⚡'
  }[type];
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function addTestResult(testName, passed, details = '', error = null, metrics = null) {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    log(`${testName}: PASSED ${details}`, 'success');
  } else {
    testResults.failed++;
    log(`${testName}: FAILED ${details}`, 'error');
    if (error) {
      testResults.errors.push({ test: testName, error: error.message });
    }
  }
  
  testResults.details.push({
    test: testName,
    passed,
    details,
    metrics,
    timestamp: new Date().toISOString()
  });
  
  if (metrics) {
    testResults.performanceMetrics.push({
      test: testName,
      ...metrics
    });
  }
}

function measureMemory() {
  const usage = process.memoryUsage();
  return {
    rss: Math.round(usage.rss / 1024 / 1024), // MB
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
    external: Math.round(usage.external / 1024 / 1024) // MB
  };
}

// Performance test functions
async function testUserCreationPerformance() {
  log('Testing user creation performance...', 'perf');
  
  const createdUsers = [];
  
  try {
    // Test 1: Single user creation performance
    const startTime = Date.now();
    const hashedPassword = await bcrypt.hash('testpassword123', 12);
    
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Performance Test User',
        role: UserRole.USER,
        emailVerified: new Date()
      }
    });
    
    const singleUserTime = Date.now() - startTime;
    createdUsers.push(user);
    
    addTestResult(
      'Performance: Single user creation',
      singleUserTime < TEST_CONFIG.performanceThresholds.userCreation,
      `${singleUserTime}ms (threshold: ${TEST_CONFIG.performanceThresholds.userCreation}ms)`,
      null,
      { operation: 'single_user_creation', duration: singleUserTime }
    );
    
    // Test 2: Bulk user creation performance
    const bulkStartTime = Date.now();
    const bulkUsers = [];
    
    for (let i = 0; i < TEST_CONFIG.testSizes.small; i++) {
      const bulkUser = await prisma.user.create({
        data: {
          email: `testerat-perf-bulk-${i}@example.com`,
          password: hashedPassword,
          name: `Bulk Test User ${i}`,
          role: i % 2 === 0 ? UserRole.USER : UserRole.ADMIN,
          emailVerified: new Date()
        }
      });
      bulkUsers.push(bulkUser);
    }
    
    const bulkTime = Date.now() - bulkStartTime;
    createdUsers.push(...bulkUsers);
    
    const avgTimePerUser = bulkTime / TEST_CONFIG.testSizes.small;
    
    addTestResult(
      'Performance: Bulk user creation',
      avgTimePerUser < TEST_CONFIG.performanceThresholds.userCreation,
      `${bulkTime}ms for ${TEST_CONFIG.testSizes.small} users (${avgTimePerUser.toFixed(2)}ms avg)`,
      null,
      { operation: 'bulk_user_creation', duration: bulkTime, count: TEST_CONFIG.testSizes.small }
    );
    
  } catch (error) {
    addTestResult(
      'Performance: User creation setup',
      false,
      'Failed to test user creation performance',
      error
    );
  }
  
  return createdUsers;
}

async function testQueryPerformance() {
  log('Testing query performance...', 'perf');
  
  try {
    // Test 3: Role-based query performance
    const roleQueryStart = Date.now();
    
    const adminUsers = await prisma.user.findMany({
      where: {
        role: {
          in: ['ADMIN', 'SUPER_ADMIN']
        }
      },
      select: { id: true, email: true, role: true }
    });
    
    const roleQueryTime = Date.now() - roleQueryStart;
    
    addTestResult(
      'Performance: Role-based query',
      roleQueryTime < TEST_CONFIG.performanceThresholds.roleQuery,
      `${roleQueryTime}ms for ${adminUsers.length} admin users`,
      null,
      { operation: 'role_query', duration: roleQueryTime, resultCount: adminUsers.length }
    );
    
    // Test 4: User count query performance
    const countQueryStart = Date.now();
    
    const userCount = await prisma.user.count();
    const adminCount = await prisma.user.count({
      where: {
        role: {
          in: ['ADMIN', 'SUPER_ADMIN']
        }
      }
    });
    
    const countQueryTime = Date.now() - countQueryStart;
    
    addTestResult(
      'Performance: Count queries',
      countQueryTime < TEST_CONFIG.performanceThresholds.roleQuery,
      `${countQueryTime}ms for counting ${userCount} total users, ${adminCount} admins`,
      null,
      { operation: 'count_query', duration: countQueryTime, totalUsers: userCount, adminUsers: adminCount }
    );
    
    // Test 5: Complex query performance
    const complexQueryStart = Date.now();
    
    const recentUsers = await prisma.user.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      select: { id: true, email: true, role: true, createdAt: true },
      orderBy: { createdAt: 'desc' },
      take: 10
    });
    
    const complexQueryTime = Date.now() - complexQueryStart;
    
    addTestResult(
      'Performance: Complex query with filters',
      complexQueryTime < TEST_CONFIG.performanceThresholds.bulkQuery,
      `${complexQueryTime}ms for recent users query (${recentUsers.length} results)`,
      null,
      { operation: 'complex_query', duration: complexQueryTime, resultCount: recentUsers.length }
    );
    
  } catch (error) {
    addTestResult(
      'Performance: Query tests',
      false,
      'Failed to test query performance',
      error
    );
  }
}

async function testUpdatePerformance() {
  log('Testing update performance...', 'perf');
  
  try {
    // Find a test user to update
    const testUser = await prisma.user.findFirst({
      where: {
        email: {
          contains: 'testerat-perf'
        }
      }
    });
    
    if (!testUser) {
      addTestResult(
        'Performance: Update test setup',
        false,
        'No test user found for update performance test'
      );
      return;
    }
    
    // Test 6: Single user update performance
    const updateStart = Date.now();
    
    await prisma.user.update({
      where: { id: testUser.id },
      data: { 
        name: 'Updated Performance Test User',
        updatedAt: new Date()
      }
    });
    
    const updateTime = Date.now() - updateStart;
    
    addTestResult(
      'Performance: Single user update',
      updateTime < TEST_CONFIG.performanceThresholds.userUpdate,
      `${updateTime}ms (threshold: ${TEST_CONFIG.performanceThresholds.userUpdate}ms)`,
      null,
      { operation: 'single_update', duration: updateTime }
    );
    
    // Test 7: Role update performance
    const roleUpdateStart = Date.now();
    const originalRole = testUser.role;
    const newRole = originalRole === 'USER' ? 'ADMIN' : 'USER';
    
    await prisma.user.update({
      where: { id: testUser.id },
      data: { role: newRole }
    });
    
    // Revert the role change
    await prisma.user.update({
      where: { id: testUser.id },
      data: { role: originalRole }
    });
    
    const roleUpdateTime = Date.now() - roleUpdateStart;
    
    addTestResult(
      'Performance: Role update',
      roleUpdateTime < TEST_CONFIG.performanceThresholds.userUpdate * 2, // Allow more time for role updates
      `${roleUpdateTime}ms for role change and revert`,
      null,
      { operation: 'role_update', duration: roleUpdateTime }
    );
    
  } catch (error) {
    addTestResult(
      'Performance: Update tests',
      false,
      'Failed to test update performance',
      error
    );
  }
}

async function testMemoryUsage() {
  log('Testing memory usage during operations...', 'perf');
  
  try {
    const startMemory = measureMemory();
    
    // Test 8: Memory usage during bulk operations
    const bulkQueryStart = Date.now();
    
    // Perform several memory-intensive operations
    const allUsers = await prisma.user.findMany({
      select: { id: true, email: true, role: true, createdAt: true }
    });
    
    const usersByRole = await prisma.user.groupBy({
      by: ['role'],
      _count: {
        role: true
      }
    });
    
    const bulkQueryTime = Date.now() - bulkQueryStart;
    const endMemory = measureMemory();
    const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed;
    
    addTestResult(
      'Performance: Memory usage during bulk operations',
      memoryIncrease < 100, // Less than 100MB increase
      `Memory increase: ${memoryIncrease}MB, Query time: ${bulkQueryTime}ms`,
      null,
      { 
        operation: 'memory_usage', 
        duration: bulkQueryTime,
        startMemory: startMemory.heapUsed,
        endMemory: endMemory.heapUsed,
        memoryIncrease
      }
    );
    
    addTestResult(
      'Performance: Bulk query results',
      allUsers.length > 0 && usersByRole.length > 0,
      `Found ${allUsers.length} users, ${usersByRole.length} role groups`
    );
    
  } catch (error) {
    addTestResult(
      'Performance: Memory usage test',
      false,
      'Failed to test memory usage',
      error
    );
  }
}

async function cleanup(testUsers) {
  log('Cleaning up performance test data...', 'info');
  
  try {
    if (testUsers && testUsers.length > 0) {
      const userIds = testUsers.map(user => user.id);
      await prisma.user.deleteMany({
        where: { id: { in: userIds } }
      });
      log(`Cleaned up ${userIds.length} test users`, 'success');
    }
    
    // Clean up any additional test users
    await prisma.user.deleteMany({
      where: {
        email: { contains: 'testerat-perf' }
      }
    });
    
  } catch (error) {
    log(`Performance test cleanup failed: ${error.message}`, 'error');
  }
}

function generatePerformanceReport() {
  const passRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  
  console.log('\n' + '='.repeat(70));
  console.log('🐭 TESTERAT - SIMPLIFIED ADMIN PERFORMANCE TEST REPORT');
  console.log('='.repeat(70));
  console.log(`📊 Total Tests: ${testResults.total}`);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Pass Rate: ${passRate}%`);
  console.log('='.repeat(70));
  
  // Performance metrics summary
  if (testResults.performanceMetrics.length > 0) {
    console.log('\n⚡ PERFORMANCE METRICS:');
    testResults.performanceMetrics.forEach(metric => {
      console.log(`  • ${metric.test}: ${metric.duration?.toFixed(2) || 'N/A'}ms`);
    });
  }
  
  if (testResults.failed > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.errors.forEach(error => {
      console.log(`  • ${error.test}: ${error.error}`);
    });
  }
  
  // Performance assessment
  const avgDuration = testResults.performanceMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / testResults.performanceMetrics.length;
  
  if (passRate >= 95 && avgDuration < 200) {
    console.log('\n🚀 PERFORMANCE ASSESSMENT: EXCELLENT - High Performance');
  } else if (passRate >= 85 && avgDuration < 500) {
    console.log('\n⚡ PERFORMANCE ASSESSMENT: GOOD - Acceptable Performance');
  } else {
    console.log('\n⚠️ PERFORMANCE ASSESSMENT: NEEDS OPTIMIZATION');
  }
  
  return passRate >= 90;
}

// Main test execution
async function runSimplePerformanceTests() {
  console.log('🐭 TESTERAT - Starting Simplified Admin Performance Tests...\n');
  
  let testUsers = [];
  
  try {
    testUsers = await testUserCreationPerformance();
    await testQueryPerformance();
    await testUpdatePerformance();
    await testMemoryUsage();
    
  } catch (error) {
    log(`Critical performance test failure: ${error.message}`, 'error');
  } finally {
    await cleanup(testUsers);
    await prisma.$disconnect();
  }
  
  const success = generatePerformanceReport();
  
  if (success) {
    console.log('\n🎉 TESTERAT PERFORMANCE VERDICT: ADMIN SYSTEM IS FAST!');
    process.exit(0);
  } else {
    console.log('\n💥 TESTERAT PERFORMANCE VERDICT: PERFORMANCE ISSUES DETECTED!');
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runSimplePerformanceTests().catch(error => {
    console.error('💥 Testerat performance tests crashed:', error);
    process.exit(1);
  });
}

module.exports = { runSimplePerformanceTests, testResults };
