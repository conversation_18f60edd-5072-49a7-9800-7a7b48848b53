#!/usr/bin/env node

/**
 * 🐭 TESTERAT: Redis Rate Limiting Implementation Validation
 * Comprehensive testing of Redis-based rate limiting with memory fallback
 */

const fs = require('fs');
const path = require('path');

class RedisRateLimitingTesterat {
  constructor() {
    this.tests = [];
    this.passed = 0;
    this.failed = 0;
    this.projectRoot = path.join(__dirname, '..');
  }

  test(name, testFn) {
    this.tests.push({ name, testFn });
  }

  async run() {
    console.log('🐭 TESTERAT: Redis Rate Limiting Implementation Validation');
    console.log('=' .repeat(60));
    
    for (const { name, testFn } of this.tests) {
      try {
        await testFn();
        console.log(`✅ ${name}`);
        this.passed++;
      } catch (error) {
        console.log(`❌ ${name}: ${error.message}`);
        this.failed++;
      }
    }
    
    this.printSummary();
  }

  printSummary() {
    const total = this.passed + this.failed;
    const passRate = ((this.passed / total) * 100).toFixed(1);
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 TESTERAT VERDICT: REDIS RATE LIMITING IMPLEMENTATION');
    console.log(`📊 Total Tests: ${total}`);
    console.log(`✅ Passed: ${this.passed}`);
    console.log(`❌ Failed: ${this.failed}`);
    console.log(`📈 Pass Rate: ${passRate}%`);
    
    if (this.failed === 0) {
      console.log('🏆 PERFECT SCORE! Redis rate limiting implementation is excellent!');
    } else if (passRate >= 90) {
      console.log('🌟 EXCELLENT! Redis rate limiting implementation is very good!');
    } else if (passRate >= 80) {
      console.log('👍 GOOD! Redis rate limiting implementation needs minor improvements.');
    } else {
      console.log('⚠️  NEEDS WORK! Redis rate limiting implementation requires attention.');
    }
  }

  fileExists(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    return true;
  }

  fileContains(filePath, content) {
    const fullPath = path.join(this.projectRoot, filePath);
    const fileContent = fs.readFileSync(fullPath, 'utf8');
    if (!fileContent.includes(content)) {
      throw new Error(`File ${filePath} does not contain: ${content}`);
    }
    return true;
  }

  fileNotContains(filePath, content) {
    const fullPath = path.join(this.projectRoot, filePath);
    const fileContent = fs.readFileSync(fullPath, 'utf8');
    if (fileContent.includes(content)) {
      throw new Error(`File ${filePath} should not contain: ${content}`);
    }
    return true;
  }

  hasValidImport(filePath, importStatement) {
    const fullPath = path.join(this.projectRoot, filePath);
    const fileContent = fs.readFileSync(fullPath, 'utf8');
    const lines = fileContent.split('\n');
    const hasImport = lines.some(line => line.trim().includes(importStatement));
    if (!hasImport) {
      throw new Error(`File ${filePath} missing import: ${importStatement}`);
    }
    return true;
  }
}

// Create test instance
const testerat = new RedisRateLimitingTesterat();

// Test 1: Redis client configuration exists
testerat.test('Redis client configuration file exists', () => {
  testerat.fileExists('src/lib/redis.ts');
});

// Test 2: Redis client has proper imports
testerat.test('Redis client imports ioredis', () => {
  testerat.hasValidImport('src/lib/redis.ts', "import Redis from 'ioredis'");
});

// Test 3: Redis client has connection configuration
testerat.test('Redis client has connection configuration', () => {
  testerat.fileContains('src/lib/redis.ts', 'redisConfig');
  testerat.fileContains('src/lib/redis.ts', 'REDIS_HOST');
  testerat.fileContains('src/lib/redis.ts', 'REDIS_PORT');
});

// Test 4: Redis client has error handling
testerat.test('Redis client has proper error handling', () => {
  testerat.fileContains('src/lib/redis.ts', 'try {');
  testerat.fileContains('src/lib/redis.ts', 'catch (error)');
  testerat.fileContains('src/lib/redis.ts', 'console.warn');
});

// Test 5: Redis client has connection management
testerat.test('Redis client has connection management functions', () => {
  testerat.fileContains('src/lib/redis.ts', 'getRedisClient');
  testerat.fileContains('src/lib/redis.ts', 'isRedisAvailable');
  testerat.fileContains('src/lib/redis.ts', 'disconnectRedis');
});

// Test 6: Main rate limiter updated with Redis
testerat.test('Main rate limiter imports Redis utilities', () => {
  testerat.hasValidImport('src/lib/rate-limit.ts', 'getRedisClient');
  testerat.hasValidImport('src/lib/rate-limit.ts', 'isRedisAvailable');
  testerat.hasValidImport('src/lib/rate-limit.ts', 'executeRedisCommand');
});

// Test 7: Rate limiter has Redis implementation
testerat.test('Rate limiter has Redis-based checking', () => {
  testerat.fileContains('src/lib/rate-limit.ts', 'checkRedisRateLimit');
  testerat.fileContains('src/lib/rate-limit.ts', 'checkMemoryRateLimit');
});

// Test 8: Rate limiter check method is async
testerat.test('Rate limiter check method is async', () => {
  testerat.fileContains('src/lib/rate-limit.ts', 'public async check');
  testerat.fileContains('src/lib/rate-limit.ts', 'Promise<{');
});

// Test 9: Alternative rate limiter updated
testerat.test('Alternative rate limiter imports Redis utilities', () => {
  testerat.hasValidImport('src/lib/rateLimit.ts', 'getRedisClient');
  testerat.hasValidImport('src/lib/rateLimit.ts', 'isRedisAvailable');
});

// Test 10: Alternative rate limiter has Redis functions
testerat.test('Alternative rate limiter has Redis functions', () => {
  testerat.fileContains('src/lib/rateLimit.ts', 'redisRateLimit');
  testerat.fileContains('src/lib/rateLimit.ts', 'memoryRateLimit');
});

// Test 11: Middleware updated with Redis
testerat.test('Middleware imports Redis utilities', () => {
  testerat.hasValidImport('middleware.ts', 'getRedisClient');
  testerat.hasValidImport('middleware.ts', 'isRedisAvailable');
});

// Test 12: Middleware has Redis rate limiting
testerat.test('Middleware has Redis rate limiting functions', () => {
  testerat.fileContains('middleware.ts', 'redisRateLimitCheck');
  testerat.fileContains('middleware.ts', 'memoryRateLimitCheck');
});

// Test 13: Middleware isRateLimited is async
testerat.test('Middleware isRateLimited function is async', () => {
  testerat.fileContains('middleware.ts', 'async function isRateLimited');
  testerat.fileContains('middleware.ts', 'await isRateLimited');
});

// Test 14: SimpleSecurity updated with Redis
testerat.test('SimpleSecurity imports Redis utilities', () => {
  testerat.hasValidImport('src/lib/simple-security.ts', 'getRedisClient');
  testerat.hasValidImport('src/lib/simple-security.ts', 'isRedisAvailable');
});

// Test 15: SimpleSecurity has async rate limiting
testerat.test('SimpleSecurity has async rate limiting method', () => {
  testerat.fileContains('src/lib/simple-security.ts', 'checkRateLimitAsync');
  testerat.fileContains('src/lib/simple-security.ts', 'Promise<boolean>');
});

// Test 16: Rate limit headers include backend info
testerat.test('Rate limit headers include backend information', () => {
  testerat.fileContains('src/lib/rate-limit.ts', 'X-RateLimit-Backend');
  testerat.fileContains('middleware.ts', 'X-RateLimit-Backend');
});

// Test 17: Environment configuration added
testerat.test('Environment configuration includes Redis settings', () => {
  // Check both possible locations for .env.example
  try {
    testerat.fileContains('.env.example', 'REDIS_URL');
    testerat.fileContains('.env.example', 'REDIS_HOST');
    testerat.fileContains('.env.example', 'REDIS_PORT');
  } catch (error) {
    try {
      // Try parent directory
      testerat.fileContains('../.env.example', 'REDIS_URL');
      testerat.fileContains('../.env.example', 'REDIS_HOST');
      testerat.fileContains('../.env.example', 'REDIS_PORT');
    } catch (error2) {
      // Try two levels up
      testerat.fileContains('../../.env.example', 'REDIS_URL');
      testerat.fileContains('../../.env.example', 'REDIS_HOST');
      testerat.fileContains('../../.env.example', 'REDIS_PORT');
    }
  }
});

// Test 18: Test script exists
testerat.test('Redis rate limiting test script exists', () => {
  testerat.fileExists('scripts/test-redis-rate-limiting.ts');
});

// Test 19: Test script has comprehensive tests
testerat.test('Test script has comprehensive test functions', () => {
  testerat.fileContains('scripts/test-redis-rate-limiting.ts', 'testRedisConnection');
  testerat.fileContains('scripts/test-redis-rate-limiting.ts', 'testRateLimiterClass');
  testerat.fileContains('scripts/test-redis-rate-limiting.ts', 'testConcurrentRequests');
});

// Test 20: Package.json has test script
testerat.test('Package.json includes Redis test script', () => {
  testerat.fileContains('package.json', 'test-redis');
  testerat.fileContains('package.json', 'scripts/test-redis-rate-limiting.ts');
});

// Test 21: Redis keys use proper prefixes
testerat.test('Redis keys use proper prefixes for organization', () => {
  testerat.fileContains('src/lib/rate-limit.ts', 'rate_limit:');
  testerat.fileContains('src/lib/rateLimit.ts', 'rate_limit_v2:');
  testerat.fileContains('middleware.ts', 'middleware_rate_limit:');
});

// Test 22: Redis commands use proper expiration
testerat.test('Redis commands use proper key expiration', () => {
  testerat.fileContains('src/lib/rate-limit.ts', 'pexpire');
  testerat.fileContains('src/lib/rateLimit.ts', 'pexpire');
  testerat.fileContains('middleware.ts', 'pexpire');
});

// Test 23: Fallback logic is implemented
testerat.test('Fallback logic properly implemented', () => {
  testerat.fileContains('src/lib/rate-limit.ts', 'falling back to memory');
  testerat.fileContains('src/lib/rateLimit.ts', 'falling back to memory');
  testerat.fileContains('middleware.ts', 'falling back to memory');
});

// Test 24: No hardcoded Redis dependencies
testerat.test('No hardcoded Redis dependencies in core logic', () => {
  testerat.fileContains('src/lib/rate-limit.ts', 'if (isRedisAvailable())');
  testerat.fileContains('src/lib/rateLimit.ts', 'if (isRedisAvailable())');
  testerat.fileContains('middleware.ts', 'if (isRedisAvailable())');
});

// Test 25: ioredis dependency exists
testerat.test('ioredis dependency is installed', () => {
  testerat.fileContains('package.json', '"ioredis"');
});

// Run all tests
testerat.run().catch(console.error);
