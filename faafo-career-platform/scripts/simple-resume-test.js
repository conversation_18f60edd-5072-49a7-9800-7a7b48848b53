#!/usr/bin/env node

/**
 * Simple Resume Builder Test - Manual Login Required
 * Tests the Resume Builder functionality after manual login
 */

const puppeteer = require('puppeteer');

const CONFIG = {
  baseUrl: 'http://localhost:3001',
  timeout: 30000,
  headless: false,
  slowMo: 500
};

const LINKEDIN_TEST_DATA = `
<PERSON>
Senior Software Engineer at Tech Corp
San Francisco, CA
<EMAIL>
linkedin.com/in/johndoe

About:
Experienced software engineer with 7+ years developing scalable web applications using React, Node.js, Python, and AWS. Led teams of 5+ developers and delivered products serving 500k+ users.

Experience:
Senior Software Engineer - Tech Corp (2021 - Present)
• Architected and built microservices handling 1M+ daily requests
• Led migration from monolith to microservices, reducing deployment time by 80%
• Mentored 5 junior developers and established code review best practices

Software Engineer - StartupXYZ (2019 - 2021)
• Developed React applications with 99.9% uptime serving 100k+ users
• Built RESTful APIs using Node.js and PostgreSQL

Education:
Master of Science in Computer Science
Stanford University (2015 - 2017)
GPA: 3.8

Bachelor of Science in Computer Science
University of California, Berkeley (2011 - 2015)
GPA: 3.6

Skills:
Programming Languages: JavaScript, Python, Java, TypeScript, Go
Frontend: React, Vue.js, Angular, HTML5, CSS3, Sass
Backend: Node.js, Express, Django, Flask, Spring Boot
Databases: PostgreSQL, MongoDB, Redis, MySQL
Cloud: AWS, Google Cloud, Azure, Docker, Kubernetes
`;

class SimpleResumeTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      pageAccess: false,
      linkedinImport: false,
      dataVerification: false
    };
  }

  async init() {
    console.log('🚀 Initializing Simple Resume Builder Test...');
    
    this.browser = await puppeteer.launch({
      headless: CONFIG.headless,
      slowMo: CONFIG.slowMo,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1280, height: 720 });
    this.page.setDefaultTimeout(CONFIG.timeout);
    
    console.log('✅ Browser initialized');
    console.log('📝 MANUAL STEP: Please login manually when the browser opens');
  }

  async waitForManualLogin() {
    console.log('\n⏳ Waiting for manual login...');
    console.log('📝 Please:');
    console.log('   1. Navigate to http://localhost:3001/login');
    console.log('   2. Login with: <EMAIL> / testpassword');
    console.log('   3. Press ENTER in this terminal when logged in');
    
    // Open login page
    await this.page.goto(`${CONFIG.baseUrl}/login`);
    
    // Wait for user input
    await new Promise(resolve => {
      process.stdin.once('data', () => {
        resolve();
      });
    });
    
    console.log('✅ Proceeding with tests...');
  }

  async testResumeBuilderAccess() {
    console.log('\n📄 Testing Resume Builder Page Access...');
    
    try {
      await this.page.goto(`${CONFIG.baseUrl}/resume-builder`);
      await this.page.waitForSelector('h1', { timeout: 10000 });
      
      const title = await this.page.$eval('h1', el => el.textContent);
      console.log(`✅ Page loaded with title: "${title}"`);
      
      // Check for key elements
      const hasImportButton = await this.page.$('text=Import from LinkedIn') !== null;
      const hasUploadButton = await this.page.$('text=Upload Existing') !== null;
      
      console.log(`   LinkedIn Import button: ${hasImportButton ? '✅' : '❌'}`);
      console.log(`   Upload button: ${hasUploadButton ? '✅' : '❌'}`);
      
      this.results.pageAccess = hasImportButton && hasUploadButton;
      return this.results.pageAccess;
    } catch (error) {
      console.log(`❌ Page access error: ${error.message}`);
      return false;
    }
  }

  async testLinkedInImport() {
    console.log('\n🔗 Testing LinkedIn Import...');
    
    try {
      // Look for LinkedIn Import button with various selectors
      const importButton = await this.page.$('text=Import from LinkedIn') || 
                          await this.page.$('button:has-text("Import from LinkedIn")') ||
                          await this.page.$('[data-testid="linkedin-import"]') ||
                          await this.page.$('button[aria-label*="LinkedIn"]');
      
      if (!importButton) {
        console.log('❌ LinkedIn Import button not found');
        return false;
      }
      
      console.log('✅ LinkedIn Import button found');
      
      // Click the button
      await importButton.click();
      await this.page.waitForTimeout(1000);
      
      // Look for modal or textarea
      const modal = await this.page.$('.modal') || 
                   await this.page.$('[role="dialog"]') ||
                   await this.page.$('textarea');
      
      if (!modal) {
        console.log('❌ LinkedIn import modal/form not found');
        return false;
      }
      
      console.log('✅ LinkedIn import interface opened');
      
      // Try to find textarea and fill it
      const textarea = await this.page.$('textarea');
      if (textarea) {
        await textarea.type(LINKEDIN_TEST_DATA);
        console.log('✅ LinkedIn data entered');
        
        // Look for submit button
        const submitButton = await this.page.$('button:has-text("Import")') ||
                            await this.page.$('button:has-text("Submit")') ||
                            await this.page.$('button[type="submit"]');
        
        if (submitButton) {
          console.log('✅ Found submit button - ready to import');
          console.log('📝 MANUAL STEP: Click the import button to test the flow');
          
          // Wait for user to manually click
          console.log('⏳ Press ENTER after clicking import...');
          await new Promise(resolve => {
            process.stdin.once('data', () => {
              resolve();
            });
          });
          
          // Check if we're on edit page
          await this.page.waitForTimeout(3000);
          const currentUrl = this.page.url();
          
          if (currentUrl.includes('/resume-builder/edit/')) {
            console.log('✅ Successfully redirected to edit page');
            console.log(`   Edit URL: ${currentUrl}`);
            this.results.linkedinImport = true;
            return true;
          } else {
            console.log(`⚠️  Current URL: ${currentUrl}`);
            console.log('❌ Not redirected to edit page');
            return false;
          }
        } else {
          console.log('❌ Submit button not found');
          return false;
        }
      } else {
        console.log('❌ Textarea not found in modal');
        return false;
      }
    } catch (error) {
      console.log(`❌ LinkedIn import error: ${error.message}`);
      return false;
    }
  }

  async testDataVerification() {
    console.log('\n🔍 Testing Data Verification...');
    
    try {
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/resume-builder/edit/')) {
        console.log('❌ Not on edit page, skipping data verification');
        return false;
      }
      
      // Wait for page to load
      await this.page.waitForTimeout(2000);
      
      // Check for personal info
      const firstName = await this.page.$eval('input[name="firstName"]', el => el.value).catch(() => '');
      const lastName = await this.page.$eval('input[name="lastName"]', el => el.value).catch(() => '');
      const email = await this.page.$eval('input[name="email"]', el => el.value).catch(() => '');
      
      console.log(`   First Name: "${firstName}"`);
      console.log(`   Last Name: "${lastName}"`);
      console.log(`   Email: "${email}"`);
      
      const hasPersonalInfo = firstName && lastName && email;
      console.log(`   Personal Info: ${hasPersonalInfo ? '✅' : '❌'}`);
      
      // Check for sections
      const experienceSection = await this.page.$('[data-testid="experience-section"]') || 
                               await this.page.$('text=Experience') ||
                               await this.page.$('h2:has-text("Experience")');
      
      const educationSection = await this.page.$('[data-testid="education-section"]') || 
                              await this.page.$('text=Education') ||
                              await this.page.$('h2:has-text("Education")');
      
      const skillsSection = await this.page.$('[data-testid="skills-section"]') || 
                           await this.page.$('text=Skills') ||
                           await this.page.$('h2:has-text("Skills")');
      
      console.log(`   Experience Section: ${experienceSection ? '✅' : '❌'}`);
      console.log(`   Education Section: ${educationSection ? '✅' : '❌'}`);
      console.log(`   Skills Section: ${skillsSection ? '✅' : '❌'}`);
      
      const hasCompleteData = hasPersonalInfo && experienceSection;
      this.results.dataVerification = hasCompleteData;
      
      if (hasCompleteData) {
        console.log('✅ Data verification successful - resume data found');
      } else {
        console.log('❌ Data verification failed - incomplete resume data');
      }
      
      return hasCompleteData;
    } catch (error) {
      console.log(`❌ Data verification error: ${error.message}`);
      return false;
    }
  }

  async generateReport() {
    console.log('\n📊 SIMPLE TEST RESULTS');
    console.log('======================');
    
    const results = this.results;
    const passCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`Page Access: ${results.pageAccess ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`LinkedIn Import: ${results.linkedinImport ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Data Verification: ${results.dataVerification ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log(`\n🎯 Overall: ${passCount}/${totalTests} tests passed`);
    
    if (passCount === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('✅ Resume Builder is FULLY FUNCTIONAL!');
      console.log('✅ LinkedIn Import works end-to-end');
      console.log('✅ Data persistence is complete');
      console.log('\n🚀 PRODUCTION READY!');
    } else if (passCount >= 2) {
      console.log('\n✅ CORE FUNCTIONALITY WORKS!');
      console.log('⚠️  Minor issues may need attention');
    } else {
      console.log('\n❌ Critical functionality issues found');
    }
    
    return passCount === totalTests;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('\n🧹 Browser closed');
    }
  }

  async runTest() {
    try {
      await this.init();
      await this.waitForManualLogin();
      await this.testResumeBuilderAccess();
      await this.testLinkedInImport();
      await this.testDataVerification();
      const success = await this.generateReport();
      return success;
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

async function main() {
  console.log('🎯 SIMPLE RESUME BUILDER TEST - MANUAL LOGIN');
  console.log('============================================');
  
  const tester = new SimpleResumeTest();
  const success = await tester.runTest();
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { SimpleResumeTest };
