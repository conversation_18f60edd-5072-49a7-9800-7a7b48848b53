#!/usr/bin/env tsx

/**
 * Automated CSRF Protection Application Script
 * Applies CSRF protection to API routes based on security level
 */

import fs from 'fs';
import path from 'path';

interface RouteInfo {
  file: string;
  fullPath: string;
  securityLevel: 'HIGH' | 'MEDIUM' | 'LOW';
  methods: string[];
  needsCSRF: boolean;
}

class CSRFProtectionApplicator {
  private apiDir = path.join(__dirname, '../src/app/api');
  private routes: RouteInfo[] = [];

  async applyCSRFProtection(): Promise<void> {
    console.log('🛡️  Starting Automated CSRF Protection Application...\n');
    
    // Scan all routes
    await this.scanRoutes();
    
    // Apply protection by priority
    await this.applyProtectionByPriority();
    
    console.log('\n✅ CSRF Protection Application Complete!');
  }

  private async scanRoutes(): Promise<void> {
    const routeFiles = this.findAllRouteFiles();
    
    for (const file of routeFiles) {
      const routeInfo = await this.analyzeRouteFile(file);
      if (routeInfo.needsCSRF) {
        this.routes.push(routeInfo);
      }
    }
    
    console.log(`📊 Found ${this.routes.length} routes requiring CSRF protection:`);
    console.log(`   HIGH: ${this.routes.filter(r => r.securityLevel === 'HIGH').length}`);
    console.log(`   MEDIUM: ${this.routes.filter(r => r.securityLevel === 'MEDIUM').length}`);
    console.log(`   LOW: ${this.routes.filter(r => r.securityLevel === 'LOW').length}\n`);
  }

  private findAllRouteFiles(): string[] {
    const files: string[] = [];
    
    const scanDirectory = (dir: string): void => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scanDirectory(fullPath);
        } else if (item === 'route.ts') {
          files.push(fullPath);
        }
      }
    };
    
    scanDirectory(this.apiDir);
    return files;
  }

  private async analyzeRouteFile(filePath: string): Promise<RouteInfo> {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(this.apiDir, filePath);
    
    const methods = this.extractMethods(content);
    const stateChangingMethods = methods.filter(m => 
      ['POST', 'PUT', 'DELETE', 'PATCH'].includes(m)
    );
    
    return {
      file: relativePath,
      fullPath: filePath,
      securityLevel: this.determineSecurityLevel(relativePath),
      methods,
      needsCSRF: stateChangingMethods.length > 0 && !this.hasCSRFProtection(content)
    };
  }

  private extractMethods(content: string): string[] {
    const methods: string[] = [];
    const methodRegex = /export\s+(?:async\s+)?(?:function\s+|const\s+)?(GET|POST|PUT|DELETE|PATCH|OPTIONS|HEAD)/g;
    let match;
    
    while ((match = methodRegex.exec(content)) !== null) {
      methods.push(match[1]);
    }
    
    return methods;
  }

  private hasCSRFProtection(content: string): boolean {
    const csrfPatterns = [
      /withCSRFProtection/,
      /withSecurityProtection/,
      /requireCSRF.*true/,
      /CSRF_CONFIGS/,
    ];
    
    return csrfPatterns.some(pattern => pattern.test(content));
  }

  private determineSecurityLevel(routePath: string): 'HIGH' | 'MEDIUM' | 'LOW' {
    // High security routes (critical operations)
    if (routePath.includes('admin/') || 
        routePath.includes('auth/') ||
        routePath.includes('profile/') ||
        routePath.includes('errors/') ||
        routePath.includes('freedom-fund/')) {
      return 'HIGH';
    }
    
    // Medium security routes (user data operations)
    if (routePath.includes('forum/') ||
        routePath.includes('assessment/') ||
        routePath.includes('goals/') ||
        routePath.includes('achievements/') ||
        routePath.includes('learning-progress/') ||
        routePath.includes('progress-tracker/') ||
        routePath.includes('resource-ratings/') ||
        routePath.includes('career-paths/') ||
        routePath.includes('learning-paths/')) {
      return 'MEDIUM';
    }
    
    return 'LOW';
  }

  private async applyProtectionByPriority(): Promise<void> {
    // Sort by security level priority
    const sortedRoutes = this.routes.sort((a, b) => {
      const priority = { HIGH: 3, MEDIUM: 2, LOW: 1 };
      return priority[b.securityLevel] - priority[a.securityLevel];
    });

    for (const route of sortedRoutes) {
      try {
        await this.applyCSRFToRoute(route);
        console.log(`✅ ${route.securityLevel}: ${route.file}`);
      } catch (error) {
        console.log(`❌ ${route.securityLevel}: ${route.file} - ${error}`);
      }
    }
  }

  private async applyCSRFToRoute(route: RouteInfo): Promise<void> {
    let content = fs.readFileSync(route.fullPath, 'utf8');
    
    // Add import if not present
    if (!content.includes('withCSRFProtection')) {
      content = this.addCSRFImport(content);
    }

    // Apply CSRF protection to state-changing methods
    content = this.wrapMethodsWithCSRF(content, route);
    
    // Write back to file
    fs.writeFileSync(route.fullPath, content, 'utf8');
  }

  private addCSRFImport(content: string): string {
    const importLines = content.split('\n').filter(line => line.trim().startsWith('import'));
    const lastImportIndex = content.lastIndexOf(importLines[importLines.length - 1]);
    const lastImportEndIndex = content.indexOf('\n', lastImportIndex);
    
    const csrfImport = "import { withCSRFProtection, CSRF_CONFIGS } from '@/lib/csrf-middleware';";
    
    return content.slice(0, lastImportEndIndex + 1) + 
           csrfImport + '\n' + 
           content.slice(lastImportEndIndex + 1);
  }

  private wrapMethodsWithCSRF(content: string, route: RouteInfo): string {
    const stateChangingMethods = ['POST', 'PUT', 'DELETE', 'PATCH'];
    const securityConfig = route.securityLevel === 'HIGH' ? 'HIGH_SECURITY' : 
                          route.securityLevel === 'MEDIUM' ? 'MEDIUM_SECURITY' : 'LOW_SECURITY';

    for (const method of stateChangingMethods) {
      // Pattern for function exports
      const functionPattern = new RegExp(
        `export\\s+async\\s+function\\s+${method}\\s*\\([^)]*\\)\\s*{`,
        'g'
      );
      
      // Pattern for const exports
      const constPattern = new RegExp(
        `export\\s+const\\s+${method}\\s*=\\s*([^;]+);`,
        'g'
      );

      // Wrap function exports
      content = content.replace(functionPattern, (match) => {
        if (match.includes('withCSRFProtection')) return match;
        return match.replace(
          `export async function ${method}`,
          `export const ${method} = withCSRFProtection(async`
        ).replace('{', ') => {') + `, CSRF_CONFIGS.${securityConfig});`;
      });

      // Wrap const exports
      content = content.replace(constPattern, (match, handler) => {
        if (match.includes('withCSRFProtection')) return match;
        return `export const ${method} = withCSRFProtection(${handler.trim()}, CSRF_CONFIGS.${securityConfig});`;
      });
    }

    return content;
  }
}

// High-priority routes to apply CSRF protection manually (complex cases)
const HIGH_PRIORITY_ROUTES = [
  'auth/resend-verification/route.ts',
  'auth/reset-password/route.ts', 
  'auth/verify-email/route.ts',
  'profile/route.ts',
  'profile/photo/route.ts',
  'errors/route.ts',
  'freedom-fund/route.ts'
];

async function applyCSRFToHighPriorityRoutes(): Promise<void> {
  console.log('🎯 Applying CSRF protection to high-priority routes...\n');
  
  for (const routePath of HIGH_PRIORITY_ROUTES) {
    const fullPath = path.join(__dirname, '../src/app/api', routePath);
    
    if (fs.existsSync(fullPath)) {
      try {
        let content = fs.readFileSync(fullPath, 'utf8');
        
        // Skip if already protected
        if (content.includes('withCSRFProtection')) {
          console.log(`⏭️  ${routePath} - Already protected`);
          continue;
        }

        // Add import
        if (!content.includes('withCSRFProtection')) {
          const importLines = content.split('\n').filter(line => line.trim().startsWith('import'));
          const lastImportIndex = content.lastIndexOf(importLines[importLines.length - 1]);
          const lastImportEndIndex = content.indexOf('\n', lastImportIndex);
          
          const csrfImport = "import { withCSRFProtection, CSRF_CONFIGS } from '@/lib/csrf-middleware';";
          
          content = content.slice(0, lastImportEndIndex + 1) + 
                   csrfImport + '\n' + 
                   content.slice(lastImportEndIndex + 1);
        }

        // Apply CSRF protection to POST methods (most common in auth routes)
        content = content.replace(
          /export\s+async\s+function\s+POST\s*\([^)]*\)\s*{/g,
          'export const POST = withCSRFProtection(async (request: NextRequest) => {'
        );

        // Close the wrapper
        content = content.replace(/^}$/gm, '}, CSRF_CONFIGS.HIGH_SECURITY);');

        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ ${routePath} - CSRF protection applied`);
        
      } catch (error) {
        console.log(`❌ ${routePath} - Error: ${error}`);
      }
    } else {
      console.log(`⚠️  ${routePath} - File not found`);
    }
  }
}

// Run the applicator if script is executed directly
if (require.main === module) {
  const applicator = new CSRFProtectionApplicator();
  applicator.applyCSRFProtection()
    .then(() => applyCSRFToHighPriorityRoutes())
    .catch(console.error);
}

export { CSRFProtectionApplicator };
