#!/usr/bin/env node

/**
 * 🐭 TESTERAT - Master Admin Testing Suite
 * 
 * Comprehensive testing orchestrator for all admin system tests
 * Runs security, API, performance, and system tests in sequence
 */

const fs = require('fs');
const path = require('path');

// Test suite modules
const { runAllTests: runSystemTests } = require('./testerat-admin-system.js');
const { runSecurityTests } = require('./testerat-admin-security.js');
const { runAPITests } = require('./testerat-admin-api.js');
const { runPerformanceTests } = require('./testerat-admin-performance.js');

// Master test configuration
const MASTER_CONFIG = {
  testSuites: [
    {
      name: 'System Tests',
      description: 'Basic admin system functionality',
      runner: runSystemTests,
      critical: true,
      timeout: 60000 // 1 minute
    },
    {
      name: 'Security Tests',
      description: 'Security vulnerabilities and access control',
      runner: runSecurityTests,
      critical: true,
      timeout: 120000 // 2 minutes
    },
    {
      name: 'API Tests',
      description: 'Admin API functionality and integration',
      runner: runAPITests,
      critical: false,
      timeout: 90000 // 1.5 minutes
    },
    {
      name: 'Performance Tests',
      description: 'Performance and scalability testing',
      runner: runPerformanceTests,
      critical: false,
      timeout: 180000 // 3 minutes
    }
  ],
  reportPath: 'docs/deployment/TESTERAT_MASTER_REPORT.json'
};

// Master test results
let masterResults = {
  startTime: new Date().toISOString(),
  endTime: null,
  totalDuration: 0,
  suites: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    critical: 0,
    skipped: 0
  },
  verdict: null,
  recommendations: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '🔍',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    master: '🐭',
    critical: '🚨'
  }[type];
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
}

// Test suite runner with timeout and error handling
async function runTestSuite(suite) {
  log(`Starting ${suite.name}...`, 'master');
  
  const suiteResult = {
    name: suite.name,
    description: suite.description,
    critical: suite.critical,
    startTime: new Date().toISOString(),
    endTime: null,
    duration: 0,
    status: 'running',
    error: null,
    results: null
  };
  
  try {
    const startTime = Date.now();
    
    // Run test suite with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Test suite timeout after ${suite.timeout}ms`)), suite.timeout);
    });
    
    const testPromise = suite.runner();
    
    await Promise.race([testPromise, timeoutPromise]);
    
    const endTime = Date.now();
    suiteResult.duration = endTime - startTime;
    suiteResult.endTime = new Date().toISOString();
    suiteResult.status = 'passed';
    
    log(`${suite.name} completed successfully in ${formatDuration(suiteResult.duration)}`, 'success');
    
  } catch (error) {
    const endTime = Date.now();
    suiteResult.duration = endTime - (Date.now() - suiteResult.duration);
    suiteResult.endTime = new Date().toISOString();
    suiteResult.status = 'failed';
    suiteResult.error = error.message;
    
    if (suite.critical) {
      log(`${suite.name} CRITICAL FAILURE: ${error.message}`, 'critical');
    } else {
      log(`${suite.name} failed: ${error.message}`, 'error');
    }
  }
  
  return suiteResult;
}

// Generate comprehensive recommendations
function generateRecommendations(results) {
  const recommendations = [];
  
  // Check for critical failures
  const criticalFailures = results.suites.filter(s => s.critical && s.status === 'failed');
  if (criticalFailures.length > 0) {
    recommendations.push({
      priority: 'CRITICAL',
      category: 'Security',
      message: 'Critical test failures detected. DO NOT DEPLOY to production.',
      action: 'Fix all critical issues before proceeding with deployment.'
    });
  }
  
  // Check for security issues
  const securitySuite = results.suites.find(s => s.name === 'Security Tests');
  if (securitySuite && securitySuite.status === 'failed') {
    recommendations.push({
      priority: 'HIGH',
      category: 'Security',
      message: 'Security vulnerabilities detected in admin system.',
      action: 'Review and fix all security issues before production deployment.'
    });
  }
  
  // Check for performance issues
  const performanceSuite = results.suites.find(s => s.name === 'Performance Tests');
  if (performanceSuite && performanceSuite.status === 'failed') {
    recommendations.push({
      priority: 'MEDIUM',
      category: 'Performance',
      message: 'Performance issues detected in admin system.',
      action: 'Optimize admin operations for better performance under load.'
    });
  }
  
  // Check for API issues
  const apiSuite = results.suites.find(s => s.name === 'API Tests');
  if (apiSuite && apiSuite.status === 'failed') {
    recommendations.push({
      priority: 'MEDIUM',
      category: 'Integration',
      message: 'API integration issues detected.',
      action: 'Fix admin API endpoints and integration issues.'
    });
  }
  
  // Success recommendations
  if (results.summary.failed === 0) {
    recommendations.push({
      priority: 'INFO',
      category: 'Deployment',
      message: 'All admin system tests passed successfully.',
      action: 'Admin system is ready for production deployment.'
    });
  }
  
  return recommendations;
}

// Generate master test report
function generateMasterReport() {
  const totalDuration = Date.now() - new Date(masterResults.startTime).getTime();
  masterResults.endTime = new Date().toISOString();
  masterResults.totalDuration = totalDuration;
  
  // Calculate summary
  masterResults.summary.total = masterResults.suites.length;
  masterResults.summary.passed = masterResults.suites.filter(s => s.status === 'passed').length;
  masterResults.summary.failed = masterResults.suites.filter(s => s.status === 'failed').length;
  masterResults.summary.critical = masterResults.suites.filter(s => s.critical && s.status === 'failed').length;
  
  // Generate verdict
  if (masterResults.summary.critical > 0) {
    masterResults.verdict = 'CRITICAL_FAILURE';
  } else if (masterResults.summary.failed === 0) {
    masterResults.verdict = 'SUCCESS';
  } else if (masterResults.summary.failed <= 1) {
    masterResults.verdict = 'MOSTLY_SUCCESS';
  } else {
    masterResults.verdict = 'FAILURE';
  }
  
  // Generate recommendations
  masterResults.recommendations = generateRecommendations(masterResults);
  
  // Display report
  console.log('\n' + '='.repeat(80));
  console.log('🐭 TESTERAT - MASTER ADMIN SYSTEM TEST REPORT');
  console.log('='.repeat(80));
  console.log(`⏱️  Total Duration: ${formatDuration(totalDuration)}`);
  console.log(`📊 Test Suites: ${masterResults.summary.total}`);
  console.log(`✅ Passed: ${masterResults.summary.passed}`);
  console.log(`❌ Failed: ${masterResults.summary.failed}`);
  console.log(`🚨 Critical Failures: ${masterResults.summary.critical}`);
  console.log('='.repeat(80));
  
  // Suite details
  console.log('\n📋 SUITE RESULTS:');
  masterResults.suites.forEach(suite => {
    const status = suite.status === 'passed' ? '✅' : '❌';
    const critical = suite.critical ? '🚨' : '  ';
    console.log(`${status} ${critical} ${suite.name}: ${suite.status.toUpperCase()} (${formatDuration(suite.duration)})`);
    if (suite.error) {
      console.log(`    Error: ${suite.error}`);
    }
  });
  
  // Recommendations
  if (masterResults.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:');
    masterResults.recommendations.forEach(rec => {
      const priority = {
        'CRITICAL': '🚨',
        'HIGH': '⚠️',
        'MEDIUM': '📋',
        'INFO': 'ℹ️'
      }[rec.priority];
      
      console.log(`${priority} [${rec.priority}] ${rec.category}: ${rec.message}`);
      console.log(`    Action: ${rec.action}`);
    });
  }
  
  // Final verdict
  console.log('\n' + '='.repeat(80));
  switch (masterResults.verdict) {
    case 'SUCCESS':
      console.log('🎉 TESTERAT MASTER VERDICT: ADMIN SYSTEM FULLY VALIDATED!');
      console.log('✅ All tests passed - Ready for production deployment');
      break;
    case 'MOSTLY_SUCCESS':
      console.log('⚠️ TESTERAT MASTER VERDICT: ADMIN SYSTEM MOSTLY VALIDATED');
      console.log('📋 Minor issues detected - Review before deployment');
      break;
    case 'FAILURE':
      console.log('❌ TESTERAT MASTER VERDICT: ADMIN SYSTEM HAS ISSUES');
      console.log('🔧 Multiple failures detected - Fix issues before deployment');
      break;
    case 'CRITICAL_FAILURE':
      console.log('🚨 TESTERAT MASTER VERDICT: CRITICAL ADMIN SYSTEM FAILURES');
      console.log('⛔ DO NOT DEPLOY - Fix critical issues immediately');
      break;
  }
  console.log('='.repeat(80));
  
  // Save detailed report
  try {
    const reportDir = path.dirname(MASTER_CONFIG.reportPath);
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(MASTER_CONFIG.reportPath, JSON.stringify(masterResults, null, 2));
    console.log(`\n📄 Detailed report saved: ${MASTER_CONFIG.reportPath}`);
  } catch (error) {
    log(`Failed to save report: ${error.message}`, 'error');
  }
  
  return masterResults.verdict === 'SUCCESS' || masterResults.verdict === 'MOSTLY_SUCCESS';
}

// Main test orchestrator
async function runMasterTests() {
  console.log('🐭 TESTERAT MASTER - Starting Comprehensive Admin System Testing...\n');
  
  log('Initializing master test suite...', 'master');
  
  // Run each test suite
  for (const suite of MASTER_CONFIG.testSuites) {
    const result = await runTestSuite(suite);
    masterResults.suites.push(result);
    
    // Stop on critical failure if configured
    if (result.critical && result.status === 'failed') {
      log('Critical test suite failed - stopping execution', 'critical');
      break;
    }
    
    // Brief pause between suites
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Generate and display final report
  const success = generateMasterReport();
  
  // Exit with appropriate code
  process.exit(success ? 0 : 1);
}

// Handle process signals
process.on('SIGINT', () => {
  log('Test execution interrupted by user', 'warning');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('Test execution terminated', 'warning');
  process.exit(1);
});

// Run master tests if called directly
if (require.main === module) {
  runMasterTests().catch(error => {
    log(`Master test execution crashed: ${error.message}`, 'critical');
    console.error(error);
    process.exit(1);
  });
}

module.exports = { runMasterTests, masterResults };
