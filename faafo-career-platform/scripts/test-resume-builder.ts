import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testResumeBuilder() {
  console.log('🧪 Testing Resume Builder functionality...\n');

  try {
    // Test 1: Check if resume templates exist
    console.log('1️⃣ Testing resume templates...');
    const templates = await prisma.resumeTemplate.findMany({
      where: { isActive: true }
    });
    console.log(`   ✅ Found ${templates.length} active templates`);
    templates.forEach(template => {
      console.log(`   📄 ${template.name} (${template.category})`);
    });

    // Test 2: Check if we can create a test user (if not exists)
    console.log('\n2️⃣ Testing user creation...');
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          password: 'hashedpassword123' // In real app, this would be properly hashed
        }
      });
      console.log('   ✅ Created test user');
    } else {
      console.log('   ✅ Test user already exists');
    }

    // Test 3: Create a test resume
    console.log('\n3️⃣ Testing resume creation...');
    const testResume = await prisma.resume.create({
      data: {
        userId: testUser.id,
        title: 'Test Software Engineer Resume',
        templateId: templates[0]?.id,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+****************',
        location: 'San Francisco, CA',
        website: 'https://johndoe.dev',
        linkedin: 'https://linkedin.com/in/johndoe',
        summary: 'Experienced software engineer with 5+ years of experience in full-stack development.'
      }
    });
    console.log(`   ✅ Created test resume: ${testResume.title} (ID: ${testResume.id})`);

    // Test 4: Add experience to resume
    console.log('\n4️⃣ Testing resume experience...');
    const experience = await prisma.resumeExperience.create({
      data: {
        resumeId: testResume.id,
        company: 'Tech Corp',
        position: 'Senior Software Engineer',
        startDate: new Date('2020-01-01'),
        endDate: new Date('2023-12-31'),
        isCurrent: false,
        description: 'Led development of microservices architecture, improved system performance by 40%.',
        achievements: ['Improved system performance by 40%', 'Led team of 5 developers'],
        sortOrder: 1
      }
    });
    console.log(`   ✅ Added experience: ${experience.position} at ${experience.company}`);

    // Test 5: Add education to resume
    console.log('\n5️⃣ Testing resume education...');
    const education = await prisma.resumeEducation.create({
      data: {
        resumeId: testResume.id,
        institution: 'University of California',
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        startDate: new Date('2016-09-01'),
        endDate: new Date('2020-05-31'),
        gpa: '3.8',
        honors: ['Magna Cum Laude', 'Dean\'s List'],
        sortOrder: 1
      }
    });
    console.log(`   ✅ Added education: ${education.degree} in ${education.fieldOfStudy}`);

    // Test 6: Add skills to resume
    console.log('\n6️⃣ Testing resume skills...');
    const skills = [
      { category: 'Programming', skillName: 'JavaScript', proficiency: 5 },
      { category: 'Programming', skillName: 'TypeScript', proficiency: 4 },
      { category: 'Frameworks', skillName: 'React', proficiency: 5 },
      { category: 'Frameworks', skillName: 'Node.js', proficiency: 4 }
    ];

    for (const skill of skills) {
      await prisma.resumeSkill.create({
        data: {
          resumeId: testResume.id,
          ...skill,
          sortOrder: 1
        }
      });
    }
    console.log(`   ✅ Added ${skills.length} skills`);

    // Test 7: Add project to resume
    console.log('\n7️⃣ Testing resume projects...');
    const project = await prisma.resumeProject.create({
      data: {
        resumeId: testResume.id,
        name: 'E-commerce Platform',
        description: 'Built a full-stack e-commerce platform with React, Node.js, and PostgreSQL',
        technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS'],
        projectUrl: 'https://ecommerce-demo.com',
        repositoryUrl: 'https://github.com/johndoe/ecommerce',
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-06-30'),
        isHighlighted: true,
        sortOrder: 1
      }
    });
    console.log(`   ✅ Added project: ${project.name}`);

    // Test 8: Fetch complete resume with all relations
    console.log('\n8️⃣ Testing complete resume fetch...');
    const completeResume = await prisma.resume.findUnique({
      where: { id: testResume.id },
      include: {
        template: true,
        experiences: { orderBy: { sortOrder: 'asc' } },
        educations: { orderBy: { sortOrder: 'asc' } },
        skills: { orderBy: [{ category: 'asc' }, { sortOrder: 'asc' }] },
        projects: { orderBy: { sortOrder: 'asc' } }
      }
    });

    if (completeResume) {
      console.log(`   ✅ Fetched complete resume with:`);
      console.log(`      📄 Template: ${completeResume.template?.name}`);
      console.log(`      💼 Experiences: ${completeResume.experiences.length}`);
      console.log(`      🎓 Education: ${completeResume.educations.length}`);
      console.log(`      🛠️  Skills: ${completeResume.skills.length}`);
      console.log(`      🚀 Projects: ${completeResume.projects.length}`);
    }

    // Test 9: Test resume update
    console.log('\n9️⃣ Testing resume update...');
    const updatedResume = await prisma.resume.update({
      where: { id: testResume.id },
      data: {
        summary: 'Updated: Experienced software engineer with 5+ years of experience in full-stack development and team leadership.'
      }
    });
    console.log(`   ✅ Updated resume summary`);

    console.log('\n🎉 All Resume Builder tests passed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`   ✅ Templates: ${templates.length} available`);
    console.log(`   ✅ Resume created with ID: ${testResume.id}`);
    console.log(`   ✅ All CRUD operations working`);
    console.log(`   ✅ Database relationships functioning`);
    console.log(`   ✅ Resume Builder is FULLY FUNCTIONAL! 🚀`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

testResumeBuilder();
