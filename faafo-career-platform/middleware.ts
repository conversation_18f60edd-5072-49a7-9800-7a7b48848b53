import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { getRedisClient, isRedisAvailable, executeRedisCommand } from './src/lib/redis';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/assessment',
  '/forum',
  '/freedom-fund',
  '/progress',
  '/recommendations',
];

// Define API routes that require authentication
const protectedApiRoutes = [
  '/api/assessment',
  '/api/profile',
  '/api/freedom-fund',
  '/api/learning-progress',
  '/api/personalized-resources',
  '/api/progress-tracker',
  '/api/recommendations',
  '/api/resource-ratings',
];

// Define public API routes that don't require authentication
const publicApiRoutes = [
  '/api/auth',
  '/api/signup',
  '/api/career-paths',
  '/api/learning-resources',
  '/api/contact',
  '/api/csrf-token',
];

// Rate limiting store (fallback for when Redis is unavailable)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  return (request as any).ip || 'unknown';
}

/**
 * Redis-based rate limiting check
 */
async function redisRateLimitCheck(clientIP: string, windowMs: number, maxRequests: number): Promise<boolean> {
  const result = await executeRedisCommand(async (redis) => {
    const now = Date.now();
    const redisKey = `middleware_rate_limit:${clientIP}`;

    // Get current data
    const data = await redis.hmget(redisKey, 'count', 'resetTime');
    let count = parseInt(data[0] || '0');
    let resetTime = parseInt(data[1] || '0');

    // Reset if window expired
    if (now > resetTime) {
      count = 0;
      resetTime = now + windowMs;
    }

    // Increment count
    count++;

    // Update Redis with new count and set expiration
    await redis.hmset(redisKey, 'count', count.toString(), 'resetTime', resetTime.toString());
    await redis.pexpire(redisKey, windowMs);

    return count > maxRequests;
  });

  return result !== null ? result : false; // Default to false if Redis fails
}

/**
 * Memory-based rate limiting check (fallback)
 */
function memoryRateLimitCheck(clientIP: string, windowMs: number, maxRequests: number): boolean {
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean up old entries
  Array.from(rateLimitStore.entries()).forEach(([key, value]) => {
    if (value.resetTime < windowStart) {
      rateLimitStore.delete(key);
    }
  });

  // Get or create entry for this IP
  const entry = rateLimitStore.get(clientIP) || { count: 0, resetTime: now + windowMs };

  // Reset if window has expired
  if (entry.resetTime < now) {
    entry.count = 0;
    entry.resetTime = now + windowMs;
  }

  // Increment count
  entry.count++;
  rateLimitStore.set(clientIP, entry);

  return entry.count > maxRequests;
}

async function isRateLimited(request: NextRequest, windowMs: number = 15 * 60 * 1000, maxRequests: number = 100): Promise<boolean> {
  const clientIP = getClientIP(request);

  // Try Redis first if available
  if (isRedisAvailable()) {
    try {
      return await redisRateLimitCheck(clientIP, windowMs, maxRequests);
    } catch (error) {
      console.warn('Redis rate limiting failed in middleware, falling back to memory:', error);
    }
  }

  // Fallback to memory-based rate limiting
  return memoryRateLimitCheck(clientIP, windowMs, maxRequests);
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Add comprehensive security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Enhanced Content Security Policy
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live https://va.vercel-scripts.com https://browser.sentry-cdn.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: blob:",
    "font-src 'self' data: https://fonts.gstatic.com",
    "connect-src 'self' https://vercel.live https://vitals.vercel-insights.com https://*.sentry.io",
    "worker-src 'self' blob:",
    "child-src 'self' blob:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');

  response.headers.set('Content-Security-Policy', csp);

  // Add HSTS header for HTTPS (always set for security testing)
  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

  // Additional security headers
  response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
  response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
  response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');

  return response;
}

/**
 * Detect path traversal attempts
 */
function isPathTraversalAttempt(pathname: string): boolean {
  const pathTraversalPatterns = [
    /\.\.[\/\\]/,                    // Directory traversal
    /[\/\\]\.\.[\/\\]/,              // Absolute path traversal
    /\.\.[\/\\]\.\./,                // Multiple level traversal
    /\.\.\/|\.\.\\|\.\.$/,           // Various traversal patterns
    /[\/\\]etc[\/\\]passwd/i,        // Unix system files
    /[\/\\]windows[\/\\]system32/i,  // Windows system files
    /\.\..*[\/\\]/,                  // Any .. followed by path separator
    /%2e%2e[\/\\]/i,                 // URL encoded traversal
    /%2e%2e%2f/i,                    // URL encoded traversal
    /%2e%2e%5c/i,                    // URL encoded traversal
    /\x2e\x2e[\/\\]/,                // Hex encoded traversal
    /\.%2e[\/\\]/i,                  // Mixed encoding
    /%252e%252e/i,                   // Double URL encoding
  ];

  return pathTraversalPatterns.some(pattern => pattern.test(pathname));
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api/_next') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Path traversal protection
  if (isPathTraversalAttempt(pathname)) {
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    console.warn(`Path traversal attempt detected: ${pathname} from ${clientIP}`);
    return new NextResponse('Forbidden', { status: 403 });
  }

  // Force HTTPS in production
  if (process.env.NODE_ENV === 'production' && request.headers.get('x-forwarded-proto') !== 'https') {
    const httpsUrl = new URL(request.url);
    httpsUrl.protocol = 'https:';
    return NextResponse.redirect(httpsUrl);
  }

  // Apply rate limiting
  if (await isRateLimited(request)) {
    const response = new NextResponse('Too Many Requests', {
      status: 429,
      headers: {
        'X-RateLimit-Backend': isRedisAvailable() ? 'redis' : 'memory',
        'Retry-After': '900' // 15 minutes
      }
    });
    return addSecurityHeaders(response);
  }

  const response = NextResponse.next();
  return addSecurityHeaders(response);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
