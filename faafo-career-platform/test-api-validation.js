#!/usr/bin/env node

/**
 * Test API validation logic without requiring database
 */

const { z } = require('zod');

console.log('🧪 Testing API Validation Logic...\n');

// Recreate the validation schema from the API
const createResumeSchema = z.object({
  title: z.string()
    .min(1, 'Title is required')
    .max(100, 'Title must be less than 100 characters')
    .transform(val => val.trim()),
  templateId: z.string().uuid().optional(),
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name too long')
    .transform(val => val.trim()),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name too long')
    .transform(val => val.trim()),
  email: z.string()
    .email('Invalid email address')
    .max(255, 'Email too long'),
  phone: z.string()
    .max(20, 'Phone number too long')
    .optional()
    .transform(val => val?.trim() || null),
  location: z.string()
    .max(100, 'Location too long')
    .optional()
    .transform(val => val?.trim() || null),
  website: z.string()
    .url('Invalid website URL')
    .max(255, 'Website URL too long')
    .optional()
    .or(z.literal(''))
    .transform(val => val === '' ? null : val),
  linkedin: z.string()
    .url('Invalid LinkedIn URL')
    .max(255, 'LinkedIn URL too long')
    .optional()
    .or(z.literal(''))
    .transform(val => val === '' ? null : val),
  summary: z.string()
    .max(1000, 'Summary too long')
    .optional()
    .transform(val => val?.trim() || null),
});

// Test cases
const testCases = [
  {
    name: 'Valid Resume Data',
    data: {
      title: 'Software Engineer Resume',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      website: 'https://johndoe.com',
      linkedin: 'https://linkedin.com/in/johndoe',
      summary: 'Experienced software engineer with 5+ years of experience.',
    },
    shouldPass: true,
  },
  {
    name: 'Minimal Valid Data',
    data: {
      title: 'Resume',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
    },
    shouldPass: true,
  },
  {
    name: 'Empty Title',
    data: {
      title: '',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
    shouldPass: false,
    expectedError: 'Title is required',
  },
  {
    name: 'Invalid Email',
    data: {
      title: 'Resume',
      firstName: 'John',
      lastName: 'Doe',
      email: 'invalid-email',
    },
    shouldPass: false,
    expectedError: 'Invalid email address',
  },
  {
    name: 'Title Too Long',
    data: {
      title: 'A'.repeat(101),
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
    shouldPass: false,
    expectedError: 'Title must be less than 100 characters',
  },
  {
    name: 'Invalid Website URL',
    data: {
      title: 'Resume',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      website: 'not-a-url',
    },
    shouldPass: false,
    expectedError: 'Invalid website URL',
  },
  {
    name: 'Empty Website (Should Pass)',
    data: {
      title: 'Resume',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      website: '',
    },
    shouldPass: true,
  },
  {
    name: 'Summary Too Long',
    data: {
      title: 'Resume',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      summary: 'A'.repeat(1001),
    },
    shouldPass: false,
    expectedError: 'Summary too long',
  },
  {
    name: 'Whitespace Trimming',
    data: {
      title: '  Software Engineer Resume  ',
      firstName: '  John  ',
      lastName: '  Doe  ',
      email: '<EMAIL>',
      summary: '  Great developer  ',
    },
    shouldPass: true,
    expectedTransform: {
      title: 'Software Engineer Resume',
      firstName: 'John',
      lastName: 'Doe',
      summary: 'Great developer',
    },
  },
];

// Run tests
let passedTests = 0;
let failedTests = 0;

console.log('Running validation tests...\n');

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  
  try {
    const result = createResumeSchema.safeParse(testCase.data);
    
    if (testCase.shouldPass) {
      if (result.success) {
        console.log('✅ PASS - Validation succeeded as expected');
        
        // Check transformations if specified
        if (testCase.expectedTransform) {
          let transformCorrect = true;
          Object.keys(testCase.expectedTransform).forEach(key => {
            if (result.data[key] !== testCase.expectedTransform[key]) {
              console.log(`❌ Transform failed for ${key}: expected "${testCase.expectedTransform[key]}", got "${result.data[key]}"`);
              transformCorrect = false;
            }
          });
          if (transformCorrect) {
            console.log('✅ Transformations applied correctly');
          }
        }
        
        passedTests++;
      } else {
        console.log('❌ FAIL - Validation failed unexpectedly');
        console.log('Errors:', result.error.errors.map(e => e.message).join(', '));
        failedTests++;
      }
    } else {
      if (!result.success) {
        const errorMessages = result.error.errors.map(e => e.message);
        const hasExpectedError = testCase.expectedError ? 
          errorMessages.some(msg => msg.includes(testCase.expectedError)) : true;
        
        if (hasExpectedError) {
          console.log('✅ PASS - Validation failed as expected');
          console.log('Error:', errorMessages.join(', '));
          passedTests++;
        } else {
          console.log('❌ FAIL - Wrong error message');
          console.log('Expected:', testCase.expectedError);
          console.log('Got:', errorMessages.join(', '));
          failedTests++;
        }
      } else {
        console.log('❌ FAIL - Validation passed when it should have failed');
        failedTests++;
      }
    }
  } catch (error) {
    console.log('❌ FAIL - Unexpected error:', error.message);
    failedTests++;
  }
  
  console.log('');
});

// Summary
console.log('='.repeat(50));
console.log('VALIDATION TEST SUMMARY');
console.log('='.repeat(50));
console.log(`✅ Passed: ${passedTests}`);
console.log(`❌ Failed: ${failedTests}`);
console.log(`📊 Total: ${testCases.length}`);
console.log(`📈 Success Rate: ${((passedTests / testCases.length) * 100).toFixed(1)}%`);

if (failedTests === 0) {
  console.log('\n🎉 All validation tests passed! API validation is working correctly.');
} else {
  console.log('\n⚠️  Some validation tests failed. Review the implementation.');
}

console.log('\n' + '='.repeat(50));
