/**
 * Notifications API Endpoint
 * Following Ultimate Project Management Protocol - Self-healing implementation
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { notificationService } from '@/lib/notificationService';
import { createErrorResponse, createSuccessResponse } from '@/lib/api-response';
import { z } from 'zod';

// Input validation schemas
const markAsReadSchema = z.object({
  notificationId: z.string().min(1, 'Notification ID is required'),
});

const markAllAsReadSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
});

// GET /api/notifications - Get user notifications
export async function GET(request: NextRequest) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Self-healing: Get notifications with fallback
    const notifications = await notificationService.getNotifications(session.user.id);

    return createSuccessResponse(notifications);
  } catch (error) {
    console.error('Error fetching notifications:', error);

    // Exception handling: Check error type and respond appropriately
    if (error instanceof Error) {
      if (error.message.includes('Invalid userId')) {
        return createErrorResponse('Invalid user ID', 400);
      }
      if (error.message.includes('Database')) {
        return createErrorResponse('Service temporarily unavailable', 503);
      }
    }

    // Self-healing: Return empty array for unknown errors
    return createSuccessResponse([]);
  }
}

// POST /api/notifications - Create notification (admin only)
export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return createErrorResponse('Unauthorized', 401);
    }

    const body = await request.json();
    
    // Input validation
    const notificationData = z.object({
      type: z.enum(['achievement', 'reminder', 'progress', 'community']),
      message: z.string().min(1, 'Message is required'),
      userId: z.string().min(1, 'User ID is required'),
    }).parse(body);

    // Create notification
    const notification = await notificationService.createNotification({
      ...notificationData,
      read: false,
    });

    return createSuccessResponse(notification);
  } catch (error) {
    console.error('Error creating notification:', error);

    // Exception handling: Handle different error types
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid input data', 400, error.errors);
    }

    // Exception handling: Handle database errors
    if (error instanceof Error && error.message.includes('Database')) {
      return createErrorResponse('Database error', 503);
    }

    return createErrorResponse('Failed to create notification', 500);
  }
}

// PUT /api/notifications/mark-read - Mark notification as read
export async function PUT(request: NextRequest) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return createErrorResponse('Unauthorized', 401);
    }

    const body = await request.json();
    
    // Input validation
    const { notificationId } = markAsReadSchema.parse(body);

    // Mark as read with self-healing
    await notificationService.markAsRead(notificationId);

    return createSuccessResponse({ success: true });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    
    // Exception handling: Handle different error types
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid input data', 400, error.errors.map(e => e.message));
    }

    // Exception handling: Handle database errors
    if (error instanceof Error) {
      if (error.message.includes('Database')) {
        return createErrorResponse('Database error', 503);
      }
      if (error.message.includes('not found')) {
        return createErrorResponse('Notification not found', 404);
      }
    }

    // Self-healing: Don't fail the request, just log the error
    return createSuccessResponse({ success: false, error: 'Failed to mark as read' });
  }
}

// PUT /api/notifications/mark-all-read - Mark all notifications as read
export async function PATCH(request: NextRequest) {
  try {
    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return createErrorResponse('Unauthorized', 401);
    }

    // Mark all as read with self-healing
    await notificationService.markAllAsRead(session.user.id);

    return createSuccessResponse({ success: true });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    
    // Self-healing: Don't fail the request, just log the error
    return createSuccessResponse({ success: false, error: 'Failed to mark all as read' });
  }
}
