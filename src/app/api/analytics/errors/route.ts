import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

// Schema for client-side error reporting
const ClientErrorSchema = z.object({
  message: z.string(),
  stack: z.string().optional(),
  url: z.string(),
  metadata: z.record(z.any()).optional(),
  level: z.enum(['error', 'warning', 'info']).default('error'),
  source: z.string().default('client'),
  userAgent: z.string().optional(),
  environment: z.string().optional(),
});

// POST /api/analytics/errors - Log client-side errors
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = ClientErrorSchema.parse(body);

    // Get user ID from session if available
    let userId: string | undefined;
    try {
      // Try to get session, but don't fail if not available
      const { getServerSession } = await import('next-auth');
      const { authOptions } = await import('@/lib/auth');
      const session = await getServerSession(authOptions);
      userId = session?.user?.id || undefined;
    } catch (error) {
      // Session not available, continue without user ID
    }

    // Rate limiting: Don't log too many errors from the same source
    const recentErrorsCount = await prisma.errorLog.count({
      where: {
        message: validatedData.message,
        url: validatedData.url,
        timestamp: {
          gte: new Date(Date.now() - 5 * 60 * 1000), // Last 5 minutes
        },
      },
    });

    // Skip if too many similar errors recently
    if (recentErrorsCount >= 10) {
      return NextResponse.json({
        success: true,
        message: 'Error logged (rate limited)',
      });
    }

    // Map lowercase level to uppercase for Prisma enum
    const levelMap: Record<string, 'ERROR' | 'WARNING' | 'INFO'> = {
      'error': 'ERROR',
      'warning': 'WARNING',
      'info': 'INFO',
    };

    // Create error log entry
    const errorLog = await prisma.errorLog.create({
      data: {
        message: validatedData.message,
        stack: validatedData.stack,
        url: validatedData.url,
        metadata: validatedData.metadata || {},
        level: levelMap[validatedData.level] || 'ERROR',
        source: validatedData.source,
        userAgent: validatedData.userAgent,
        userId: userId,
        timestamp: new Date(),
        resolved: false,
      },
    });

    // For critical errors, also log to console
    if (validatedData.level === 'error') {
      console.error('Client error logged:', {
        id: errorLog.id,
        message: validatedData.message,
        url: validatedData.url,
        userId: userId,
        timestamp: new Date().toISOString(),
      });
    }

    return NextResponse.json({
      success: true,
      errorId: errorLog.id,
    });

  } catch (error) {
    console.error('Error logging client error:', error);
    
    // Still return success to avoid client-side error loops
    return NextResponse.json({
      success: true,
      message: 'Error logged with fallback',
    });
  }
}
