/**
 * Client-side Notification Service
 * API-based implementation for browser usage
 */

export interface Notification {
  id: string;
  type: 'achievement' | 'reminder' | 'progress' | 'community';
  message: string;
  read: boolean;
  createdAt?: Date;
  readAt?: Date;
  userId?: string;
}

class ClientNotificationService {
  private subscribers: Function[] = [];

  /**
   * Get notifications for a user via API
   */
  async getNotifications(userId: string): Promise<Notification[]> {
    if (!userId) {
      return [];
    }

    try {
      const response = await fetch('/api/notifications');
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }
      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as read via API
   */
  async markAsRead(notificationId: string): Promise<void> {
    if (!notificationId) return;

    try {
      await fetch('/api/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationId }),
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Mark all notifications as read via API
   */
  async markAllAsRead(userId: string): Promise<void> {
    if (!userId) return;

    try {
      await fetch('/api/notifications', {
        method: 'PATCH',
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  /**
   * Subscribe to real-time notifications
   */
  subscribe(callback: Function): void {
    this.subscribers.push(callback);
  }

  /**
   * Unsubscribe from real-time notifications
   */
  unsubscribe(callback: Function): void {
    this.subscribers = this.subscribers.filter(sub => sub !== callback);
  }

  /**
   * Check if there are active subscribers
   */
  hasSubscribers(): boolean {
    return this.subscribers.length > 0;
  }

  /**
   * Create a new notification via API
   */
  async createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Promise<Notification> {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notification),
      });

      if (!response.ok) {
        throw new Error('Failed to create notification');
      }

      const data = await response.json();
      const created = data.data;

      // Notify subscribers
      this.notifySubscribers(created);

      return created;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Notify all subscribers of new notification
   */
  private notifySubscribers(notification: Notification): void {
    this.subscribers.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Error notifying subscriber:', error);
      }
    });
  }
}

export const notificationService = new ClientNotificationService();
