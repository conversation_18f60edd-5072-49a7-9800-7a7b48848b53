/**
 * Notification Trigger System
 * Automatically creates notifications for user actions
 */

import { notificationService } from './notificationService';

export class NotificationTriggers {
  /**
   * Trigger notification when user completes an assessment
   */
  static async onAssessmentComplete(userId: string, assessmentType: string) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'achievement',
        message: `🎉 Congratulations! You've completed your ${assessmentType} assessment. Check out your personalized career recommendations!`,
        read: false,
        metadata: {
          action: 'assessment_complete',
          assessmentType,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create assessment completion notification:', error);
    }
  }

  /**
   * Trigger notification when user reaches a learning milestone
   */
  static async onLearningMilestone(userId: string, milestone: string, progress: number) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'progress',
        message: `🚀 Great progress! You've reached ${milestone} with ${progress}% completion. Keep up the excellent work!`,
        read: false,
        metadata: {
          action: 'learning_milestone',
          milestone,
          progress,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create learning milestone notification:', error);
    }
  }

  /**
   * Trigger notification when user completes a learning path
   */
  static async onLearningPathComplete(userId: string, pathTitle: string) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'achievement',
        message: `🏆 Amazing! You've completed the "${pathTitle}" learning path. You're one step closer to your career goals!`,
        read: false,
        metadata: {
          action: 'learning_path_complete',
          pathTitle,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create learning path completion notification:', error);
    }
  }

  /**
   * Trigger notification when user achieves a goal
   */
  static async onGoalAchievement(userId: string, goalTitle: string, goalType: string) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'achievement',
        message: `🎯 Goal achieved! You've successfully completed your ${goalType} goal: "${goalTitle}". Time to set your next challenge!`,
        read: false,
        metadata: {
          action: 'goal_achievement',
          goalTitle,
          goalType,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create goal achievement notification:', error);
    }
  }

  /**
   * Trigger notification for forum interactions
   */
  static async onForumReply(userId: string, postTitle: string, replierName: string) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'community',
        message: `💬 ${replierName} replied to your post "${postTitle}". Check out the discussion!`,
        read: false,
        metadata: {
          action: 'forum_reply',
          postTitle,
          replierName,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create forum reply notification:', error);
    }
  }

  /**
   * Trigger notification for skill level up
   */
  static async onSkillLevelUp(userId: string, skillName: string, newLevel: string) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'progress',
        message: `📈 Skill level up! Your ${skillName} skill has advanced to ${newLevel} level. Keep practicing!`,
        read: false,
        metadata: {
          action: 'skill_level_up',
          skillName,
          newLevel,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create skill level up notification:', error);
    }
  }

  /**
   * Trigger reminder notifications
   */
  static async createReminder(userId: string, reminderType: string, message: string, metadata?: any) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'reminder',
        message,
        read: false,
        metadata: {
          action: 'reminder',
          reminderType,
          ...metadata,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create reminder notification:', error);
    }
  }

  /**
   * Trigger weekly check-in reminder
   */
  static async weeklyCheckIn(userId: string) {
    await this.createReminder(
      userId,
      'weekly_checkin',
      '📅 Time for your weekly check-in! Review your progress and set goals for the upcoming week.',
      { frequency: 'weekly' }
    );
  }

  /**
   * Trigger learning streak reminder
   */
  static async learningStreakReminder(userId: string, streakDays: number) {
    await this.createReminder(
      userId,
      'learning_streak',
      `🔥 You're on a ${streakDays}-day learning streak! Don't break the chain - complete a learning activity today.`,
      { streakDays }
    );
  }

  /**
   * Trigger freedom fund milestone
   */
  static async onFreedomFundMilestone(userId: string, percentage: number, targetAmount: number) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'progress',
        message: `💰 Freedom Fund milestone! You've saved ${percentage}% of your target ($${targetAmount.toLocaleString()}). Financial freedom is within reach!`,
        read: false,
        metadata: {
          action: 'freedom_fund_milestone',
          percentage,
          targetAmount,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create freedom fund milestone notification:', error);
    }
  }

  /**
   * Trigger system announcements
   */
  static async systemAnnouncement(userId: string, title: string, message: string) {
    try {
      await notificationService.createNotification({
        userId,
        type: 'community',
        message: `📢 ${title}: ${message}`,
        read: false,
        metadata: {
          action: 'system_announcement',
          title,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to create system announcement notification:', error);
    }
  }

  /**
   * Bulk create welcome notifications for new users
   */
  static async createWelcomeNotifications(userId: string) {
    const welcomeNotifications = [
      {
        type: 'community' as const,
        message: '🎉 Welcome to FAAFO Career Platform! Start by taking your career assessment to get personalized recommendations.',
        metadata: { action: 'welcome', step: 1 }
      },
      {
        type: 'reminder' as const,
        message: '📚 Explore our learning resources and find courses that match your career goals.',
        metadata: { action: 'welcome', step: 2 }
      },
      {
        type: 'community' as const,
        message: '💬 Join our community forum to connect with like-minded professionals and get support.',
        metadata: { action: 'welcome', step: 3 }
      }
    ];

    for (const notification of welcomeNotifications) {
      try {
        await notificationService.createNotification({
          userId,
          ...notification,
          read: false
        });
      } catch (error) {
        console.error('Failed to create welcome notification:', error);
      }
    }
  }
}

export default NotificationTriggers;
