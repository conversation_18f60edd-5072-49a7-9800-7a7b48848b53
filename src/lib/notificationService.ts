/**
 * Notification Service
 * Minimal implementation to pass TDD tests
 */

// Only import prisma on server side
let prisma: any;
if (typeof window === 'undefined') {
  prisma = require('./prisma').default;
}

export interface Notification {
  id: string;
  type: 'achievement' | 'reminder' | 'progress' | 'community';
  message: string;
  read: boolean;
  createdAt?: Date;
  readAt?: Date;
  userId?: string;
}

class NotificationService {
  private subscribers: Function[] = [];
  private retryAttempts = 3;
  private retryDelay = 1000; // 1 second

  /**
   * Get notifications for a user with retry mechanism
   */
  async getNotifications(userId: string): Promise<Notification[]> {
    // Input validation
    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid userId');
    }

    // Server-side only
    if (typeof window !== 'undefined') {
      return [];
    }

    return this.withRetry(async () => {
      const notifications = await prisma.notification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      });

      return notifications as Notification[];
    }, 'fetching notifications');
  }

  /**
   * Retry mechanism for database operations
   */
  private async withRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    fallbackValue?: T
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        console.error(`Error ${operationName} (attempt ${attempt}/${this.retryAttempts}):`, error);

        if (attempt < this.retryAttempts) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
        }
      }
    }

    // All retries failed
    console.error(`All retry attempts failed for ${operationName}:`, lastError);

    // Return fallback value if provided, otherwise return safe default
    if (fallbackValue !== undefined) {
      return fallbackValue;
    }

    // For array operations, return empty array as safe fallback
    if (operationName.includes('notifications')) {
      return [] as unknown as T;
    }

    // For other operations, re-throw the error
    throw lastError;
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    if (!notificationId) {
      throw new Error('Invalid notificationId');
    }

    // Server-side only
    if (typeof window !== 'undefined') {
      return;
    }

    try {
      await prisma.notification.update({
        where: { id: notificationId },
        data: {
          read: true,
          readAt: new Date()
        },
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Self-healing: don't throw, just log
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<void> {
    if (!userId) {
      throw new Error('Invalid userId');
    }

    // Server-side only
    if (typeof window !== 'undefined') {
      return;
    }

    try {
      await prisma.notification.updateMany({
        where: { userId, read: false },
        data: {
          read: true,
          readAt: new Date()
        },
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      // Self-healing: don't throw, just log
    }
  }

  /**
   * Subscribe to real-time notifications
   */
  subscribe(callback: Function): void {
    this.subscribers.push(callback);
  }

  /**
   * Unsubscribe from real-time notifications
   */
  unsubscribe(callback: Function): void {
    this.subscribers = this.subscribers.filter(sub => sub !== callback);
  }

  /**
   * Check if there are active subscribers
   */
  hasSubscribers(): boolean {
    return this.subscribers.length > 0;
  }

  /**
   * Create a new notification
   */
  async createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Promise<Notification> {
    // Server-side only
    if (typeof window !== 'undefined') {
      return {
        id: 'client-side',
        createdAt: new Date(),
        ...notification
      } as Notification;
    }

    try {
      const created = await prisma.notification.create({
        data: {
          ...notification,
          createdAt: new Date(),
        },
      });

      // Notify subscribers
      this.notifySubscribers(created as Notification);

      return created as Notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Notify all subscribers of new notification
   */
  private notifySubscribers(notification: Notification): void {
    this.subscribers.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Error notifying subscriber:', error);
      }
    });
  }
}

export const notificationService = new NotificationService();
