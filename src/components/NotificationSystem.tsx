/**
 * Notification System Component
 * Minimal implementation to pass TDD tests
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Bell } from 'lucide-react';
import { notificationService, Notification } from '@/lib/notificationService.client';

interface NotificationSystemProps {
  userId?: string;
}

export const NotificationSystem: React.FC<NotificationSystemProps> = ({ userId = 'test-user' }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  // Self-healing error recovery
  const handleError = useCallback((error: Error) => {
    console.error('NotificationSystem error:', error);
    setError(error.message);
    
    // Auto-recovery attempt
    setTimeout(() => {
      setError(null);
      loadNotifications();
    }, 5000);
  }, []);

  // Load notifications with retry logic
  const loadNotifications = useCallback(async () => {
    if (!userId) return;
    
    setIsLoading(true);
    try {
      const data = await notificationService.getNotifications(userId);
      setNotifications(data);
      setError(null);
    } catch (err) {
      handleError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [userId, handleError]);

  // Real-time subscription
  useEffect(() => {
    const handleNewNotification = (notification: Notification) => {
      setNotifications(prev => [notification, ...prev]);
    };

    notificationService.subscribe(handleNewNotification);
    loadNotifications();

    return () => {
      notificationService.unsubscribe(handleNewNotification);
    };
  }, [loadNotifications]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
    } catch (err) {
      handleError(err as Error);
    }
  }, [handleError]);

  // Mark all as read
  const markAllAsRead = useCallback(async () => {
    try {
      await notificationService.markAllAsRead(userId);
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    } catch (err) {
      handleError(err as Error);
    }
  }, [userId, handleError]);

  const unreadCount = notifications.filter(n => !n.read).length;

  if (error) {
    return (
      <div role="alert" className="error-boundary">
        <button 
          aria-label="notifications"
          onClick={() => setError(null)}
          className="relative p-2 text-gray-600 hover:text-gray-900"
        >
          <Bell size={20} />
        </button>
      </div>
    );
  }

  return (
    <div className="relative">
      <button
        aria-label="notifications"
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900"
      >
        <Bell size={20} />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 className="font-semibold">Notifications</h3>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Mark all read
              </button>
            )}
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">Loading...</div>
            ) : notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">No notifications</div>
            ) : (
              notifications.map(notification => (
                <div
                  key={notification.id}
                  className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
                    !notification.read ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => !notification.read && markAsRead(notification.id)}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <p className="text-sm text-gray-900">{notification.message}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {notification.type}
                      </p>
                    </div>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};
