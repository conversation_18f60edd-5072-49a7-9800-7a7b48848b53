# FAAFO Career Platform Documentation

Welcome to the FAAFO Career Platform documentation - an AI-powered platform helping individuals transition from traditional 9-to-5 jobs to fulfilling, flexible career paths.

## 🚀 Quick Start

### **New to the Platform?**
1. **[Project Overview](atoms/concepts/project-overview.md)** - Understand the mission and goals
2. **[Development Workflow](workflows/development.md)** - Set up your development environment
3. **[Testing Workflow](workflows/testing.md)** - Run comprehensive tests
4. **[Deployment Workflow](workflows/deployment.md)** - Deploy to production

### **Looking for Specific Procedures?**
- **Environment Setup**: [atoms/setup/environment.md](atoms/setup/environment.md)
- **Database Configuration**: [atoms/setup/database.md](atoms/setup/database.md)
- **Database Optimization**: [atoms/procedures/database-optimization.md](atoms/procedures/database-optimization.md)
- **Testing Commands**: [atoms/commands/testing.md](atoms/commands/testing.md)
- **Security Testing**: [atoms/procedures/security-testing.md](atoms/procedures/security-testing.md)
- **API Testing**: [atoms/procedures/api-testing.md](atoms/procedures/api-testing.md)

## 📁 Documentation Architecture

This documentation follows an **atomic design pattern** for maximum reusability and maintainability:

### **🔬 Atomic Content** (`atoms/`)
Small, focused procedures that can be reused across workflows:
- **`setup/`** - Environment, database, and tool installation procedures
- **`commands/`** - CLI commands and scripts
- **`concepts/`** - Core project concepts and principles
- **`procedures/`** - Step-by-step operational procedures

### **🔄 Workflows** (`workflows/`)
Complete, orchestrated processes that combine atomic procedures:
- **`development.md`** - Complete development setup and daily procedures
- **`testing.md`** - Comprehensive testing procedures
- **`deployment.md`** - Production deployment procedures

### **📚 Reference** (`reference/`)
- **`relationships.md`** - Auto-generated documentation relationships

### **📦 Archives** (`archives/`)
- **`implementation-history/`** - Project implementation history
- **`legacy-documentation/`** - Archived legacy documentation

## 🎯 Platform Technology Stack

- **Frontend**: Next.js 15 with React
- **Backend**: Next.js API routes  
- **Database**: Vercel Postgres (Neon)
- **AI Integration**: Google Gemini API
- **Authentication**: NextAuth.js
- **Deployment**: Vercel
- **Testing**: Jest, Cypress, Testerat

## 🔍 Navigation Guide

### **For Developers**
1. Start with [Development Workflow](workflows/development.md)
2. Follow [Testing Workflow](workflows/testing.md) for quality assurance
3. Use [Deployment Workflow](workflows/deployment.md) for production releases

### **For Operations**
1. Review [Database Backup Procedures](atoms/procedures/database-backup.md)
2. Follow [Deployment Workflow](workflows/deployment.md)
3. Set up monitoring using operational procedures

### **For Quality Assurance**
1. Use [Testing Workflow](workflows/testing.md) for comprehensive testing
2. Follow [Security Testing Procedures](atoms/procedures/security-testing.md)
3. Validate APIs using [API Testing Procedures](atoms/procedures/api-testing.md)

## 📖 Legacy Documentation

Legacy documentation has been archived to `archives/legacy-*` directories. The current atomic structure provides:
- ✅ **Single Source of Truth** - Each procedure exists exactly once
- ✅ **Maximum Reusability** - Atomic procedures used across workflows
- ✅ **Easy Maintenance** - Small, focused files
- ✅ **AI-Optimized** - Predictable structure with rich metadata

## 🤝 Contributing

When working with documentation:
1. **Edit atomic procedures** in `atoms/` for reusable content
2. **Update workflows** in `workflows/` for complete processes
3. **Run validation**: `python scripts/validate-metadata.py`
4. **Test builds**: `python scripts/build-composed-docs.py`

## 📞 Support

- **Development Issues**: See [Development Workflow](workflows/development.md)
- **Testing Problems**: See [Testing Workflow](workflows/testing.md)
- **Deployment Issues**: See [Deployment Workflow](workflows/deployment.md)
- **Legacy Content**: Check `archives/legacy-documentation/`

---

**Documentation System**: Atomic Design Pattern  
**Last Updated**: June 15, 2025  
**Status**: Production Ready
