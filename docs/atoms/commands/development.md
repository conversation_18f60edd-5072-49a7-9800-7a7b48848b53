---
category: commands
complexity: beginner
dependencies:
- environment-setup
doc_type: atomic-procedure
estimated_time: 5 minutes
last_validated: '2025-06-15'
owner: '@backend-team'
tags:
- development
- npm
- commands
- scripts
title: Development Commands
used_in:
- development.md
---

# Development Commands

## Package Management

### Install Dependencies
```bash
# Install all dependencies
npm install

# Install development dependencies only
npm install --save-dev

# Install specific package
npm install package-name

# Install global package
npm install -g package-name
```

### Update Dependencies
```bash
# Update all packages
npm update

# Check for outdated packages
npm outdated

# Audit for vulnerabilities
npm audit
npm audit fix
```

## Development Server

### Start Development Server
```bash
# Start with hot reload
npm run dev

# Start on specific port
PORT=3001 npm run dev

# Start with debug mode
DEBUG=* npm run dev
```

### Build Commands
```bash
# Build for production
npm run build

# Build and start production server
npm run build && npm start

# Analyze bundle size
npm run analyze
```

## Database Commands

### Prisma Operations
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate dev

# Reset database (development)
npx prisma migrate reset

# View database in browser
npx prisma studio
```

### Database Utilities
```bash
# Apply database indexes
npx tsx scripts/apply-database-indexes.ts

# Test database connection
npx tsx scripts/test-database-connection.ts
```

## Code Quality

### Linting and Formatting
```bash
# Run ESLint
npm run lint

# Fix ESLint issues
npm run lint:fix

# Format code with Prettier
npm run format

# Type checking
npm run type-check
```

### Pre-commit Checks
```bash
# Run all quality checks
npm run pre-commit

# Individual checks
npm run lint
npm run type-check
npm run test
```

## Environment Management

### Environment Variables
```bash
# Copy example environment
cp .env.example .env

# Validate environment
npm run env:check

# Load environment for scripts
npx dotenv-cli -- command
```

### Configuration
```bash
# Validate configuration
npm run config:validate

# Generate configuration docs
npm run config:docs
```

## Debugging

### Debug Mode
```bash
# Start with Node.js debugger
npm run dev:debug

# Debug specific script
node --inspect scripts/script-name.js

# Debug with VS Code
# Use "Debug: Start Debugging" in VS Code
```

### Logging
```bash
# Enable debug logging
DEBUG=app:* npm run dev

# View application logs
npm run logs

# Clear logs
npm run logs:clear
```

## Performance

### Performance Testing
```bash
# Run performance benchmarks
npm run perf

# Analyze bundle
npm run analyze

# Memory usage analysis
npm run memory-test
```

### Optimization
```bash
# Optimize images
npm run optimize:images

# Optimize dependencies
npm run optimize:deps

# Clean build artifacts
npm run clean
```

## Utilities

### Code Generation
```bash
# Generate component
npm run generate:component ComponentName

# Generate API route
npm run generate:api route-name

# Generate database migration
npx prisma migrate dev --name migration-name
```

### Documentation
```bash
# Generate API docs
npm run docs:api

# Generate component docs
npm run docs:components

# Serve documentation
npm run docs:serve
```

## Troubleshooting Commands

### Common Issues
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Next.js cache
rm -rf .next
npm run dev

# Reset database
npx prisma migrate reset
```

### Health Checks
```bash
# Check system health
npm run health

# Verify all services
npm run verify

# Run diagnostics
npm run diagnostics
```
