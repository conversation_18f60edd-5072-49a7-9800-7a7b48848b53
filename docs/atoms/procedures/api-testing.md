---
category: procedures
complexity: intermediate
dependencies:
- development-server
- authentication
doc_type: atomic-procedure
estimated_time: 15 minutes
last_validated: '2025-06-15'
owner: '@qa-team'
tags:
- api
- testing
- curl
- endpoints
title: API Testing Procedures
used_in:
- development.md
---

# API Testing Procedures

## Prerequisites
- Development server running (`npm run dev`)
- Valid authentication session
- API endpoints accessible

## Authentication Setup

### Get Session Token
```bash
# Login and extract session token
curl -X POST "http://localhost:3000/api/auth/signin" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' \
  -c cookies.txt

# Extract session token from cookies
SESSION_TOKEN=$(grep "next-auth.session-token" cookies.txt | cut -f7)
```

### Admin Authentication
```bash
# Use admin email from environment
ADMIN_EMAIL="<EMAIL>"

# Admin endpoints require admin session
curl -X POST "http://localhost:3000/api/auth/signin" \
  -H "Content-Type: application/json" \
  -d '{"email": "'$ADMIN_EMAIL'", "password": "admin_password"}' \
  -c admin_cookies.txt
```

## AI Endpoints Testing

### Resume Analysis
```bash
curl -X POST "http://localhost:3000/api/ai/resume-analysis" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$SESSION_TOKEN" \
  -d '{
    "resumeText": "John Doe\nSoftware Engineer\n5 years experience in React and Node.js..."
  }'
```

### Career Recommendations
```bash
curl -X POST "http://localhost:3000/api/ai/career-recommendations" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$SESSION_TOKEN" \
  -d '{
    "currentSkills": ["JavaScript", "React", "Node.js"],
    "preferences": {
      "workEnvironment": "remote",
      "careerStage": "mid"
    }
  }'
```

### Skills Gap Analysis
```bash
curl -X POST "http://localhost:3000/api/ai/skills-analysis" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$SESSION_TOKEN" \
  -d '{
    "currentSkills": ["JavaScript", "React"],
    "targetCareerPath": "Senior Full Stack Developer",
    "experienceLevel": "mid"
  }'
```

## Learning Management Testing

### Create Learning Path
```bash
curl -X POST "http://localhost:3000/api/learning-paths" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$ADMIN_SESSION_TOKEN" \
  -d '{
    "title": "React Fundamentals",
    "description": "Learn the basics of React development",
    "difficulty": "BEGINNER",
    "estimatedHours": 20,
    "category": "WEB_DEVELOPMENT"
  }'
```

### Enroll in Learning Path
```bash
# Replace {path-id} with actual path ID
curl -X POST "http://localhost:3000/api/learning-paths/{path-id}/enroll" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$SESSION_TOKEN"
```

### Update Progress
```bash
# Replace {path-id} and {step-id} with actual IDs
curl -X PUT "http://localhost:3000/api/learning-paths/{path-id}/steps/{step-id}/progress" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$SESSION_TOKEN" \
  -d '{
    "status": "COMPLETED",
    "timeSpent": 45
  }'
```

## Admin Endpoints Testing

### Database Statistics
```bash
curl "http://localhost:3000/api/admin/database?action=stats" \
  -H "Cookie: next-auth.session-token=$ADMIN_SESSION_TOKEN"
```

### Apply Database Optimizations
```bash
curl -X POST "http://localhost:3000/api/admin/database" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$ADMIN_SESSION_TOKEN" \
  -d '{"action": "optimize"}'
```

### Apply Performance Indexes
```bash
curl -X POST "http://localhost:3000/api/admin/database" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=$ADMIN_SESSION_TOKEN" \
  -d '{"action": "apply_indexes"}'
```

## Health Check Endpoints

### AI Service Health
```bash
curl "http://localhost:3000/api/ai/health"

# Detailed health check
curl "http://localhost:3000/api/ai/health?detailed=true"
```

### Database Health
```bash
curl "http://localhost:3000/api/health/database" \
  -H "Cookie: next-auth.session-token=$SESSION_TOKEN"
```

### System Health
```bash
curl "http://localhost:3000/api/health"
```

## Response Validation

### Expected Response Formats

#### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

#### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

### Status Code Validation
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Automated Testing Scripts

### Test All Endpoints
```bash
# Run comprehensive API test suite
npx tsx scripts/test-api-endpoints.ts

# Test specific endpoint category
npx tsx scripts/test-api-endpoints.ts --category=ai

# Test with specific user
npx tsx scripts/test-api-endpoints.ts --user=admin
```

### Performance Testing
```bash
# Load test API endpoints
npx tsx scripts/load-test-api.ts

# Stress test specific endpoint
npx tsx scripts/stress-test.ts --endpoint=/api/ai/resume-analysis
```

## Troubleshooting

### Common Issues

#### Authentication Errors
- Verify session token is valid
- Check cookie expiration
- Ensure user has required permissions

#### API Errors
- Check request format and headers
- Verify endpoint URL is correct
- Review server logs for details

#### Performance Issues
- Monitor response times
- Check database connection
- Verify cache service status

### Debug Commands
```bash
# Check API documentation
curl "http://localhost:3000/api/docs"

# View OpenAPI specification
curl "http://localhost:3000/api/docs?format=yaml"

# Test endpoint with verbose output
curl -v "http://localhost:3000/api/health"
```
