---
category: setup
complexity: beginner
dependencies: []
doc_type: atomic-procedure
estimated_time: 10 minutes
last_validated: '2025-06-15'
owner: '@backend-team'
tags:
- environment
- nodejs
- npm
- setup
title: Environment Setup
used_in:
- STYLE_GUIDE.md
- development.md
---

# Environment Setup

## Prerequisites
- Node.js 18+ installed
- Git installed and configured
- Access to the repository

## Environment Variables

Create a `.env` file in the project root:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/faafo_dev"

# Authentication
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# Email (Resend)
RESEND_API_KEY="re_your_api_key_here"

# AI Services
GEMINI_API_KEY="your_gemini_api_key_here"

# Redis (optional for development)
REDIS_URL="redis://localhost:6379"
```

## Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install development dependencies
npm install --save-dev
```

## Verify Setup

```bash
# Check Node.js version
node --version  # Should be 18+

# Check npm version
npm --version

# Verify environment variables
npm run env:check
```

## Common Issues

### Node.js Version Mismatch
```bash
# Use nvm to switch Node.js versions
nvm use 18
nvm alias default 18
```

### Missing Environment Variables
```bash
# Copy example environment file
cp .env.example .env
# Edit .env with your values
```

### Permission Issues
```bash
# Fix npm permissions (macOS/Linux)
sudo chown -R $(whoami) ~/.npm
```

## Next Steps

After environment setup:
1. [Database Setup](database.md)
2. [Development Commands](../commands/development.md)
3. [Running Tests](../commands/testing.md)
