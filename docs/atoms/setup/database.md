---
category: setup
complexity: intermediate
dependencies:
- environment-setup
doc_type: atomic-procedure
estimated_time: 10 minutes
last_validated: '2025-06-15'
owner: '@backend-team'
tags:
- database
- postgresql
- neon
- prisma
- setup
title: Database Setup
used_in:
- development.md
---

# Database Setup

## Prerequisites
- Node.js 18+ installed
- Environment variables configured
- Vercel Postgres (Neon) database created

## Database Configuration

The FAAFO platform uses Vercel Postgres (Neon) as the primary database.

### Connection String
```bash
# Add to .env file
DATABASE_URL="****************************************/neondb"

# Database optimization settings
DATABASE_CONNECTION_POOL_SIZE=20
DATABASE_QUERY_TIMEOUT=5000
```

## Setup Commands

### 1. Generate Prisma Client
```bash
npx prisma generate
```

### 2. Run Database Migrations
```bash
# Apply all pending migrations
npx prisma migrate dev

# For production deployment
npx prisma migrate deploy
```

### 3. Seed Database (Optional)
```bash
npx prisma db seed
```

## Verification

### Test Database Connection
```bash
# Pull current schema to verify connection
npx prisma db pull

# Validate schema
npx prisma validate
```

### Check Migration Status
```bash
# View migration history
npx prisma migrate status

# View current schema
npx prisma db pull
```

## Database Operations

### Reset Database (Development Only)
```bash
# WARNING: This will delete all data
npx prisma migrate reset
```

### Apply Performance Indexes
```bash
# Run optimization script
npx tsx scripts/apply-database-indexes.ts
```

## Troubleshooting

### Connection Issues
```bash
# Test connection
npx prisma db pull

# Check environment variables
echo $DATABASE_URL
```

### Migration Errors
```bash
# Reset and reapply migrations
npx prisma migrate reset
npx prisma migrate dev
```

### Schema Sync Issues
```bash
# Force schema sync
npx prisma db push

# Generate client after schema changes
npx prisma generate
```

## Performance Monitoring

### Database Stats Endpoint
```bash
curl "http://localhost:3000/api/admin/database?action=stats" \
  -H "Cookie: next-auth.session-token=your-admin-session-token"
```

### Apply Optimizations
```bash
curl -X POST "http://localhost:3000/api/admin/database" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-admin-session-token" \
  -d '{"action": "optimize"}'
```

## Success Criteria

✅ Database connection established  
✅ All migrations applied successfully  
✅ Prisma client generated  
✅ Schema validation passes  
✅ Performance indexes applied  

## Next Steps

After database setup:
1. [Development Commands](../commands/development.md)
2. [Testing Setup](../commands/testing.md)
3. [API Testing](../procedures/api-testing.md)
