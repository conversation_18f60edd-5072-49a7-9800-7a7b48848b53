---
category: setup
complexity: beginner
dependencies:
- python-environment
doc_type: atomic-procedure
estimated_time: 10 minutes
last_validated: '2025-06-15'
owner: '@qa-team'
tags:
- testerat
- testing
- installation
- playwright
title: Testerat Installation
used_in:
- testing.md
---

# Testerat Installation

## Prerequisites
- Python 3.8+ installed
- pip package manager
- Internet connection for downloads

## Core Installation

### 1. Install Python Dependencies
```bash
# Install required packages
pip install playwright requests

# Install Playwright browser
playwright install chromium
```

### 2. Verify Installation
```bash
# Test Playwright installation
python -c "from playwright.sync_api import sync_playwright; print('Playwright installed successfully')"

# Test requests library
python -c "import requests; print('Requests installed successfully')"
```

## Optional: AI Enhancement

### Install Ollama (for enhanced AI analysis)
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull AI model
ollama pull llama2

# Verify Ollama installation
ollama list
```

## Testerat Setup

### Download Testerat
```bash
# Clone or download testerat script
# (Assuming testerat is in the project scripts directory)
chmod +x testerat
```

### Test Installation
```bash
# Run demo mode to verify everything works
python3 testerat

# Test with a simple URL
python3 testerat https://example.com
```

## Troubleshooting

### Common Installation Issues

#### Playwright Browser Installation Failed
```bash
# Reinstall Playwright browsers
playwright install --force chromium

# Check system dependencies (Linux)
sudo apt-get update
sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

#### Permission Errors
```bash
# Fix permissions (macOS/Linux)
sudo chown -R $(whoami) ~/.cache/ms-playwright

# Alternative: Use user installation
pip install --user playwright requests
```

#### Ollama Installation Issues
```bash
# Check Ollama service status
ollama serve

# Restart Ollama service
sudo systemctl restart ollama

# Check available models
ollama list
```

### Python Environment Issues
```bash
# Check Python version
python3 --version  # Should be 3.8+

# Create virtual environment (recommended)
python3 -m venv testerat-env
source testerat-env/bin/activate  # Linux/macOS
# testerat-env\Scripts\activate  # Windows

# Install in virtual environment
pip install playwright requests
```

## Verification Commands

### Test Core Functionality
```bash
# Basic functionality test
python3 testerat --help

# Demo mode (no network required)
python3 testerat

# Simple website test
python3 testerat https://httpbin.org/html
```

### Test AI Features (if Ollama installed)
```bash
# Check Ollama connectivity
curl http://localhost:11434/api/tags

# Test AI analysis
python3 testerat https://example.com "AI analysis test"
```

## Performance Optimization

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: 2+ cores recommended
- **Disk**: 2GB free space for browsers and cache
- **Network**: Stable internet connection

### Configuration Tuning
```bash
# Set environment variables for better performance
export PLAYWRIGHT_BROWSERS_PATH=/opt/playwright
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1  # If browsers already installed
```

## Success Criteria

✅ Python dependencies installed  
✅ Playwright browser downloaded  
✅ Testerat script executable  
✅ Demo mode runs successfully  
✅ Basic website testing works  
✅ (Optional) Ollama AI enhancement working  

## Next Steps

After installation:
1. [Basic Testing Commands](../commands/testerat-usage.md)
2. [Security Testing Procedures](../procedures/security-testing.md)
3. [Testing Workflow](../../workflows/testing.md)
