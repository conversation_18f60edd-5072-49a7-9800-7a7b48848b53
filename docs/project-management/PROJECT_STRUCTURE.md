# Project Structure Guidelines

## 🎯 Purpose
This document defines the canonical file structure for the FAAFO Career Platform to prevent duplicate files and maintain consistency.

## 📁 Directory Structure

### **Root Level**
```
faafo-career-platform/
├── src/                    # Main source code (CANONICAL)
├── docs/                   # All documentation
├── __tests__/              # Test files
├── public/                 # Static assets
├── node_modules/           # Dependencies
└── [config files]          # Package.json, etc.
```

### **Source Code Structure (src/)**
```
src/
├── app/                    # Next.js App Router pages
├── components/             # React components (CANONICAL LOCATION)
│   ├── ui/                 # Reusable UI components
│   ├── dashboard/          # Dashboard-specific components
│   ├── auth/               # Authentication components
│   └── forms/              # Form components
├── lib/                    # Utility functions and configurations
├── types/                  # TypeScript type definitions
├── emails/                 # Email templates
└── styles/                 # Global styles
```

### **Documentation Structure (docs/)**
```
docs/
├── api/                    # API documentation
├── components/             # Component documentation
├── deployment/             # Deployment guides
├── development/            # Development setup
└── architecture/           # System architecture
```

## 🚫 **DEPRECATED/DUPLICATE LOCATIONS**

### **DO NOT USE:**
- `/components/` (root level) - Use `/src/components/` instead
- Scattered `.md` files in root - Use `/docs/` instead
- Multiple config directories - Keep configs in root

## ✅ **Before Creating Any File:**

1. **Check existing structure**: `view src/` and `view docs/`
2. **Search for similar files**: Use codebase search
3. **Follow established patterns**: Match existing naming conventions
4. **Verify location**: Ensure it's in the canonical directory

## 🔧 **File Naming Conventions**

### **Components**
- PascalCase: `UserProfile.tsx`
- Test files: `UserProfile.test.tsx`
- Stories: `UserProfile.stories.tsx`

### **Documentation**
- UPPERCASE: `README.md`, `API.md`
- Descriptive: `deployment-guide.md`

### **Utilities**
- camelCase: `formatDate.ts`
- kebab-case for configs: `jest.config.js`

## 🎯 **Validation Checklist**

Before creating any file, ask:
- [ ] Does this file already exist elsewhere?
- [ ] Am I following the established directory structure?
- [ ] Is this the canonical location for this type of file?
- [ ] Does this match existing naming conventions?
- [ ] Will this create confusion or duplication?

## 🔄 **Cleanup Process**

When duplicates are found:
1. Identify the canonical location
2. Compare file contents
3. Merge if necessary
4. Remove duplicates
5. Update imports/references
6. Update this documentation
