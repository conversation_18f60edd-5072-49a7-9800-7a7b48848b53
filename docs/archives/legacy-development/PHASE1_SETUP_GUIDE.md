# Phase 1 Setup Guide 🚀

This guide will help you set up and test the new Phase 1 features of the FAAFO Career Platform.

## 📋 Prerequisites

- Node.js 18+ installed
- ✅ **Database**: Vercel Postgres (Neon) - **CONFIGURED AND ACTIVE**
- Google Gemini API key (for AI features)
- Redis (optional, for production caching)

## ✅ Database Migration Status

**Database successfully migrated to Vercel Postgres!**
- **Provider**: Vercel Postgres (Neon)
- **Database**: `neondb`
- **Migration**: `20250609122128_init` ✅ Applied
- **Connection**: ✅ Tested and working
- **CRUD Operations**: ✅ Verified

## 🔧 Quick Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

Copy the environment variables and update with your values:

```bash
# AI Configuration (Required for AI features)
GOOGLE_GEMINI_API_KEY=your_google_gemini_api_key_here
GEMINI_MODEL=gemini-pro
AI_CACHE_TTL=3600

# Redis Configuration (Optional - uses memory cache if not provided)
REDIS_URL=redis://localhost:6379
REDIS_TTL=1800

# Database Optimization
DATABASE_CONNECTION_POOL_SIZE=20
DATABASE_QUERY_TIMEOUT=5000

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
```

### 3. Database Setup ✅ **COMPLETED**

```bash
# Generate Prisma client ✅ DONE
npx prisma generate

# Run database migrations ✅ DONE (Migration: 20250609122128_init)
npx prisma migrate dev

# (Optional) Seed the database
npx prisma db seed
```

**Note**: Database is already configured and connected to Vercel Postgres (Neon). All migrations have been applied successfully.

### 4. Apply Database Indexes

```bash
# Run the Phase 1 test suite to validate setup
npx tsx scripts/test-phase1-features.ts
```

## 🧪 Testing Phase 1 Features

### Run Comprehensive Tests

```bash
npx tsx scripts/test-phase1-features.ts
```

This will test:
- ✅ Database schema and models
- ✅ Database optimization service
- ✅ Cache service functionality
- ✅ Learning path operations
- ✅ API endpoint structure
- ✅ Service files and configuration

### Expected Output

```
🎉 All Phase 1 core features are successfully implemented!

📊 Test Summary:
✅ Passed: 23
❌ Failed: 0
⏭️ Skipped: 0
```

## 🤖 Testing AI Features

### 1. Get Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `.env` file as `GOOGLE_GEMINI_API_KEY`

### 2. Test AI Endpoints

Start the development server:

```bash
npm run dev
```

#### Resume Analysis

```bash
curl -X POST "http://localhost:3000/api/ai/resume-analysis" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-session-token" \
  -d '{
    "resumeText": "John Doe\nSoftware Engineer\n5 years experience in React and Node.js..."
  }'
```

#### Career Recommendations

```bash
curl -X POST "http://localhost:3000/api/ai/career-recommendations" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-session-token" \
  -d '{
    "currentSkills": ["JavaScript", "React", "Node.js"],
    "preferences": {
      "workEnvironment": "remote",
      "careerStage": "mid"
    }
  }'
```

#### Skills Gap Analysis

```bash
curl -X POST "http://localhost:3000/api/ai/skills-analysis" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-session-token" \
  -d '{
    "currentSkills": ["JavaScript", "React"],
    "targetCareerPath": "Senior Full Stack Developer",
    "experienceLevel": "mid"
  }'
```

## 📚 Testing Learning Management

### 1. Create a Learning Path

```bash
curl -X POST "http://localhost:3000/api/learning-paths" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-admin-session-token" \
  -d '{
    "title": "React Fundamentals",
    "description": "Learn the basics of React development",
    "difficulty": "BEGINNER",
    "estimatedHours": 20,
    "category": "WEB_DEVELOPMENT"
  }'
```

### 2. Enroll in Learning Path

```bash
curl -X POST "http://localhost:3000/api/learning-paths/{path-id}/enroll" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-session-token"
```

### 3. Update Progress

```bash
curl -X PUT "http://localhost:3000/api/learning-paths/{path-id}/steps/{step-id}/progress" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-session-token" \
  -d '{
    "status": "COMPLETED",
    "timeSpent": 45
  }'
```

## 📖 API Documentation

### Access Interactive Documentation

Visit: `http://localhost:3000/api-docs`

This provides:
- 🔍 Interactive API explorer
- 📝 Complete endpoint documentation
- 🧪 Live API testing
- 💻 Code examples in multiple languages

### Download API Specification

- JSON: `http://localhost:3000/api/docs`
- YAML: `http://localhost:3000/api/docs?format=yaml`

## 🔍 Database Optimization

### Monitor Database Performance

```bash
curl "http://localhost:3000/api/admin/database" \
  -H "Cookie: next-auth.session-token=your-admin-session-token"
```

### Apply Performance Indexes

```bash
curl -X POST "http://localhost:3000/api/admin/database" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-admin-session-token" \
  -d '{"action": "apply_indexes"}'
```

### Run Database Optimization

```bash
curl -X POST "http://localhost:3000/api/admin/database" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-admin-session-token" \
  -d '{"action": "optimize"}'
```

## 🚨 Troubleshooting

### Common Issues

#### 1. AI Features Not Working
- ✅ Check `GOOGLE_GEMINI_API_KEY` is set correctly
- ✅ Verify API key has proper permissions
- ✅ Check network connectivity to Google AI services

#### 2. Database Errors
- ✅ Run `npx prisma generate` after schema changes
- ✅ Run `npx prisma migrate dev` to apply migrations
- ✅ Check database connection string

#### 3. Cache Issues
- ✅ Redis connection (if using Redis)
- ✅ Memory cache fallback should work without Redis
- ✅ Check cache service health: `/api/ai/health`

#### 4. Permission Errors
- ✅ Ensure user is authenticated for protected endpoints
- ✅ Admin endpoints require admin email in environment
- ✅ Check session cookies are being sent

### Debug Commands

```bash
# Check database connection
npx prisma db pull

# Validate schema
npx prisma validate

# Check service health
curl "http://localhost:3000/api/ai/health"

# Run specific tests
npx tsx scripts/test-phase1-features.ts
```

## 📈 Performance Monitoring

### Key Metrics to Monitor

1. **Database Performance**
   - Average query time (target: <50ms)
   - Slow query count
   - Connection pool usage

2. **AI Service Performance**
   - Response time (target: <5s)
   - Cache hit rate
   - Error rate

3. **Learning Management**
   - Enrollment rate
   - Progress completion rate
   - User engagement metrics

### Monitoring Endpoints

- Database stats: `GET /api/admin/database?action=stats`
- AI health: `GET /api/ai/health`
- Cache stats: `GET /api/ai/health?detailed=true`

## 🎯 Next Steps

After successful Phase 1 setup:

1. **Explore AI Features**: Test resume analysis and career recommendations
2. **Create Learning Content**: Add learning paths and resources
3. **Monitor Performance**: Use admin dashboard for optimization
4. **Review Documentation**: Familiarize with API endpoints
5. **Plan Phase 2**: Job market integration and advanced features

## 🆘 Support

If you encounter issues:

1. Check the test suite output: `npx tsx scripts/test-phase1-features.ts`
2. Review the implementation documentation: `PHASE1_IMPLEMENTATION_COMPLETE.md`
3. Check the API documentation: `http://localhost:3000/api-docs`
4. Verify environment configuration matches the requirements

---

**Phase 1 is now ready for development and testing! 🚀**
