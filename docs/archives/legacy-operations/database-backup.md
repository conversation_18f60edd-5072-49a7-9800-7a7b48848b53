# Database Backup and Recovery Procedures

This document outlines the database backup and recovery procedures for the FAAFO Career Platform.

## 📋 Overview

The platform uses **Vercel <PERSON> (Neon)** as the primary database with automated backup procedures to ensure data protection and recovery capabilities.

### ✅ Current Database Configuration
- **Provider**: Vercel <PERSON> (Neon)
- **Database**: `neondb`
- **Host**: `ep-cold-violet-a4fdonpt-pooler.us-east-1.aws.neon.tech`
- **Connection**: Pooled connection with SSL
- **Migration Status**: ✅ Active (Migration: `20250609122128_init`)

## 🔄 Backup Strategy

### Backup Types
- **Full Backups**: Complete database dumps including schema and data
- **Incremental Backups**: Transaction log backups for point-in-time recovery
- **Schema Backups**: Structure-only backups for development environments

### Backup Schedule
- **Daily**: Automated full backups at 2:00 AM UTC
- **Hourly**: Transaction log backups during business hours
- **Weekly**: Full backup verification and testing
- **Monthly**: Long-term archive backup creation

### Retention Policy
- **Daily Backups**: Retained for 30 days
- **Weekly Backups**: Retained for 12 weeks
- **Monthly Backups**: Retained for 12 months
- **Yearly Backups**: Retained for 7 years

## 🛠️ Backup Procedures

### Automated Backup Script

The platform includes an automated backup script located at:
```
faafo-career-platform/scripts/backup_database.sh
```

#### Script Features
- Environment variable configuration
- Timestamped backup files
- Automatic cleanup of old backups
- Error handling and logging
- PostgreSQL custom format dumps

#### Configuration
```bash
# Environment variables required (Vercel Postgres - Neon)
DB_USER="${POSTGRES_USER}"              # neondb_owner
DB_PASSWORD="${POSTGRES_PASSWORD}"      # npg_BqdyWAlV08jN
DB_NAME="${POSTGRES_DB}"                # neondb
DB_HOST="${POSTGRES_HOST}"              # ep-cold-violet-a4fdonpt-pooler.us-east-1.aws.neon.tech
DB_PORT="${POSTGRES_PORT:-5432}"        # 5432
```

### Manual Backup Process

#### 1. Full Database Backup
```bash
# Navigate to project directory
cd /path/to/faafo-career-platform

# Run backup script
./scripts/backup_database.sh
```

#### 2. Schema-Only Backup
```bash
PGPASSWORD="${DB_PASSWORD}" pg_dump \
  -h "${DB_HOST}" \
  -p "${DB_PORT}" \
  -U "${DB_USER}" \
  -d "${DB_NAME}" \
  --schema-only \
  -f "schema_backup_$(date +%Y%m%d).sql"
```

#### 3. Data-Only Backup
```bash
PGPASSWORD="${DB_PASSWORD}" pg_dump \
  -h "${DB_HOST}" \
  -p "${DB_PORT}" \
  -U "${DB_USER}" \
  -d "${DB_NAME}" \
  --data-only \
  -f "data_backup_$(date +%Y%m%d).sql"
```

## 🔧 Recovery Procedures

### Full Database Restore

#### 1. Prepare for Restore
```bash
# Stop application services
sudo systemctl stop faafo-career-platform

# Create new database (if needed)
createdb -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" "${DB_NAME}_restore"
```

#### 2. Restore from Backup
```bash
# Restore from custom format backup
PGPASSWORD="${DB_PASSWORD}" pg_restore \
  -h "${DB_HOST}" \
  -p "${DB_PORT}" \
  -U "${DB_USER}" \
  -d "${DB_NAME}_restore" \
  -v \
  /path/to/backup/faafo_db_backup_YYYYMMDDHHMMSS.sql
```

#### 3. Verify Restore
```bash
# Connect to restored database
psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}_restore"

# Verify table counts and data integrity
\dt
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM assessments;
SELECT COUNT(*) FROM forum_posts;
```

### Point-in-Time Recovery

#### 1. Identify Recovery Point
```bash
# Review transaction logs
ls -la /var/lib/postgresql/data/pg_wal/

# Check backup timestamps
ls -la /path/to/backups/
```

#### 2. Restore Base Backup
```bash
# Restore from most recent backup before target time
pg_restore -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" backup_file.sql
```

#### 3. Apply Transaction Logs
```bash
# Configure recovery settings
echo "restore_command = 'cp /path/to/wal/%f %p'" >> recovery.conf
echo "recovery_target_time = '2024-01-15 14:30:00'" >> recovery.conf
```

## 📊 Backup Monitoring

### Backup Verification

#### 1. Automated Verification
```bash
# Verify backup script (included in backup process)
./scripts/verify_backup.sh /path/to/backup/file.sql
```

#### 2. Manual Verification
```bash
# Test restore to temporary database
createdb temp_verify_db
pg_restore -d temp_verify_db backup_file.sql

# Verify data integrity
psql -d temp_verify_db -c "SELECT COUNT(*) FROM users;"

# Cleanup
dropdb temp_verify_db
```

### Monitoring Alerts
- **Backup Failure**: Immediate notification
- **Backup Size Anomaly**: Alert if backup size varies significantly
- **Missing Backups**: Alert if scheduled backup doesn't complete
- **Storage Space**: Alert when backup storage reaches 80% capacity

## 🔒 Security Considerations

### Backup Security
- **Encryption**: All backups encrypted at rest
- **Access Control**: Restricted access to backup files
- **Network Security**: Secure transfer protocols for remote backups
- **Audit Logging**: All backup operations logged

### Data Protection
- **Personal Data**: Ensure GDPR compliance in backups
- **Sensitive Information**: Encrypt sensitive data fields
- **Access Logs**: Monitor backup file access
- **Retention Compliance**: Follow data retention policies

## 📍 Backup Locations

### Primary Backup Storage
- **Local**: `/Users/<USER>/faafo/faafo-career-platform/backups/`
- **Cloud**: Encrypted cloud storage for off-site backups
- **Archive**: Long-term storage for compliance backups

### Backup File Naming Convention
```
faafo_db_backup_YYYYMMDDHHMMSS.sql
```

Example: `faafo_db_backup_20240115143000.sql`

## 🚨 Emergency Procedures

### Disaster Recovery
1. **Assess Damage**: Determine extent of data loss
2. **Identify Recovery Point**: Choose appropriate backup
3. **Prepare Environment**: Set up recovery infrastructure
4. **Restore Data**: Execute recovery procedures
5. **Verify Integrity**: Validate restored data
6. **Resume Operations**: Restart application services

### Contact Information
- **Database Administrator**: [Contact details]
- **System Administrator**: [Contact details]
- **Emergency Escalation**: [Contact details]

## 📝 Maintenance Tasks

### Weekly Tasks
- Verify backup completion
- Test backup file integrity
- Review backup logs
- Monitor storage usage

### Monthly Tasks
- Test full restore procedure
- Review retention policies
- Update backup documentation
- Audit backup security

---

[← Back to Operations Documentation](./README.md) | [← Back to Main Documentation](../README.md)
