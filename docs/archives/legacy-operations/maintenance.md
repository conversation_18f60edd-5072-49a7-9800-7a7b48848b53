# Maintenance Procedures

This document outlines regular maintenance tasks and procedures for the FAAFO Career Platform to ensure optimal performance, security, and reliability.

## 📋 Overview

Regular maintenance is essential for system health, security, and performance. This document provides schedules and procedures for all maintenance activities.

## 🕐 Maintenance Schedule

### Daily Tasks (Automated)
- **System Health Checks**: Monitor system status and alerts
- **Log Review**: Check error logs and system logs
- **Backup Verification**: Confirm daily backups completed successfully
- **Performance Monitoring**: Review key performance metrics
- **Security Monitoring**: Check for security alerts and anomalies

### Weekly Tasks
- **Database Maintenance**: Index optimization and statistics updates
- **Log Rotation**: Archive and clean old log files
- **Security Updates**: Review and apply security patches
- **Performance Analysis**: Analyze weekly performance trends
- **Backup Testing**: Verify backup integrity and restore procedures

### Monthly Tasks
- **Dependency Updates**: Update npm packages and dependencies
- **Security Audit**: Comprehensive security review
- **Performance Optimization**: Database and application tuning
- **Capacity Planning**: Review resource usage and scaling needs
- **Documentation Review**: Update maintenance documentation

### Quarterly Tasks
- **System Upgrade**: Major version updates and migrations
- **Disaster Recovery Testing**: Full DR procedure testing
- **Security Penetration Testing**: External security assessment
- **Performance Benchmarking**: Comprehensive performance evaluation
- **Business Continuity Review**: Update BC/DR plans

## 🔧 Daily Maintenance Procedures

### System Health Monitoring
```bash
# Check system status
systemctl status faafo-career-platform
systemctl status postgresql
systemctl status redis

# Review system resources
df -h
free -m
top -n 1

# Check application logs
tail -f /var/log/faafo/application.log
tail -f /var/log/faafo/error.log
```

### Database Health Check
```sql
-- Check database connections
SELECT count(*) FROM pg_stat_activity;

-- Check database size
SELECT pg_size_pretty(pg_database_size('faafo_career'));

-- Check for long-running queries
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';

-- Check table sizes
SELECT schemaname,tablename,attname,n_distinct,correlation 
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY n_distinct DESC;
```

### Performance Metrics Review
```bash
# Application response times
curl -w "@curl-format.txt" -o /dev/null -s https://faafo-career.com/api/health

# Database performance
psql -d faafo_career -c "SELECT * FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;"

# System metrics
iostat -x 1 5
vmstat 1 5
```

## 📊 Weekly Maintenance Procedures

### Database Optimization
```sql
-- Update table statistics
ANALYZE;

-- Reindex tables if needed
REINDEX DATABASE faafo_career;

-- Check for bloated tables
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE schemaname = 'public';

-- Vacuum tables
VACUUM ANALYZE;
```

### Log Management
```bash
# Rotate application logs
logrotate /etc/logrotate.d/faafo-career-platform

# Archive old logs
find /var/log/faafo -name "*.log" -mtime +30 -exec gzip {} \;

# Clean up old archived logs
find /var/log/faafo -name "*.gz" -mtime +90 -delete

# Review log sizes
du -sh /var/log/faafo/*
```

### Security Updates
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Node.js dependencies
npm audit
npm audit fix

# Check for security vulnerabilities
npm audit --audit-level high

# Update Docker images (if using containers)
docker pull node:18-alpine
docker pull postgres:14
```

## 🔄 Monthly Maintenance Procedures

### Dependency Management
```bash
# Check for outdated packages
npm outdated

# Update dependencies
npm update

# Check for security vulnerabilities
npm audit

# Update major versions (with testing)
npm install package@latest

# Test after updates
npm test
npm run test:integration
```

### Performance Optimization
```sql
-- Database performance tuning
-- Check slow queries
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 20;

-- Optimize indexes
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE schemaname = 'public' 
AND n_distinct > 100;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
FROM pg_stat_user_indexes 
ORDER BY idx_scan ASC;
```

### Capacity Planning
```bash
# Check disk usage trends
df -h
du -sh /var/lib/postgresql/data
du -sh /var/log/faafo

# Monitor database growth
psql -d faafo_career -c "
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
"

# Check memory usage trends
free -m
cat /proc/meminfo
```

## 🛡️ Security Maintenance

### Security Monitoring
```bash
# Check failed login attempts
grep "Failed password" /var/log/auth.log | tail -20

# Review application security logs
grep -i "security\|auth\|login" /var/log/faafo/application.log

# Check for suspicious network activity
netstat -tulpn | grep LISTEN
ss -tulpn | grep LISTEN
```

### SSL Certificate Management
```bash
# Check certificate expiration
openssl x509 -in /etc/ssl/certs/faafo-career.crt -text -noout | grep "Not After"

# Renew Let's Encrypt certificates (if applicable)
certbot renew --dry-run
certbot renew

# Verify certificate chain
openssl s_client -connect faafo-career.com:443 -servername faafo-career.com
```

### Access Control Review
```bash
# Review user accounts
cat /etc/passwd | grep -v nologin

# Check sudo access
grep -r "sudo" /etc/group

# Review SSH keys
ls -la ~/.ssh/authorized_keys
```

## 📈 Performance Monitoring

### Application Performance
```bash
# Monitor response times
curl -w "@curl-format.txt" -o /dev/null -s https://faafo-career.com/

# Check application metrics
curl https://faafo-career.com/api/metrics

# Monitor error rates
grep -c "ERROR" /var/log/faafo/application.log
```

### Database Performance
```sql
-- Monitor connection pool
SELECT count(*) as connections, state 
FROM pg_stat_activity 
GROUP BY state;

-- Check cache hit ratio
SELECT 
    sum(heap_blks_read) as heap_read,
    sum(heap_blks_hit)  as heap_hit,
    (sum(heap_blks_hit) - sum(heap_blks_read)) / sum(heap_blks_hit) as ratio
FROM pg_statio_user_tables;

-- Monitor lock waits
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

## 🚨 Emergency Procedures

### System Recovery
```bash
# Restart application services
sudo systemctl restart faafo-career-platform

# Restart database
sudo systemctl restart postgresql

# Clear application cache
redis-cli FLUSHALL

# Check system status
systemctl status faafo-career-platform
systemctl status postgresql
systemctl status redis
```

### Database Recovery
```bash
# Check database integrity
psql -d faafo_career -c "SELECT pg_database_size('faafo_career');"

# Restore from backup (if needed)
pg_restore -d faafo_career /path/to/backup/file.sql

# Verify data integrity
psql -d faafo_career -c "SELECT COUNT(*) FROM users;"
```

## 📝 Maintenance Logging

### Log Maintenance Activities
```bash
# Create maintenance log entry
echo "$(date): Weekly maintenance completed - database optimized, logs rotated" >> /var/log/faafo/maintenance.log

# Review maintenance history
tail -50 /var/log/faafo/maintenance.log
```

### Maintenance Reports
- **Weekly Reports**: System health summary
- **Monthly Reports**: Performance trends and capacity planning
- **Quarterly Reports**: Comprehensive system review
- **Annual Reports**: Strategic planning and major upgrades

---

[← Back to Operations Documentation](./README.md) | [← Back to Main Documentation](../README.md)
