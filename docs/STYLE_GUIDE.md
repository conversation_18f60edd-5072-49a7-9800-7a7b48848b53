# Documentation Style Guide
## Metadata Schema

### Required Fields (All Documents)
```yaml
---
title: "Human-readable title"
doc_type: "atomic-procedure|atomic-concept|workflow|reference"
category: "setup|commands|concepts|procedures"
tags: ["array", "of", "searchable", "keywords"]
owner: "@team-handle"  # e.g., "@backend-team"
last_validated: "YYYY-MM-DD"
---
```

### Optional Fields
```yaml
dependencies: ["prerequisite-atom-ids"]
estimated_time: "5 minutes"
complexity: "beginner|intermediate|advanced"
related_concepts: ["related-atom-ids"]
used_in: []  # Auto-generated, do not edit manually
```

## Atomization Principles

### Granularity Rule
**One atom = One self-contained procedure, concept, or configuration**

### Reusability Test
- If used in 3+ workflows → Make it an atom
- If it's a high-risk, standalone procedure → Make it an atom (even if used once)

### Independence Test
Can this be understood without external context?

## File Structure
```
docs/
├── README.md                    # Master index
├── STYLE_GUIDE.md              # This file
├── atoms/                       # Atomic content blocks
│   ├── setup/                   # Environment, database, dependencies
│   ├── commands/                # CLI commands and scripts
│   ├── concepts/                # Architecture, patterns, principles
│   └── procedures/              # Step-by-step processes
├── workflows/                   # Composed workflow guides
├── reference/                   # Complete technical specs
└── archives/                    # Historical documents
```

## Composition Rules

### Workflow Structure
1. **Overview** - What this workflow accomplishes
2. **Prerequisites** - What you need before starting
3. **Steps** - Detailed procedures (via transclusion)
4. **Verification** - How to confirm success
5. **Troubleshooting** - Common issues and solutions

### Include Syntax
```markdown
{% include "atoms/setup/environment.md" %}
```

### Cross-Reference Format
```markdown
[Environment Setup](atoms/setup/environment.md#prerequisites)
```

## Writing Standards

### Voice & Tone
- **Clear and direct**: Use active voice
- **Consistent terminology**: Use the glossary
- **Actionable**: Every procedure should be executable

### Formatting
- **H1**: Document title only
- **H2**: Major sections
- **H3**: Subsections
- **Code blocks**: Always specify language
- **Lists**: Use for steps and options

## Validation Rules

### Metadata Validation
- All required fields must be present
- `owner` must be a valid GitHub team handle
- `last_validated` must be within 90 days for critical procedures
- `tags` must be lowercase, hyphen-separated

### Content Validation
- All `{% include %}` paths must exist
- All internal links must resolve
- No circular dependencies in includes
- Atoms should be < 500 lines (warning threshold)

## Ownership & Maintenance

### Team Responsibilities
- **@backend-team**: Database, API, server configuration atoms
- **@frontend-team**: UI components, client-side procedures
- **@devops-team**: Deployment, infrastructure, monitoring
- **@qa-team**: Testing procedures and validation

### Validation Schedule
- **Critical procedures**: Validate every 30 days
- **Standard procedures**: Validate every 90 days
- **Reference material**: Validate every 180 days

### Update Process
1. Edit the atomic content file
2. Update `last_validated` date
3. Run validation pipeline
4. Submit PR with clear change description
