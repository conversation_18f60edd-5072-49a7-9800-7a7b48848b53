{"files.defaultLanguage": "markdown", "markdown.suggest.paths.enabled": true, "markdown.suggest.paths.includeWorkspaceHeaderCompletions": "onDoubleHash", "files.associations": {"*.md": "markdown"}, "explorer.fileNesting.patterns": {"*.md": "${capture}.backup.md"}, "files.exclude": {"**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/coverage": true, "**/.next": true}, "search.exclude": {"**/node_modules": true, "**/coverage": true, "**/.next": true, "**/backups": true}}