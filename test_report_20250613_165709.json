{"timestamp": "2025-06-13T16:57:09.640855", "summary": {"passed": 14, "failed": 4, "warnings": 0, "errors": 0, "total_execution_time": 19.979979753494263}, "results": [{"test_name": "page_structure", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.04087090492248535, "recommendations": [], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.025424957275390625, "recommendations": [], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "PASSED", "severity": "LOW", "details": "No forms found", "execution_time": 0.0014369487762451172, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.03448033332824707, "recommendations": ["Consider adding breadcrumb navigation for deep site structures", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5852069854736328, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.006720066070556641, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.1477680206298828, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.017010927200317383, "recommendations": ["Consider adding structured data for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.0034208297729492188, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.13678574562072754, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 5.556596994400024, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "PASSED", "severity": "LOW", "details": "Tested 0 malicious inputs | Issues: 0", "execution_time": 0.01866292953491211, "recommendations": [], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 3.450136184692383, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 2.6781511306762695, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 2.645458936691284, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.39229679107666, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "PASSED", "severity": "LOW", "details": "Tested 3 error handling cases | Issues: 0", "execution_time": 0.05015826225280762, "recommendations": [], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 4 | Vulnerabilities: 3 | Recommendations: 10 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Inline scripts detected - potential XSS risk", "execution_time": 0.****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test signup with duplicate email", "Test email validation", "Test password strength requirements", "Test input sanitization", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250613_165709.png"}]}