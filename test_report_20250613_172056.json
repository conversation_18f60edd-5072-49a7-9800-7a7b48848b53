{"timestamp": "2025-06-13T17:20:56.477374", "summary": {"passed": 10, "failed": 8, "warnings": 0, "errors": 0, "total_execution_time": 366.78269624710083}, "results": [{"test_name": "page_structure", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.056819915771484375, "recommendations": [], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "1 form inputs without proper labels", "execution_time": 0.0341038703918457, "recommendations": ["Associate labels with form inputs using 'for' attribute or aria-label"], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "FAILED", "severity": "MEDIUM", "details": "AI-tested 1 forms | Insights: 6 | Issues: 2 | AI: Tested standard case for email; Tested standard case for email; Tested standard case for email", "execution_time": 32.82541608810425, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.022280216217041016, "recommendations": ["Consider adding breadcrumb navigation for deep site structures", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5550360679626465, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.01138615608215332, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.16051793098449707, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.01314401626586914, "recommendations": ["Consider adding structured data for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.002933979034423828, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.12635493278503418, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 161.30044603347778, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "FAILED", "severity": "HIGH", "details": "Tested 35 malicious inputs | Issues: 5", "execution_time": 156.76270508766174, "recommendations": ["Implement robust input validation", "Add length limits to inputs", "Sanitize user inputs", "Use content security policy"], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 3.4792540073394775, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 5 auth edge cases | Issues: 0", "execution_time": 3.5968849658966064, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 6 boundary conditions | Issues: 0", "execution_time": 2.675704002380371, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.9921419620513916, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "FAILED", "severity": "HIGH", "details": "Tested 3 error handling cases | Issues: 3", "execution_time": 0.04863095283508301, "recommendations": ["Implement proper 404 error pages", "Add path traversal protection", "Implement error boundaries", "Add proper error logging"], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 3 | Vulnerabilities: 2 | Recommendations: 10 | Key findings: Usability: No major usability issues detected; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Inline scripts detected - potential XSS risk; Mixed content detected", "execution_time": 0.*****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test signup with duplicate email", "Test email validation", "Test password strength requirements", "Test input sanitization", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250613_172056.png"}]}