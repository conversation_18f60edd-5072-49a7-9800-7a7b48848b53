#!/usr/bin/env python3
"""
Validate documentation metadata according to style guide.
"""

import os
import sys
import yaml
import re
from pathlib import Path
from datetime import datetime, timedelta

# Required fields for all documents
REQUIRED_FIELDS = {
    'title': str,
    'doc_type': ['atomic-procedure', 'atomic-concept', 'workflow', 'reference'],
    'category': ['setup', 'commands', 'concepts', 'procedures'],
    'tags': list,
    'owner': str,
    'last_validated': str
}

# Valid team handles
VALID_TEAMS = [
    '@backend-team',
    '@frontend-team', 
    '@devops-team',
    '@qa-team',
    '@docs-team'
]

def extract_frontmatter(file_path):
    """Extract YAML frontmatter from markdown file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if not content.startswith('---\n'):
            return None, f"No frontmatter found in {file_path}"
        
        # Find the end of frontmatter
        end_marker = content.find('\n---\n', 4)
        if end_marker == -1:
            return None, f"Malformed frontmatter in {file_path}"
        
        frontmatter = content[4:end_marker]
        return yaml.safe_load(frontmatter), None
        
    except Exception as e:
        return None, f"Error reading {file_path}: {str(e)}"

def validate_metadata(metadata, file_path):
    """Validate metadata against schema."""
    errors = []
    warnings = []
    
    # Check required fields
    for field, field_type in REQUIRED_FIELDS.items():
        if field not in metadata:
            errors.append(f"Missing required field: {field}")
            continue
            
        value = metadata[field]
        
        # Type validation
        if isinstance(field_type, list):
            # Enum validation
            if value not in field_type:
                errors.append(f"Invalid {field}: '{value}'. Must be one of: {field_type}")
        elif field_type == str:
            if not isinstance(value, str) or not value.strip():
                errors.append(f"Field {field} must be a non-empty string")
        elif field_type == list:
            if not isinstance(value, list) or len(value) == 0:
                errors.append(f"Field {field} must be a non-empty list")
    
    # Specific validations
    if 'owner' in metadata:
        if not metadata['owner'].startswith('@'):
            errors.append("Owner must start with '@'")
        elif metadata['owner'] not in VALID_TEAMS:
            warnings.append(f"Unknown team: {metadata['owner']}. Valid teams: {VALID_TEAMS}")
    
    if 'last_validated' in metadata:
        try:
            validated_date = datetime.strptime(metadata['last_validated'], '%Y-%m-%d')
            days_old = (datetime.now() - validated_date).days
            
            if days_old > 90:
                warnings.append(f"Content is {days_old} days old. Consider updating.")
            elif days_old > 180:
                errors.append(f"Content is {days_old} days old. Must be updated.")
                
        except ValueError:
            errors.append("last_validated must be in YYYY-MM-DD format")
    
    if 'tags' in metadata:
        for tag in metadata['tags']:
            if not isinstance(tag, str):
                errors.append(f"All tags must be strings, found: {type(tag)}")
            elif not re.match(r'^[a-z0-9-]+$', tag):
                errors.append(f"Tag '{tag}' must be lowercase, alphanumeric with hyphens only")
    
    return errors, warnings

def main():
    """Main validation function."""
    docs_dir = Path('docs')
    total_files = 0
    total_errors = 0
    total_warnings = 0
    
    print("🔍 Validating documentation metadata...")
    
    # Find all markdown files
    for md_file in docs_dir.rglob('*.md'):
        # Skip certain files
        if md_file.name in ['README.md', 'CHANGELOG.md']:
            continue
            
        total_files += 1
        print(f"\n📄 Checking {md_file}")
        
        # Extract and validate metadata
        metadata, error = extract_frontmatter(md_file)
        
        if error:
            print(f"❌ {error}")
            total_errors += 1
            continue
            
        if metadata is None:
            print(f"⚠️  No metadata found (may be acceptable for some files)")
            continue
        
        errors, warnings = validate_metadata(metadata, md_file)
        
        # Report results
        if errors:
            print(f"❌ {len(errors)} error(s):")
            for error in errors:
                print(f"   • {error}")
            total_errors += len(errors)
            
        if warnings:
            print(f"⚠️  {len(warnings)} warning(s):")
            for warning in warnings:
                print(f"   • {warning}")
            total_warnings += len(warnings)
            
        if not errors and not warnings:
            print("✅ Valid metadata")
    
    # Summary
    print(f"\n📊 Validation Summary:")
    print(f"   Files checked: {total_files}")
    print(f"   Errors: {total_errors}")
    print(f"   Warnings: {total_warnings}")
    
    if total_errors > 0:
        print("\n❌ Validation failed. Please fix errors before proceeding.")
        sys.exit(1)
    else:
        print("\n✅ All validations passed!")

if __name__ == '__main__':
    main()
