#!/usr/bin/env python3
"""
Check internal and external links in documentation.
"""

import os
import re
import sys
import requests
from pathlib import Path
from urllib.parse import urljoin, urlparse

def extract_links(file_path):
    """Extract all markdown links from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find markdown links: [text](url)
        links = re.findall(r'\[([^\]]*)\]\(([^)]+)\)', content)
        return links
        
    except Exception as e:
        print(f"❌ Error reading {file_path}: {str(e)}")
        return []

def is_internal_link(url):
    """Check if a link is internal (relative path)."""
    parsed = urlparse(url)
    return not parsed.scheme and not parsed.netloc

def check_internal_link(link_url, source_file):
    """Check if an internal link exists."""
    # Handle anchors
    if '#' in link_url:
        file_part, anchor = link_url.split('#', 1)
    else:
        file_part, anchor = link_url, None
    
    # Resolve relative path
    if file_part:
        source_dir = source_file.parent
        target_path = source_dir / file_part
        
        # Try to resolve the path
        try:
            resolved_path = target_path.resolve()
            return resolved_path.exists()
        except Exception:
            return False
    
    return True  # Anchor-only links are assumed valid

def check_external_link(url, timeout=10):
    """Check if an external link is accessible."""
    try:
        response = requests.head(url, timeout=timeout, allow_redirects=True)
        return response.status_code < 400
    except Exception:
        try:
            # Try GET if HEAD fails
            response = requests.get(url, timeout=timeout, allow_redirects=True)
            return response.status_code < 400
        except Exception:
            return False

def main():
    """Main link checking function."""
    docs_dir = Path('docs')
    total_links = 0
    broken_internal = 0
    broken_external = 0
    
    print("🔍 Checking documentation links...")
    
    # Check all markdown files
    for md_file in docs_dir.rglob('*.md'):
        links = extract_links(md_file)
        
        if links:
            print(f"\n📄 Checking links in {md_file.relative_to(docs_dir)}")
            
            for link_text, link_url in links:
                total_links += 1
                
                if is_internal_link(link_url):
                    # Check internal link
                    if check_internal_link(link_url, md_file):
                        print(f"   ✅ {link_url}")
                    else:
                        print(f"   ❌ {link_url} (internal link broken)")
                        broken_internal += 1
                else:
                    # Check external link
                    if check_external_link(link_url):
                        print(f"   ✅ {link_url}")
                    else:
                        print(f"   ⚠️  {link_url} (external link may be down)")
                        broken_external += 1
    
    # Summary
    print(f"\n📊 Link Check Summary:")
    print(f"   Total links checked: {total_links}")
    print(f"   Broken internal links: {broken_internal}")
    print(f"   Broken external links: {broken_external}")
    
    if broken_internal > 0:
        print(f"\n❌ Found {broken_internal} broken internal links. Please fix these.")
        sys.exit(1)
    elif broken_external > 0:
        print(f"\n⚠️  Found {broken_external} potentially broken external links.")
        print("   External links may be temporarily unavailable.")
    else:
        print("\n✅ All links are working!")

if __name__ == '__main__':
    main()
