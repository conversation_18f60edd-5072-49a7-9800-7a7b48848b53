#!/usr/bin/env python3
"""
Generate usage graph and update used_in fields automatically.
"""

import os
import re
import yaml
from pathlib import Path
from collections import defaultdict

def extract_frontmatter(file_path):
    """Extract YAML frontmatter from markdown file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if not content.startswith('---\n'):
            return {}, content
        
        end_marker = content.find('\n---\n', 4)
        if end_marker == -1:
            return {}, content
        
        frontmatter = content[4:end_marker]
        body = content[end_marker + 5:]
        
        metadata = yaml.safe_load(frontmatter)
        return metadata or {}, body
        
    except Exception as e:
        print(f"❌ Error reading {file_path}: {str(e)}")
        return {}, ""

def update_frontmatter(file_path, updates):
    """Update frontmatter in a markdown file."""
    try:
        metadata, body = extract_frontmatter(file_path)
        
        # Apply updates
        metadata.update(updates)
        
        # Reconstruct file
        new_content = f"---\n{yaml.dump(metadata, default_flow_style=False)}---\n{body}"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating {file_path}: {str(e)}")
        return False

def scan_includes():
    """Scan all files for include statements and build relationship graph."""
    docs_dir = Path('docs')
    relationships = defaultdict(set)
    
    print("🔍 Scanning for include relationships...")
    
    for md_file in docs_dir.rglob('*.md'):
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find include statements
            includes = re.findall(r'{%\s*include\s+["\']([^"\']+)["\']\s*%}', content)
            
            for include_path in includes:
                # Normalize paths
                included_file = docs_dir / include_path
                consumer_file = md_file
                
                if included_file.exists():
                    # Record that included_file is used by consumer_file
                    relationships[str(included_file)].add(consumer_file.name)
                    print(f"   📄 {included_file.relative_to(docs_dir)} ← {consumer_file.relative_to(docs_dir)}")
        
        except Exception as e:
            print(f"❌ Error scanning {md_file}: {str(e)}")
    
    return relationships

def generate_relationship_report(relationships):
    """Generate a markdown report of all relationships."""
    report_path = Path('docs/reference/relationships.md')
    
    content = """---
title: "Documentation Relationships"
doc_type: "reference"
category: "concepts"
tags: ["documentation", "relationships", "dependencies"]
owner: "@docs-team"
last_validated: "2025-01-15"
---

# Documentation Relationships

This file is auto-generated. It shows which atomic content is used by which workflows.

## Usage Graph

"""
    
    # Sort by most-used first
    sorted_relationships = sorted(relationships.items(), key=lambda x: len(x[1]), reverse=True)
    
    for atom_path, consumers in sorted_relationships:
        atom_name = Path(atom_path).name
        content += f"\n### {atom_name}\n"
        content += f"**Path**: `{atom_path}`  \n"
        content += f"**Used by**: {len(consumers)} file(s)\n\n"
        
        for consumer in sorted(consumers):
            content += f"- {consumer}\n"
        
        content += "\n"
    
    # Ensure directory exists
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"📊 Relationship report generated: {report_path}")

def update_used_in_fields(relationships):
    """Update used_in fields in atomic content files."""
    print("🔄 Updating used_in fields...")
    
    updated_count = 0
    
    for atom_path, consumers in relationships.items():
        atom_file = Path(atom_path)
        
        if atom_file.exists():
            # Convert consumers to list for YAML serialization
            consumer_list = sorted(list(consumers))
            
            # Update the used_in field
            if update_frontmatter(atom_file, {'used_in': consumer_list}):
                print(f"   ✅ Updated {atom_file.relative_to(Path('docs'))}")
                updated_count += 1
            else:
                print(f"   ❌ Failed to update {atom_file}")
    
    print(f"📊 Updated {updated_count} files with usage information")

def find_orphaned_atoms():
    """Find atomic content that isn't used anywhere."""
    docs_dir = Path('docs')
    atoms_dir = docs_dir / 'atoms'
    
    if not atoms_dir.exists():
        return []
    
    orphaned = []
    
    for atom_file in atoms_dir.rglob('*.md'):
        metadata, _ = extract_frontmatter(atom_file)
        used_in = metadata.get('used_in', [])
        
        if not used_in:
            orphaned.append(atom_file)
    
    return orphaned

def main():
    """Main function."""
    print("🔗 Generating documentation usage graph...")
    
    # Scan for relationships
    relationships = scan_includes()
    
    # Generate relationship report
    generate_relationship_report(relationships)
    
    # Update used_in fields
    update_used_in_fields(relationships)
    
    # Find orphaned atoms
    orphaned = find_orphaned_atoms()
    if orphaned:
        print(f"\n⚠️  Found {len(orphaned)} orphaned atoms:")
        for atom in orphaned:
            print(f"   📄 {atom.relative_to(Path('docs'))}")
        print("   Consider removing or finding uses for these atoms.")
    
    # Summary
    print(f"\n📊 Usage Graph Summary:")
    print(f"   Total relationships: {sum(len(consumers) for consumers in relationships.values())}")
    print(f"   Atoms with usage: {len(relationships)}")
    print(f"   Orphaned atoms: {len(orphaned)}")
    
    print("\n✅ Usage graph generation complete!")

if __name__ == '__main__':
    main()
