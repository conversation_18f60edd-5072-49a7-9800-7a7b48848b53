#!/usr/bin/env python3
"""
Check atomization rules and suggest improvements.
"""

import os
import sys
import re
from pathlib import Path

def count_procedures_in_file(file_path):
    """Count the number of procedures in a markdown file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count H2 headings that look like procedures
        procedure_patterns = [
            r'^## (?:Step \d+|Setup|Install|Configure|Create|Build|Deploy|Test)',
            r'^## \d+\.',  # Numbered procedures
            r'^## How to',
            r'^## Running',
            r'^## Setting up'
        ]
        
        procedure_count = 0
        for pattern in procedure_patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)
            procedure_count += len(matches)
        
        return procedure_count
        
    except Exception as e:
        print(f"❌ Error reading {file_path}: {str(e)}")
        return 0

def check_file_size(file_path):
    """Check if file is too large."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return len(lines)
    except Exception:
        return 0

def analyze_atomization(file_path):
    """Analyze a file for atomization issues."""
    warnings = []
    suggestions = []
    
    line_count = check_file_size(file_path)
    procedure_count = count_procedures_in_file(file_path)
    
    # Check file size
    if line_count > 500:
        warnings.append(f"File is {line_count} lines long (>500 line threshold)")
        suggestions.append("Consider splitting into smaller, focused files")
    
    # Check procedure count
    if procedure_count > 3:
        warnings.append(f"Contains {procedure_count} procedures (>3 procedure threshold)")
        suggestions.append("Consider extracting procedures into separate atomic files")
    
    # Check if it's in atoms/ but seems like a workflow
    if 'atoms/' in str(file_path) and procedure_count > 1:
        warnings.append("Atomic file contains multiple procedures")
        suggestions.append("Atomic files should contain single procedures or concepts")
    
    # Check if it's a workflow but has no includes
    if 'workflows/' in str(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '{% include' not in content and line_count > 100:
                warnings.append("Workflow file is large but doesn't use includes")
                suggestions.append("Consider breaking content into atoms and using includes")
        except Exception:
            pass
    
    return warnings, suggestions

def main():
    """Main atomization check function."""
    docs_dir = Path('docs')
    total_warnings = 0
    
    print("🔍 Checking atomization rules...")
    
    # Check all markdown files
    for md_file in docs_dir.rglob('*.md'):
        # Skip certain files
        if md_file.name in ['README.md', 'CHANGELOG.md', 'STYLE_GUIDE.md']:
            continue
        
        warnings, suggestions = analyze_atomization(md_file)
        
        if warnings or suggestions:
            print(f"\n📄 {md_file.relative_to(docs_dir)}")
            
            if warnings:
                print("⚠️  Warnings:")
                for warning in warnings:
                    print(f"   • {warning}")
                total_warnings += len(warnings)
            
            if suggestions:
                print("💡 Suggestions:")
                for suggestion in suggestions:
                    print(f"   • {suggestion}")
    
    # Summary
    print(f"\n📊 Atomization Check Summary:")
    print(f"   Total warnings: {total_warnings}")
    
    if total_warnings > 0:
        print("\n⚠️  Consider addressing atomization issues for better maintainability.")
    else:
        print("\n✅ All files follow good atomization practices!")

if __name__ == '__main__':
    main()
