#!/usr/bin/env python3
"""
Build composed documentation by processing include statements.
"""

import os
import re
import yaml
from pathlib import Path
import shutil

def extract_frontmatter(content):
    """Extract YAML frontmatter from content."""
    if not content.startswith('---\n'):
        return {}, content
    
    end_marker = content.find('\n---\n', 4)
    if end_marker == -1:
        return {}, content
    
    frontmatter = content[4:end_marker]
    body = content[end_marker + 5:]
    
    try:
        metadata = yaml.safe_load(frontmatter)
        return metadata or {}, body
    except yaml.YAMLError:
        return {}, content

def process_includes(content, base_path):
    """Process {% include %} statements in content."""
    def replace_include(match):
        include_path = match.group(1)
        full_path = base_path / include_path

        if not full_path.exists():
            error_msg = f"ERROR: Include file not found: {include_path}"
            print(f"❌ {error_msg}")
            raise FileNotFoundError(error_msg)

        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                included_content = f.read()

            # Extract just the body (no frontmatter) from included files
            _, included_body = extract_frontmatter(included_content)

            # Remove the first H1 header to avoid duplication
            lines = included_body.split('\n')
            processed_lines = []
            skip_first_h1 = True

            for line in lines:
                if skip_first_h1 and line.startswith('# '):
                    skip_first_h1 = False
                    continue
                processed_lines.append(line)

            processed_content = '\n'.join(processed_lines)

            # Recursively process includes in the included content
            return process_includes(processed_content, base_path)

        except Exception as e:
            return f"<!-- ERROR: Could not include {include_path}: {str(e)} -->"

    # Replace include statements
    pattern = r'{%\s*include\s+["\']([^"\']+)["\']\s*%}'
    return re.sub(pattern, replace_include, content)

def build_composed_file(source_path, output_path, docs_root):
    """Build a single composed file."""
    try:
        with open(source_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract metadata and body
        metadata, body = extract_frontmatter(content)
        
        # Process includes in the body
        composed_body = process_includes(body, docs_root)
        
        # Reconstruct the file with metadata
        if metadata:
            composed_content = f"---\n{yaml.dump(metadata, default_flow_style=False)}---\n{composed_body}"
        else:
            composed_content = composed_body
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write composed file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(composed_content)
        
        return True
        
    except Exception as e:
        print(f"❌ Error building {source_path}: {str(e)}")
        return False

def main():
    """Main build function."""
    docs_root = Path('docs')
    build_dir = Path('docs_build')
    
    print("🔨 Building composed documentation...")
    
    # Clean and create build directory
    if build_dir.exists():
        shutil.rmtree(build_dir)
    build_dir.mkdir()
    
    # Copy all files to build directory first
    shutil.copytree(docs_root, build_dir, dirs_exist_ok=True)
    
    # Process workflow files (these typically have includes)
    workflows_dir = build_dir / 'workflows'
    if workflows_dir.exists():
        for workflow_file in workflows_dir.glob('*.md'):
            print(f"📄 Processing {workflow_file.relative_to(build_dir)}")
            
            # Read original file
            with open(workflow_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract metadata and body
            metadata, body = extract_frontmatter(content)
            
            # Process includes
            composed_body = process_includes(body, docs_root)
            
            # Write back to build directory
            if metadata:
                composed_content = f"---\n{yaml.dump(metadata, default_flow_style=False)}---\n{composed_body}"
            else:
                composed_content = composed_body
            
            with open(workflow_file, 'w', encoding='utf-8') as f:
                f.write(composed_content)
    
    # Update mkdocs.yml to point to build directory
    mkdocs_config = Path('mkdocs.yml')
    if mkdocs_config.exists():
        with open(mkdocs_config, 'r') as f:
            config_content = f.read()
        
        # Update docs_dir to point to build directory
        if 'docs_dir:' not in config_content:
            config_content = f"docs_dir: {build_dir}\n" + config_content
        else:
            config_content = re.sub(r'docs_dir:.*', f'docs_dir: {build_dir}', config_content)
        
        with open('mkdocs_build.yml', 'w') as f:
            f.write(config_content)
    
    print("✅ Documentation build complete!")
    print(f"   Build directory: {build_dir}")
    print(f"   Use: mkdocs serve -f mkdocs_build.yml")

if __name__ == '__main__':
    main()
