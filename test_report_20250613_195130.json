{"timestamp": "2025-06-13T19:51:30.957204", "summary": {"passed": 9, "failed": 9, "warnings": 0, "errors": 0, "total_execution_time": 16.92860722541809}, "results": [{"test_name": "page_structure", "status": "FAILED", "severity": "MEDIUM", "details": "Missing <header> element; Missing <main> element; Page title missing or too short; Missing meta description; No H1 heading found", "execution_time": 0.0594789981842041, "recommendations": ["Add semantic <header> element", "Add semantic <main> element for primary content", "Add descriptive page title (50-60 characters)", "Add meta description for SEO", "Add exactly one H1 heading per page"], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Insufficient ARIA landmarks", "execution_time": 0.022092819213867188, "recommendations": ["Add ARIA landmarks for better screen reader navigation"], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "PASSED", "severity": "LOW", "details": "No forms found", "execution_time": 0.004336118698120117, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Limited navigation options", "execution_time": 0.03149104118347168, "recommendations": ["Provide adequate navigation options", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5564978122711182, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.004626035690307617, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.02800893783569336, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "FAILED", "severity": "LOW", "details": "Missing page title; Limited internal linking", "execution_time": 0.004683017730712891, "recommendations": ["Add descriptive page title", "Consider adding structured data for better SEO", "Add more internal links for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.002567768096923828, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.16657495498657227, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 4.076685190200806, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "PASSED", "severity": "LOW", "details": "Tested 0 malicious inputs | Issues: 0", "execution_time": 0.018485307693481445, "recommendations": [], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 2.973642110824585, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 2.0920729637145996, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 2.624401330947876, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.127185106277466, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "FAILED", "severity": "MEDIUM", "details": "Tested 3 error handling cases | Issues: 1", "execution_time": 0.04000377655029297, "recommendations": ["Implement proper 404 error pages", "Add path traversal protection", "Implement error boundaries", "Add proper error logging"], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 2 | Vulnerabilities: 1 | Recommendations: 6 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability", "execution_time": 0.*****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250613_195130.png"}]}