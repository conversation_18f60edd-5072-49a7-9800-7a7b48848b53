{"timestamp": "2025-06-12T23:47:53.820877", "summary": {"passed": 16, "failed": 20, "warnings": 0, "errors": 0, "total_execution_time": 74.78897881507874}, "results": [{"test_name": "page_structure", "status": "FAILED", "severity": "MEDIUM", "details": "Missing <header> element; Missing <main> element; Missing meta description", "execution_time": 0.0548398494720459, "recommendations": ["Add semantic <header> element", "Add semantic <main> element for primary content", "Add meta description for SEO"], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Insufficient ARIA landmarks", "execution_time": 0.01035308837890625, "recommendations": ["Add ARIA landmarks for better screen reader navigation"], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "PASSED", "severity": "LOW", "details": "No forms found", "execution_time": 0.0016181468963623047, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "No navigation element found; Limited navigation options", "execution_time": 0.030449867248535156, "recommendations": ["Add semantic navigation element", "Provide adequate navigation options", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5485329627990723, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.009969949722290039, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Missing security headers: x-frame-options, x-content-type-options, strict-transport-security, content-security-policy; 1 external links without rel='noopener'", "execution_time": 0.3234989643096924, "recommendations": ["Implement security headers for better protection", "Add rel='noopener' to external links"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "FAILED", "severity": "LOW", "details": "Limited internal linking", "execution_time": 0.011492013931274414, "recommendations": ["Consider adding structured data for better SEO", "Add more internal links for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.00516200065612793, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 1.5618748664855957, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 12.623044967651367, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "PASSED", "severity": "LOW", "details": "Tested 0 malicious inputs | Issues: 0", "execution_time": 0.022064924240112305, "recommendations": [], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 9.075952053070068, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 8.035944938659668, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 3 boundary conditions | Issues: 0", "execution_time": 2.641134023666382, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.7540981769561768, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "FAILED", "severity": "MEDIUM", "details": "Tested 3 error handling cases | Issues: 1", "execution_time": 0.03946709632873535, "recommendations": ["Implement proper 404 error pages", "Add path traversal protection", "Implement error boundaries", "Add proper error logging"], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 3 | Vulnerabilities: 2 | Recommendations: 3 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Mixed content detected", "execution_time": 0.10444998741149902, "recommendations": ["Test core user flows", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250612_234717.png"}, {"test_name": "page_structure", "status": "FAILED", "severity": "MEDIUM", "details": "Missing <header> element; Missing <main> element; Page title missing or too short; Missing meta description; No H1 heading found", "execution_time": 0.03413987159729004, "recommendations": ["Add semantic <header> element", "Add semantic <main> element for primary content", "Add descriptive page title (50-60 characters)", "Add meta description for SEO", "Add exactly one H1 heading per page"], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "11 form inputs without proper labels; Insufficient ARIA landmarks", "execution_time": 0.045903682708740234, "recommendations": ["Associate labels with form inputs using 'for' attribute or aria-label", "Add ARIA landmarks for better screen reader navigation"], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "FAILED", "severity": "MEDIUM", "details": "AI-tested 1 forms | Insights: 6 | Issues: 3 | AI: Tested standard case for email; Tested standard case for email; Tested standard case for email", "execution_time": 3.3442113399505615, "recommendations": ["Add submit button to form"], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "No navigation element found; Limited navigation options", "execution_time": 0.0071680545806884766, "recommendations": ["Add semantic navigation element", "Provide adequate navigation options", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Missing viewport meta tag; Touch targets too small (< 44px)", "execution_time": 1.5369160175323486, "recommendations": ["Add viewport meta tag for mobile optimization", "Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.00842905044555664, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "MEDIUM", "details": "Missing security headers: x-frame-options, x-content-type-options, strict-transport-security, content-security-policy", "execution_time": 0.17122483253479004, "recommendations": ["Implement security headers for better protection"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "FAILED", "severity": "LOW", "details": "Missing page title; Limited internal linking", "execution_time": 0.009140968322753906, "recommendations": ["Add descriptive page title", "Consider adding structured data for better SEO", "Add more internal links for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.005374908447265625, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.1690690517425537, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 8.034266233444214, "recommendations": [], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "FAILED", "severity": "HIGH", "details": "Tested 55 malicious inputs | Issues: 10", "execution_time": 9.818628787994385, "recommendations": ["Implement robust input validation", "Add length limits to inputs", "Sanitize user inputs", "Use content security policy"], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 3.829390048980713, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 0 auth edge cases | Issues: 0", "execution_time": 2.021545886993408, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 6 boundary conditions | Issues: 0", "execution_time": 2.6371161937713623, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.1644418239593506, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "FAILED", "severity": "MEDIUM", "details": "Tested 3 error handling cases | Issues: 1", "execution_time": 0.017338991165161133, "recommendations": ["Implement proper 404 error pages", "Add path traversal protection", "Implement error boundaries", "Add proper error logging"], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 2 | Vulnerabilities: 1 | Recommendations: 3 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability", "execution_time": 0.08072519302368164, "recommendations": ["Test core user flows", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250612_234753.png"}]}