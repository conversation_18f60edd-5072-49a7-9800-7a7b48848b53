
        <!DOCTYPE html>
        <html>
        <head>
            <title>🤖 Super Testerator - Enhanced Web Testing Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; }
                .ai-status { background: #28a745; color: white; padding: 10px; border-radius: 5px; margin: 10px 0; text-align: center; }
                .summary { display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap; }
                .metric { background: #fff; border: 1px solid #ddd; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .passed { border-left: 6px solid #28a745; }
                .failed { border-left: 6px solid #dc3545; }
                .warning { border-left: 6px solid #ffc107; }
                .error { border-left: 6px solid #6f42c1; }
                .security { border-left: 6px solid #fd7e14; }
                .ai-insights { border-left: 6px solid #20c997; }
                .test-result { margin: 15px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .recommendations { background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px; }
                .critical { background: #ffebee; border-left: 6px solid #f44336; }
                .high { background: #fff3e0; border-left: 6px solid #ff9800; }
                .medium { background: #f3e5f5; border-left: 6px solid #9c27b0; }
                .low { background: #e8f5e8; border-left: 6px solid #4caf50; }
                .security-section { background: #fff5f5; border: 2px solid #ff6b6b; border-radius: 10px; padding: 20px; margin: 20px 0; }
                .ai-section { background: #f0fff4; border: 2px solid #00d4aa; border-radius: 10px; padding: 20px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 Super Testerator Report</h1>
                <h2>Enhanced Web Testing with AI & Security Analysis</h2>
                <p>Generated on: 2025-06-13 18:56:55</p>
                <p>Total Execution Time: 0.00 seconds</p>
            </div>

            <div class="ai-status">
                <h3>🧠 AI Intelligence: 🧠 ENABLED</h3>
                <p>Enhanced with security testing, edge case analysis, and AI-powered insights</p>
            </div>

            <div class="summary">
                <div class="metric passed">
                    <h3>0</h3>
                    <p>✅ Passed</p>
                </div>
                <div class="metric failed">
                    <h3>0</h3>
                    <p>❌ Failed</p>
                </div>
                <div class="metric warning">
                    <h3>0</h3>
                    <p>⚠️ Warnings</p>
                </div>
                <div class="metric error">
                    <h3>1</h3>
                    <p>🚨 Errors</p>
                </div>
                <div class="metric security">
                    <h3>0</h3>
                    <p>🔒 Security Issues</p>
                </div>
                <div class="metric ai-insights">
                    <h3>0</h3>
                    <p>🧠 AI Insights</p>
                </div>
            </div>

            <div class="security-section">
                <h2>🔒 Security Analysis Summary</h2>
                <p><strong>Security Issues Found:</strong> 0</p>
                <p><strong>Security Tests Performed:</strong> Advanced XSS, SQL Injection, Path Traversal, Session Management</p>
                <p><strong>Edge Cases Tested:</strong> Authentication boundaries, malicious inputs, concurrent operations</p>
            </div>

            <div class="ai-section">
                <h2>🧠 AI Analysis Summary</h2>
                <p><strong>AI Status:</strong> 🧠 ENABLED</p>
                <p><strong>AI Insights Generated:</strong> 0</p>
                <p><strong>Analysis Categories:</strong> Usability, Security, Accessibility, Performance, Vulnerabilities</p>
            </div>

            <h2>📊 Severity Distribution</h2>
            <ul>
        <li>CRITICAL: 1 issues</li>
            </ul>
            
            <h2>Detailed Results</h2>
        
            <div class="test-result error critical">
                <h3>Page Load</h3>
                <p><strong>Status:</strong> ERROR</p>
                <p><strong>Severity:</strong> CRITICAL</p>
                <p><strong>Execution Time:</strong> 0.00s</p>
                <p><strong>Details:</strong> Failed to load page: Page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3000/login
Call log:
  - navigating to "http://localhost:3000/login", waiting until "networkidle"
</p>
                
                <div class="recommendations"><h4>Recommendations:</h4><ul><li>Check URL accessibility and network connectivity</li></ul></div>
                
                
            </div>
            
        </body>
        </html>
        